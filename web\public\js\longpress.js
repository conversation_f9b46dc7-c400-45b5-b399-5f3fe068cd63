const LONG_PRESS_THRESHOLD = 800 // 800ms threshold for long press
function setupLongPress(element) {
  let pressTimer

  function handleLongPress(event) {
    const longPressEvent = new CustomEvent('longPress', {
      bubbles: true,
    })
    element.dispatchEvent(longPressEvent)
  }

  element.addEventListener('mousedown', (event) => {
    pressTimer = setTimeout(() => handleLongPress(event), LONG_PRESS_THRESHOLD)
  })

  element.addEventListener('touchstart', (event) => {
    pressTimer = setTimeout(() => handleLongPress(event), LONG_PRESS_THRESHOLD)
  })

  element.addEventListener('mouseup', () => clearTimeout(pressTimer))
  element.addEventListener('mouseleave', () => clearTimeout(pressTimer))
  element.addEventListener('touchend', () => clearTimeout(pressTimer))
  element.addEventListener('touchcancel', () => clearTimeout(pressTimer))
}

export function setupLongPressByTagName(elementName) {
  const elements = document.getElementsByTagName(elementName)
  for (const element of elements) {
    setupLongPress(element)
  }
}

document.addEventListener('DOMContentLoaded', () => {
  const buttons = document.getElementsByTagName('button')
  for (const button of buttons) {
    setupLongPress(button)
  }
})
