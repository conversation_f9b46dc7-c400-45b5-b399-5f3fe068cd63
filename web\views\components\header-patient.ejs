<% if (selectedPatient) { %>
<div class="flex flex-col items-center justify-center w-full rounded-lg bg-darkgray p-2 mx-4">
  <div class="flex flex-row w-full text-white gap-x-2 px-2 py-2">
    <!-- <div class="flex w-10 items-center justify-center">
        <% if (selectedPatient.gender == 'F') { %>
        <i class="fa-solid fa-female text-5xl ml-2 text-[#EC4899]"></i>
        <% } %> <% if (selectedPatient.gender == 'M') { %>
        <i class="fa-solid fa-male text-5xl ml-2 text-[#3B82F6]"></i>
        <% } %>
    </div> -->
    <div class="flex flex-grow">
      <p class="text-5xl"><%= selectedPatient.lastName %> <%= selectedPatient.firstName %></p>
    </div>
    <div class="flex flex-1 items-center justify-end">
      <% if (selectedPatient.statusTag) { %>
        <% const badgeColor = selectedPatient.statusTag === 'before' ? 'bg-green-500' : selectedPatient.statusTag === 'after' ? 'bg-yellow-500' : 'bg-red-500'; %>
        <%- include('./badge-text', {badgeColor:badgeColor, badgeText: 'badge.' + selectedPatient.statusTag}) %>
      <% } %>
    </div>
  </div>

  <div class="flex flex-row w-full items-center px-2 gap-x-2 text-2xl">
    <div class="flex flex-2 text-center">
      <p class="text-darkred font-bold"><%= t('birthDate') %>:&nbsp;</p>
      <p><%= locDate(selectedPatient.birthDate) %></p>
    </div>
    <div class="flex flex-1 text-center">
      <p class="text-darkred font-bold"><%= t('ef') %>:&nbsp;</p>
      <p><%= selectedPatient.ef %></p>
    </div>
    <div class="flex flex-1 text-center">
      <p class="text-darkred font-bold"><%= t('esv') %>:&nbsp;</p>
      <p><%= selectedPatient.esv %></p>
    </div>

    <% if (typeof tooltip !== 'undefined' && tooltip) { %>
    <div class="flex flex-1 items-center justify-center text-center">
      <p class="px-2"><i class="fa-solid fa-warning text-darkred"></i></p>
      <p title="<%= tooltip %>"><%= t('problema') %></p>
    </div>
    <% } %>
  </div>
</div>
<% } %>
