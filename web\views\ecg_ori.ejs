<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
  <head>
    <%- include('components/head') %>
  <link href="/css/ecg.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
      #magnifierCanvas {
        position: absolute;
        pointer-events: none;
        border: 2px solid #333;
        display: none;
      }
    </style>
  </head>
  <body>
    <%- include('components/header') %>
    <main>
    <div id="overlay">
      <div id="loader" class="spinner"></div>
      <p id="loadingText">Caricamento dati ECG in corso...</p>
    </div>
    <div id="offset-panel" class="card shadow-sm" style="position: absolute; top: 780px; left: 10px; width: 940px; z-index: 20">
      <input type="range" class="form-range" min="0" max="0" step="500" id="offsetSlider" value="0" />
    </div>
    <canvas id="ecgCanvas"></canvas>
    </main>
    <script>
      const canvas = document.getElementById('ecgCanvas');
      const overlay = document.getElementById('overlay');
      const offsetSlider = document.getElementById('offsetSlider');
      const magnifierCanvas = document.createElement('canvas');
      magnifierCanvas.id = 'magnifierCanvas';
      magnifierCanvas.width = 100;
      magnifierCanvas.height = 800;
      document.body.appendChild(magnifierCanvas);
      const magnifierCtx = magnifierCanvas.getContext('2d');
      const magnifierRadius = 75; // raggio della lente
    </script>
    <script type="module">
      import { EcgData, EcgDrawer } from './js/ecg-lib.js';

      const drawer = new EcgDrawer(canvas);

      window.onload = async () => {
        overlay.style.display = 'flex';
        offsetSlider.value = 0;
        await new Promise((resolve) => requestAnimationFrame(resolve));
        let readData = await loadEcgFromUrl('/12345A2.csv');
        if (readData) {
          drawer.setEcgData(readData);
          offsetSlider.max = drawer.getMaxDataOffset();
        }
        overlay.style.display = 'none';
        drawer.setCanvasDimensions(1000, 800);
        //drawer.redraw();
      };
      //In caso di resize, ridisegno tutto.
      let resizeTimeout = null;
      window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          //Eventualmente se full size, usare questa:
          drawer.setCanvasDimensions(window.innerWidth, window.innerHeight);
          //oppure solo redraw:
          //drawer.redraw();
        }, 100); // 100ms dopo l'ultimo resize
      });

      offsetSlider.addEventListener('input', function (e) {
        drawer.setOffsetIndex(parseInt(this.value));
      });

      /** Carica da remoto i dati ecg e restituisce un oggetto EcgData.  */
      async function loadEcgFromUrl(url) {
        await new Promise((resolve) => setTimeout(resolve, 500));
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
          const csv = await response.text();
          const ecgData = new EcgData();
          ecgData.fromCsvData(csv);
          return ecgData;
        } catch (error) {
          console.error('Failed to load ECG data:', error);
          return null;
        }
      }

      let zoomTimeout = null;
      // Evento mousemove
      canvas.addEventListener('click', (e) => {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Posizione della lente (centrata sul mouse)
        magnifierCanvas.style.left = `${e.clientX-50}px`;
        magnifierCanvas.style.top = `0px`;
        magnifierCanvas.style.display = 'block';

        // Pulizia
        magnifierCtx.clearRect(0, 0, magnifierCanvas.width, magnifierCanvas.height);

        // Disegna contenuto ingrandito
        // Puoi cambiare il "scale" o la logica qui
        magnifierCtx.save();
        magnifierCtx.beginPath();

        magnifierCtx.drawImage(canvas, e.clientX-25, 0, 50, 800, 0, 0, 100, 800);
        magnifierCtx.restore();

        zoomTimeout = setTimeout(() => {
          magnifierCanvas.style.display = 'none';
        }, 1000*5); 
      });

      mainCanvas.addEventListener('mouseleave', () => {
        magnifierCanvas.style.display = 'none';
      });
    </script>
  </body>
</html>
