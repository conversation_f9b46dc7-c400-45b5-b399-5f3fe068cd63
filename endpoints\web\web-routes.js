import express from "express";
import fs from "fs";
import patientRoutes from "./patients-routes.js";
import { translationMiddleware } from "../../middlewares/i18n.js";
import localsMiddleware from "../../middlewares/locals.js";
import authentication from "../../middlewares/authentication.js";
import logger from "medcommon/logger.js";
import crypto from "crypto";
import NiceUserModel from "../../api/models/nice-user.js";
import oracle from "medcommon/oracle";
//FIXME: Spostare chi usa questi imports..
import path from "path";
import PatientResourceModel from "../../api/models/patient-resource.js";
const packageJson = JSON.parse(fs.readFileSync("./package.json", "utf-8"));
const { version } = packageJson;

const webRoutes = express.Router();

webRoutes.use(translationMiddleware);
webRoutes.use(authentication.authenticationMiddleware);
webRoutes.use(localsMiddleware);

//webRoutes.get('/vtk', (req, res) => res.render('vtk-viewer', { vtuModel: '/eam_model.vtu', plyModel: '/cs_model.ply' }));
//webRoutes.get('/ecg', (req, res) => res.render('ecg-small', { ecgUrl : req.query.file }));

// TODO: remove for production
webRoutes.put("/login", async (req, res) => {
  if (process.env.NODE_ENV === "production") {
    return res.status(404).send();
  }
  const salt = crypto.randomBytes(16).toString("base64");
  const cryptedPW = authentication.encryptPassword(req.body.password, process.env.AUTH_KEY_SECRET, salt);
  const respId = await oracle.dbSingleRow("SELECT S_NICE_USERS.nextval id from dual");
  // GET nextval
  const newUser = new NiceUserModel({
    ID: respId["ID"],
    username: req.body.username,
    firstName: req.body.firstName,
    lastName: req.body.lastName,
    password: cryptedPW,
    salt: salt.toString("base64"),
  });
  const result = await newUser.save();
  //
  res.send(result);
});

webRoutes
  .route("/login")
  .get(async (req, res) => res.render("login", { version }))
  .post(async (req, res) => {
    let errors = [];
    const { username, password } = req.body;
    if (username.length === 0 || password.length === 0) {
      errors.push("login.error");
      return res.render("login", { errors, version });
    }
    try {
      const niceUserModel = new NiceUserModel();
      const result = await niceUserModel.findFree([username.trim()], "USER_NAME = :1", "");
      const user = result[0];
      if (!user) {
        logger.debug("POST /login user not found");
        throw new Error("User not found");
      }
      const cryptedPW = authentication.encryptPassword(password.trim(), process.env.AUTH_KEY_SECRET, user.salt);
      if (user.password !== cryptedPW) {
        logger.debug("POST /login wrong password");
        throw new Error("Wrong password");
      }
      logger.debug("POST /login user:%O", user);
      if (user && user.password === cryptedPW) {
        logger.debug("POST /login user found");
      }
      user.lastLogin = new Date();
      await user.update();
      req.session.userid = user.id;
      req.session.username = user.username;
      req.session.save();
    } catch (error) {
      res.locals.username = username;
      errors.push("login.error");
    }
    //#endregion
    return errors.length === 0 ? res.redirect("/post-login-warning") : res.render("login", { errors, version });
  });

webRoutes.get("/post-login-warning", (req, res) => res.render("post-login-warning", { version }));

webRoutes.get("/logout", async (req, res) => {
  //req.session.userid = undefined;
  //req.session.username = undefined;
  //req.session.userInfo = undefined;
  req.session.destroy((err) => {
    return res.redirect("/");
  });
});
//La chiamata viene fatta tramite axios lato node js al metodo oauth il quale
//fà il redirect, in questo modo riesco ad intercettarlo e gestirlo da axios.
webRoutes.get("/callback", (req, res) => {
  let { code } = req.query;
  req.session.code = code;
  res.send({ code: code });
});

webRoutes.get("/viewer", (req, res) =>
  res.render("activation-map", { vtuModel: req.query.vtu, plyModel: req.query.ply, patientId: req.query.patientId, mapId: req.query.mapId })
);

webRoutes.get("/iframes", (req, res) => res.render("iframes"));

webRoutes.use("/patients", patientRoutes);

webRoutes
  .route("/ecg-acquisition")
  .get(async (req, res) => {
    const payload = req.body;
    const redirectUrl = req.query.redirectUrl;
    return res.render("ecg-acquisition", { payload, redirectUrl });
  })
  .post(async function (req, res) {
    const ecgId = req.body.uid;
    const resText = req.body.resourceText;
    const projectRoot = process.cwd();
    const fullFileName = path.join(projectRoot, "storage", "ecgs", ecgId);
    const newPath = res.locals.getStorageUrl(req.session.selectedPatient.id, 0, 1, ecgId);
    const newFullFileName = path.join(projectRoot, newPath);
    let result = false;
    try {
      fs.renameSync(fullFileName, newFullFileName);
      result = true;
    } catch (err) {
      logger.error(err);
    }
    const resId = await oracle.dbSingleRow("SELECT S_RESOURCES_ID.nextval id from dual");
    const resData = {
      id: resId['ID'],
      idPatient: req.session.selectedPatient.id,
      idUserCreate: 1,
      idUserUpdate: 1,
      uuid: ecgId,
      idResourceType: 1,
      resourceDate: new Date(),
      resourceTag: req.session.selectedPatient.statusTag,
      resourceNotes: resText,
      resourceOrder: resId['ID'],
    };
    if (result) {
      const esito = await new PatientResourceModel(resData).save();
      return res.redirect("/patients/" + req.session.selectedPatient.id);
    } else {
      res.end();
    }
  });
webRoutes.get("/todo", (req, res) => {
  return res.render("todo");
});

const buttons = JSON.parse(fs.readFileSync("./web/public/json/home.json", "utf-8"));

webRoutes.get("/", (req, res) => {
  req.session.selectedPatient = {};
  res.locals.selectedPatient = req.session.selectedPatient;
  return res.render("home", { buttons, invisibleBreadcrumb: true });
  //return res.render('index');
});

//In caso di route non trovata si arriva qua..
webRoutes.use((req, res) => {
  const errorMsg = req.query["noAuth"] ? "notFound.noPermission" : "notFound.message";
  return res.render("404", { errorMsg });
});
export default webRoutes;
