const config = require("medcommon/config");
const logger = require("medcommon/logger");
const DbAdapter = require("./db-adapter");
const oracledb = require("oracledb");
oracledb.fetchAsString = [oracledb.CLOB];
try {
  oracledb.initOracleClient({ libDir: config.instantClient });
  logger.info("Oracle Instant Client inizializzato: %s", config.instantClient);
} catch (error) {
  logger.info("Oracle Instant Client già inizializzato");
}

class DbOracle extends DbAdapter {
  constructor() {
    super();
  }

  async init() {
  }

  /**
 * Recupera una connessione Oracle dal pool di connessioni.
 * @returns Oggetto connessione.
 */
  async getDBConnection() {
    let conn = await oracledb.getConnection(config.oracleConfig);
    logger.debug("DbOracle.getDbConnection -> from connPool: %O", conn.processID);
    return conn;
  }

  async dbExecute(query, params = [], options = { outFormat: oracledb.OUT_FORMAT_OBJECT, autoCommit: true }) {
    logger.debug("DbOracle.dbExecute(%s,%O,%O)", query, params, options);
    let toRet = this.defDbExecuteResponse();
    toRet.dbOperation = this.detectQueryOperation(query);
    let conn;
    try {
      conn = await this.getDBConnection();
      const result = await conn.execute(query, params, options);
      toRet = this.fillResponseFromResult(result);
    } catch (error) {
      logger.error("ERROR on DbOracle.dbExecute:%O", error);
      toRet.success = false;
      toRet.error = error.message;
    } finally {
      try {
        if (conn) {
          await conn.close();
        }
      } catch (err) {
        logger.error("ERROR on DbOracle.conn.end(). %s", err.message);
      }
    }
    return toRet;
  }

  fillResponseFromResult(result) {
    let toRet = this.defDbExecuteResponse();
    logger.info("DbOracle.dbExecute result:%O", result);
    if (result) {
      const resJson = {};
      if (result.rows) {
        resJson.rows = result.rows;
        resJson.rowsAffected = result.length;
      }
      if (result.rowsAffected && result.rowsAffected > 0) {
        resJson.rowsAffected = result.rowsAffected;
      }
      resJson.success = true;
      toRet = { ...toRet, ...resJson };
    }
    return toRet;
  }

  async getNewTransaction() {
    const conn = await this.getDBConnection();
    // Inizia la transazione
    //await conn.execute('BEGIN');
    return conn;
  }
  async transactExecute(conn, query, params) {
    logger.debug("DbOracle.transactExecute", query, params);
    let toRet = this.defDbExecuteResponse();
    toRet.dbOperation = this.detectQueryOperation(query);
    if (conn) {
      try {
        const result = await conn.execute(query, params, { outFormat: oracledb.OUT_FORMAT_OBJECT, autoCommit: false });
        //logger.info("DbMaria.transactExecute result:%O", result);
        toRet = this.fillResponseFromResult(result);
      } catch (error) {
        logger.error("ERROR on DbOracle.transactExecute:%O", error);
        toRet.success = false;
        toRet.error = error.message;
      }
    }
    return toRet;
  }

  async commit(conn) {
    if (conn) await conn.commit();
  }

  async rollback(conn) {
    if (conn) await conn.rollback();
  }

  /** Rilascia nel modo nativo la connessione.
   * @param {*} conn 
   */
  async release(conn) {
    if (conn) conn.close();
  }

  //#region Formattazione specifica queries del client db
  formatTableName(schema = {}) {
    return `"${schema.tableName}"`;
  }
  formatAllFieldsNames(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    return allFields.map((key) => `"${key}"`).join(',')
  }
  //Caso insert into table (....) values(§,§,§,§)
  formatAllFieldsParams(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    const allStr = allFields.map((key) => `:${key}`).join(',');
    return allStr;
  }
  //Caso update table set (A=§, B=§) where .... 
  formatUpdateParams(schema = {}) {
    const updStr = schema.fields.map((key) => `"${key}"=:${key}`).join(',');
    return updStr;
  }
  //Caso della clausola WHERE => A=§ AND B=§ AND C=§
  formatWhereParams(schema = {}, startIndex = 0) {
    const keys = schema.keys || [];
    const keysStr = schema.keys.map((key) => `"${key}"=:${key}`).join(" AND ");
    return keysStr;
  }
  formatDbParam = (name, index) => `:${name}`;
  formatDbField = (name) => `"${name}"`;
  dbParamRegex = () => /:\w+/g;
  //#endregion
}
module.exports = DbOracle;
