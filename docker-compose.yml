---
# file: docker-compose.yaml
services:
  app:
    container_name: nice-backend
    build:
      context: .
      dockerfile: ./Dockerfile
    ports:
    - ${PORT}:${PORT}
    - ${WEB_SOCKET_PORT}:${WEB_SOCKET_PORT}
    healthcheck:
      test: curl -I --fail --noproxy '*' http://localhost:${PORT}${VIRTUALDIRPATH:-/}health-check || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
    restart: ${DOCKER_RESTART:-no}
    volumes:
    - ./logs:${LOGPATH}
    #- ${LOGPATH}:${LOGPATH} <- questo su windows NON VA!
    - ${STORAGE_PATH}:/app/storage
    - .env:/app/.env
    environment:
    - IP_ADDRESS=0.0.0.0
    - NODE_ENV=production
...
