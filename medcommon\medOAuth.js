const dbManager = require("./dbManager");
const logger = require("./logger");

const oAuthUserQuery = `SELECT 
  u.username username
  , oauth.client_id tod
  , u.id medID
  , u.SYS_M isAdmin 
  , u.SYS_U isUserAdmin
FROM
  oauth2.access_tokens oauth, utenti u 
WHERE 
  oauth.access_token = :token AND oauth.USER_ID = u.id AND oauth.EXPIRES > sysdate`;

function getTokenFromHeader(req) {
  var auth = req.get("Authorization");
  return auth ? auth.split(" ")[1] : " ";
}
/**
 *  Se il token è stato trovato e non è scaduto ritorna la riga letta.
 */
async function fillUserFromToken(token) {
  let toRet = false;
  try {
    const result = await dbManager.dbExecute(oAuthUserQuery, [token], {});
    if (result && result.rows[0]) {
      toRet = {
        username: result.rows[0]["USERNAME"],
        id: result.rows[0]["MEDID"],
        client_id: result.rows[0]["TOD"],
        isAdmin: result.rows[0]["ISADMIN"],
        isUserAdmin: result.rows[0]["ISUSERADMIN"],
      };
    }
  } catch (error) {
    logger.error(
      "/medcommon/medOAuth/fillUserFromToken(${%s}) => err:%O",
      token,
      error
    );
    //loggo l'errore e basta, no rethow qui.
  }
  return toRet;
}
/** ANCHOR CHECKAUTENTICAZIONE
 * Imposta sulla request oggetto infoUtente (req.infoUtente) se l'access_token è ok!
 * @param {*} req la richiesta http
 * @param {*} res la risposta http (non usata)
 * @param {*} next la funzione successiva da chiamare se il token risulta valido e l'utente è un admin.
 */
async function validateUserFromToken(req, res, next) {
  let token = getTokenFromHeader(req);
  let datiUtente = await fillUserFromToken(token);
  if (datiUtente) {
    logger.debug(
      "/medcommon/medOAuth/validateTokenUser(%s) => %O",
      token,
      datiUtente
    );
    req.infoUtente = datiUtente;
    next();
  } else {
    logger.warn("401 - Authentication failed.");
    next({
      status: 401,
      message: "Authentication failed, token empty or expired.",
    });
  }
}
/**
 * SystemAdmin => SYS_M == 'S'
 * @param {*} req la richiesta http
 * @param {*} res la risposta http (non usata)
 * @param {*} next la funzione successiva da chiamare se il token è valido e l'utente è un admin.
 */
async function validateSystemAdminFromToken(req, res, next) {
  let token = getTokenFromHeader(req);
  let datiUtente = await fillUserFromToken(token);
  if (datiUtente) {
    if (datiUtente.isAdmin === "S") {
      logger.debug(
        "/medcommon/medOAuth/validateAdminTokenUser(%s) => %O",
        token,
        datiUtente
      );
      req.infoUtente = datiUtente;
      next();
    } else {
      logger.warn("403 - Authorization failed.");
      next({
        status: 403,
        message: "Authorization failed, user is not an Administrator",
      });
    }
  } else {
    logger.warn("401 - Authentication failed.");
    next({
      status: 401,
      message: "Authentication failed, token empty or expired.",
    });
  }
}
/**
 * UserAdmin => SYS_U == 'S'
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
async function validateUserAdminFromToken(req, res, next) {
  let token = getTokenFromHeader(req);
  let datiUtente = await fillUserFromToken(token);
  if (datiUtente) {
    if (datiUtente.isAdmin === "S") {
      logger.debug(
        "/medcommon/medOAuth/validateAdminTokenUser(%s) => %O",
        token,
        datiUtente
      );
      req.infoUtente = datiUtente;
      next();
    } else {
      logger.warn("403 - Authorization failed.");
      next({
        status: 403,
        message: "Authorization failed, user is not an Administrator",
      });
    }
  } else {
    logger.warn("401 - Authentication failed.");
    next({
      status: 401,
      message: "Authentication failed, token empty or expired.",
    });
  }
}

module.exports = {
  validateUserFromToken,
  validateTokenUser: validateUserFromToken,
  validateTokenAdminUser: validateUserAdminFromToken,
  validateSystemAdminFromToken,
  validateUserAdminFromToken,
  fillUserFromToken,
  getTokenFromHeader
};
