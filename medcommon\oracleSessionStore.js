"use strict";

const oracle = require("./oracle");
const logger = require("./logger");

const DEFAULT_PRUNE_INTERVAL_IN_SECONDS = 60 * 15;
//const ONE_DAY = 86400;

let elkData = {
  "log.origin.file.name": "medcommon/oracleSessionStore.js",
};

/** @typedef {*} ExpressSession */
/** @typedef {*} ExpressSessionStore */
/** @typedef {(error: Error|null) => void} SimpleErrorCallback */
/** @typedef {{ cookie: { maxAge?: number, expire?: number, [property: string]: any }, [property: string]: any }} SessionObject */
/** @typedef {(delay: number) => number} PGStorePruneDelayRandomizer */
/** @typedef {Object<string, any>} PGStoreQueryResult */
/** @typedef {(error: Error|null, firstRow?: PGStoreQueryResult) => void} PGStoreQueryCallback */

/**
 * @typedef PGStoreOptions
 * @property {string} [schemaName]
 * @property {string} [tableName]
 * @property {boolean} [createTableIfMissing]
 * @property {number} [ttl]
 * @property {boolean} [disableTouch]
 * @property {typeof console.error} [errorLog]
 * @property {import('pg').Pool} [pool]
 * @property {*} [pgPromise]
 * @property {string} [conString]
 * @property {*} [conObject]
 * @property {false|number} [pruneSessionInterval]
 * @property {false|PGStorePruneDelayRandomizer} [pruneSessionRandomizedInterval]
 */

/**
 * @param {ExpressSession} session
 * @returns {ExpressSessionStore}
 */
module.exports = function (session) {
  /** @type {ExpressSessionStore} */
  const Store = session.Store || session.session.Store;

  class OracleSessionStore extends Store {
    /** @type {boolean} */
    #createTableIfMissing;
    /** @type {boolean} */
    #disableTouch;
    /** @type {false|number} */
    #pruneSessionInterval;
    /** @type {PGStorePruneDelayRandomizer|undefined} */
    #pruneSessionRandomizedInterval;

    /** @type {string} */
    #schemaName;

    /** @type {string} */
    #tableName;

    #dbConnection;

    /** @param {PGStoreOptions} options */
    constructor(options = {}) {
      super(options);
      if (options.elk) {
        elkData = {
          ...elkData,
          ...options.elk,
        };
      }
      logger.info("OracleSessionStore.constructor(%s)", options, elkData);
      this.ttl = options.ttl; // TODO: Make this private as well, some bug in at least TS 4.6.4 stops that
      this.#schemaName = options.schemaName || 'OAUTH2';
      this.#tableName = options.tableName || 'COOKIE_SESSIONS';
      this.#disableTouch = !!options.disableTouch;
      if (options.pruneSessionInterval === false) {
        this.#pruneSessionInterval = false;
      } else {
        this.#pruneSessionInterval = (options.pruneSessionInterval || DEFAULT_PRUNE_INTERVAL_IN_SECONDS) * 1000;
      }
      this.closed = false;
    }

    /**
     * Closes the session store
     *
     * Currently only stops the automatic pruning, if any, from continuing
     *
     * @access public
     * @returns {Promise<void>}
     */
    async close() {
      logger.debug("close", elkData);
      this.closed = true;
      this.#clearPruneTimer();
      if (this.#dbConnection) {
        this.#dbConnection.close();
      }
    }

    async #initPruneTimer() {
      if (!this.#dbConnection) {
        this.#dbConnection = await oracle.getDBConnection().catch((error) => {
          // Questo è normalmente vuoto perchè se non va la prima volta, 
          // verrà fatto un tentativo la prossima volta, quando sarà attiva verrà poi riutilizzata
        });
      }
      if (this.#pruneSessionInterval && !this.closed && !this.pruneTimer) {
        logger.debug("#rearmPruneTimer", elkData);
        const delay = this.#pruneSessionRandomizedInterval ? this.#pruneSessionRandomizedInterval(this.#pruneSessionInterval) : this.#pruneSessionInterval;
        this.pruneTimer = setTimeout(() => {
          this.pruneSessions();
        }, delay);
        this.pruneTimer.unref();
        //commento
      }
    }

    #clearPruneTimer() {
      if (this.pruneTimer) {
        logger.debug("#clearPruneTimer", elkData);
        clearTimeout(this.pruneTimer);
        this.pruneTimer = undefined;
      }
    }

    /**
     * Does garbage collection for expired session in the database
     *
     * @param {SimpleErrorCallback} [fn] - standard Node.js callback called on completion
     * @returns {void}
     * @access public
     */
    pruneSessions(fn) {
      logger.verbose("pruningCookieSessions", elkData);
      let query = `DELETE FROM `+ this.#tableName +` WHERE expire < current_timestamp`;
      oracle
        .dbExecute(query, [], { connection: this.#dbConnection, doCommit: true, elk: elkData })
        .then((result) => {
          if (result.rowsAffected != 0) {
            logger.info("Eliminate %s voci di sessione cookies scadute.", result.rowsAffected, elkData);
          }
          return result;
        })
        .catch((error) => {
          logger.error("ERROR on pruneSessions: %O", error, { error, ...elkData });
        })
        .finally((result) => {
          this.#clearPruneTimer();
          this.#initPruneTimer();
        });
    }

    /**
     * Attempt to fetch session by the given `sid`.
     *
     * @param {string} sid – the session id
     * @param {(error: Error|null, firstRow?: PGStoreQueryResult) => void} fn – a standard Node.js callback returning the parsed session object
     * @access public
     */
    get(sid, fn) {
      logger.verbose("get sessionSid(%O)", sid, elkData);
      this.#initPruneTimer();
      let query = "SELECT sess FROM "+ this.#tableName +" WHERE sid = :1 ";
      let params = [{ val: sid }];
      oracle
        .dbExecute(query, params, { connection: this.#dbConnection, elk: elkData })
        .then((result) => {
          if (result && result.rows && result.rows.length == 1) {
            let sessData = typeof result.rows[0].SESS === "string" ? JSON.parse(result.rows[0].SESS) : data["sess"];
            return fn(null, sessData);
          } else {
            return fn(null);
          }
        })
        .catch((error) => {
          logger.error("ERROR on getSessionSid(%s): %O", sid, error, { error, ...elkData });
          fn && fn(error);
        });
    }

    /**
     * Commit the given `sess` object associated with the given `sid`.
     *
     * @param {string} sid – the session id
     * @param {SessionObject} sess – the session object to store
     * @param {SimpleErrorCallback} fn – a standard Node.js callback returning the parsed session object
     * @access public
     */
    set(sid, sess, fn) {
      logger.verbose("set sessionSid(%O,..)", sid, elkData);
      //logger.debug("set sessionSid sess:%O", sess, elkData);
      this.#initPruneTimer();
      let params = {
        sid: { val: sid },
        expire: { val: sess.cookie["expires"] },
        sess: { val: JSON.stringify(sess) },
      };
      let setQuerySql = `BEGIN INSERT INTO `+ this.#tableName +` (sid, expire, sess) values(:sid, :expire, :sess); EXCEPTION WHEN DUP_VAL_ON_INDEX THEN UPDATE `+ this.#tableName +` SET expire = :expire, sess = :sess WHERE sid = :sid; END;`;
      oracle
        .dbExecute(setQuerySql, params, { connection: this.#dbConnection, doCommit: true, elk: elkData })
        .then((result) => {
          return fn(null, sess);
        })
        .catch((error) => {
          logger.error("ERROR on setSession(%s): %O", sid, error, { error, ...elkData });
          fn && fn(error);
        });
    }

    /**
     * Destroy the session associated with the given `sid`.
     *
     * @param {string} sid – the session id
     * @param {SimpleErrorCallback} fn – a standard Node.js callback returning the parsed session object
     * @access public
     */
    destroy(sid, fn) {
      logger.verbose("destroy sessionSid(%s)", sid, elkData);
      this.#initPruneTimer();
      let params = [{ val: sid }];
      let deleteQuerySql = "DELETE FROM " + this.#tableName + " WHERE sid=:1";
      oracle
        .dbExecute(deleteQuerySql, params, { connection: this.#dbConnection, doCommit: true, elk: elkData })
        .then((result) => {
          return fn && fn(null);
        })
        .catch((error) => {
          logger.error("ERROR on destroy(%s):%O", sid, error, { error, ...elkData });
          fn && fn(error);
        });
    }

    /**
     * Touch the given session object associated with the given session ID.
     *
     * @param {string} sid – the session id
     * @param {SessionObject} sess – the session object to store
     * @param {SimpleErrorCallback} fn – a standard Node.js callback returning the parsed session object
     * @access public
     */
    touch(sid, sess, fn) {
      logger.verbose("touch sessionSid(%O)", sid);
      this.#initPruneTimer();
      if (this.#disableTouch) {
        fn && fn(null);
        return;
      }
      let params = [{ val: sess.cookie["expires"] }, { val: JSON.stringify(sess) }, { val: sid }];
      let query = "UPDATE "+ this.#tableName +" SET expire = :1, sess = :2 WHERE sid=:3";
      oracle
        .dbExecute(query, params, { doCommit: true, connection: this.#dbConnection, elk: elkData })
        .then((result) => {
          //logger.debug("session.db.touch.result -> %O", result);
          return fn(null, sess);
        })
        .catch((error) => {
          logger.error("ERROR on touch(%s):%O", sid, error, { error, ...elkData });
          fn && fn(error);
        });
    }
    createTableSql = `CREATE TABLE `+ this.#schemaName + `.` + this.#tableName + `("SID" VARCHAR2(32 BYTE) NOT NULL ENABLE, "EXPIRE" TIMESTAMP (6) WITH TIME ZONE, "SESS" VARCHAR2(4000), CONSTRAINT "ENSURE_JSON" CHECK (sess is json) ENABLE, PRIMARY KEY ("SID"))`;
  }
  return OracleSessionStore;
};
/*
CREATE TABLE "OAUTH2"."COOKIE_SESSIONS" 
   (	"SID" VARCHAR2(32 BYTE) NOT NULL ENABLE, 
	"EXPIRE" TIMESTAMP (6) WITH TIME ZONE, 
	"SESS" VARCHAR2(4000), 
	 CONSTRAINT "ENSURE_JSON" CHECK (sess is json) ENABLE, 
	 PRIMARY KEY ("SID")
  );
*/
