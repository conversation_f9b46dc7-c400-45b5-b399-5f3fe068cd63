<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">

<head>
  <% title=t("viewer.heading") %>
    <% icon="fa-solid fa-map" %> <%- include('components/head') %>
</head>

<body class="bg-darkgray text-white h-screen m-0 overflow-hidden <%= lang === 'ar' ? 'text-right' : '' %>">
  <!-- Full-screen loading overlay -->
  <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
    <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
    <div class="mt-4 text-2xl text-gray-300">
      <%= t("please_wait") %>
    </div>
  </div>
  <!-- 3D Viewer -->
  <div class="flex flex-row p-1">
    <canvas id="vtk-3d-canvas" class="hidden"></canvas>
    <!-- Button Row Overlay -->
    <div id="buttonRow"
      class="absolute top-6 left-1/2 -translate-x-1/2 flex gap-4 z-50 bg-darkgray bg-opacity-80 px-4 py-2 rounded shadow-lg min-w-max">
      <button onclick="parent.window.location.href = '/patients/<%=patientId%>/simulation-new?mapId=<%=mapId%>'"
        class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition w-full flex items-center justify-center gap-4">
        <i class="fa-solid fa-add text-l"></i>
        <p>
          <%= t("simulation.new") %>
        </p>
      </button>
      <button onclick="viewer.setCameraView('AP')"
        class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition">
        <%= t("viewer.view_ap") %>
      </button>
      <button onclick="viewer.setCameraView('PA')"
        class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition">
        <%= t("viewer.view_pa") %>
      </button>
      <button onclick="viewer.setCameraView('RO')"
        class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition">
        <%= t("viewer.view_ro") %>
      </button>
      <button onclick="viewer.setCameraView('LO')"
        class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition">
        <%= t("viewer.view_lo") %>
      </button>
      <button onclick="viewer.setCameraView('RL')"
        class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition">
        <%= t("viewer.view_rl") %>
      </button>
            <button onclick="viewer.setCameraView('LL')"
        class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition">
        <%= t("viewer.view_ll") %>
      </button>
      <button id="immensiveBtn" class="px-4 pb-2 pt-3 bg-darkred text-white rounded hover:bg-darkred transition">
        <i class="fa-solid fa-up-right-from-square text-xl text-center"></i>
      </button>
    </div>

    <%- include('components/viewer-hamburger') %>
      <script src="/js/hamburger-menu.js"></script>
  </div>
  <script src="/js/vtk-viewer.js" type="module"></script>
  <script>
    let viewer;
    let models = <%- resources %>;

    const loadingScreenDiv = document.getElementById("loadingScreen");
    const vtkContainer = document.getElementById("vtk-3d-canvas");

    let canvas_width = window.innerWidth;
    let canvas_height = 750;

    vtkContainer.width = canvas_width;
    vtkContainer.height = canvas_height;
    vtkContainer.style.width = canvas_width + "px";
    vtkContainer.style.height = canvas_height + "px";

    function hideLoading() {
      if (loadingScreenDiv) {
        loadingScreenDiv.classList.add("hidden");
      }
      if (vtkContainer.classList) {
        vtkContainer.classList.remove("hidden");
      }
    }

    window.addEventListener("ModuleLoaded", async () => {
      viewer = new VtkViewer("#vtk-3d-canvas", { models: models });
      await viewer.viewerInit();
      viewer.setSize(canvas_width, canvas_height);
      await viewer.refresh();
      window.viewer = viewer;
      hideLoading();
    });

    function onCellClickEvent(actor, x, y, z) {
      console.log("onCellClickEvent received from C++: ", actor, x, y, z);
    }
  </script>

  <script type="module">
    // get url
    const currentUrl = window.location.href;
    console.log(currentUrl);
    const patientId = "<%= patientId %>";
    const mapId = "<%= mapId %>";

    const immensiveBtn = document.getElementById("immensiveBtn");
    document.addEventListener("DOMContentLoaded", () => {
      immensiveBtn.addEventListener("click", async () => {
        await fetch("<%= process.env.IMMENSIVE_HOST %>/ws/setViewerData", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            eventId: "map-" + mapId,
            patientId: patientId,
          }),
        });
        parent.window.electronAPI.openImmensive();
      });
    });
  </script>
</body>

</html>