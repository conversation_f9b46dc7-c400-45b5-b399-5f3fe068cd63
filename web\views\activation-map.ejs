<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("viewer.heading") %> <% icon = "fa-solid fa-map" %> <%- include('components/head') %>
  </head>
  <body class="bg-darkgray text-white h-screen m-0 overflow-hidden <%= lang === 'ar' ? 'text-right' : '' %>">
    <!-- Full-screen loading overlay -->
    <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
      <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
      <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
    </div>
    <a href="/patients/<%=patientId%>/simulation?mapId=<%=mapId%>" target="_parent" >
        <button class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition w-full flex items-center justify-center gap-4">
          <i class="fa-solid fa-add text-l"></i>
          <p><%= t("simulation.new") %></p>
        </button>
      </a>
    <!-- 3D Viewer -->
    <div class="flex flex-row p-1">
      <canvas id="vtk-3d-canvas" class="hidden"></canvas>
          <!-- Button Row Overlay -->
    <div id="buttonRow" class="absolute top-14 left-1/2 -translate-x-1/2 flex gap-4 z-50 bg-darkgray bg-opacity-80 px-4 py-2 rounded shadow-lg">
      <button onclick="setCameraView('AP')" class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition"><%= t("viewer.view_ap") %></button>
      <button onclick="setCameraView('PA')" class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition"><%= t("viewer.view_pa") %></button>
      <button onclick="setCameraView('LL')" class="px-4 py-2 bg-darkred text-white rounded hover:bg-darkred transition"><%= t("viewer.view_ll") %></button>
    </div>
    <button id="immensiveBtn" class="absolute bottom-12 right-4 bg-darkred text-white pt-1 rounded-lg w-12 h-12">
      <i class="fa-solid fa-up-right-from-square text-xl text-center"></i>
    </button>
    <%- include('components/viewer-hamburger') %>
    <script src="/js/hamburger-menu.js"></script>
    </div>
    <script>
      window.MODELS = <%- resources %>;
      window.CANVAS_SIZE = {
        width: window.innerWidth,
        height: 750
      };
      let idx = 0;
      function onCellClickEvent(actor, x, y, z) {
        console.log("onCellClickEvent received from C++: ", actor, x, y, z);
        /*
        if (actor == "LV_ENDOCARDIUM") {
          idx++;
          window.setTargetPosition("LV_TARGET_"+idx, x, y, z);
        }
        if (actor == "RV_ENDOCARDIUM") {
          window.setTargetPosition("RV_TARGET", x, y, z);
        }*/
      }
    </script>
    <script src="/js/viewer.js" type="module"></script>
    <script type="module">
      // call init after viewer is loaded
      window.addEventListener("viewerLoaded", async () => {
        await window.viewerInit();
      });
      // get url
      const currentUrl = window.location.href;
      console.log(currentUrl);
      const patientId = "<%= patientId %>";
      const mapId = "<%= mapId %>";

      const immensiveBtn = document.getElementById("immensiveBtn");
      document.addEventListener("DOMContentLoaded", () => {
        immensiveBtn.addEventListener("click", async () => {
          await fetch("<%= process.env.IMMENSIVE_HOST %>/ws/setViewerData", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              eventId: "map-" + mapId,
              patientId: patientId,
            }),
          });
          parent.window.electronAPI.openImmensive();
        });
      });
    </script>
  </body>
</html>
