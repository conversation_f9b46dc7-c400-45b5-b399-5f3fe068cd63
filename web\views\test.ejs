<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1" />
    <link
      rel="icon"
      type="image/x-icon"
      href="https://images.squarespace-cdn.com/content/v1/612f2e13d90b21039c48c496/b8deb88b-a86b-4a19-8579-6185584710c4/favicon.ico?format=100w"
    />
    <title>ECG</title>
    <link href="/css/ecg.css" rel="stylesheet" />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div id="overlay">
      <div id="loader" class="spinner"></div>
      <p id="loadingText">Caricamento dati ECG in corso...</p>
    </div>
    <canvas id="ecgCanvas"></canvas>
    <script>
      const canvas = document.getElementById('ecgCanvas')
      const overlay = document.getElementById('overlay')
    </script>
    <script type="module">
      import { EcgData, EcgDrawer } from './js/ecg-lib.js';

      const drawer = new EcgDrawer(canvas);

      window.onload = async () => {
        //document.getElementById('overlay')
        overlay.style.display = 'flex';
        await new Promise((resolve) => requestAnimationFrame(resolve))
        let readData = await loadEcgData()
        drawer.setEcgData(readData)
        //document.getElementById('overlay')
        overlay.style.display = 'none';
        drawer.redraw();
      }
      async function loadEcgData() {
        let toRet = new EcgData();

        const response = await fetch('/12345A2.csv');

        const data = await response.text();
        const lines = data.trim().split('\n');
        console.log('Numero di righe:', lines.length)

        const headers = lines[0].split(',').map((h) => h.trim())
        console.log('Numero di derivazioni:', headers.length)

        const dataLines = lines.slice(1)
        //Pre-alloco gli array, per velocizzare il tutto.
        for (const key of Object.keys(toRet.data)) {
          toRet.data[key].values = new Array(dataLines.length)
        }
        for (let i = 0; i < dataLines.length; i++) {
          const values = dataLines[i].split(',')
          const index = parseInt(values[0], 10)
          for (let j = 1; j < headers.length; j++) {
            const header = headers[j]
            const lead = toRet.data[header]
            if (lead) {
              lead.values[index] = parseFloat(values[j])
            }
          }
        }
        return toRet
      }
    </script>
  </body>
</html>
