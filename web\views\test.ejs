<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1" />
    <link
      rel="icon"
      type="image/x-icon"
      href="https://images.squarespace-cdn.com/content/v1/612f2e13d90b21039c48c496/b8deb88b-a86b-4a19-8579-6185584710c4/favicon.ico?format=100w"
    />
    <title>ECG</title>
    <link href="/css/ecg.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  </head>
  <body>
    <h1>WebSocket Client</h1>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="Type a message..." />
    <button id="sendButton">Send</button>

    <script>
      // Crea una connessione WebSocket
      const socket = new WebSocket("ws://localhost:8080");

      // Gestisci l'apertura della connessione
      socket.addEventListener("open", () => {
        console.log("Connected to the WebSocket server");
      });

      // Gestisci i messaggi ricevuti dal server
      socket.addEventListener("message", (event) => {
        const messagesDiv = document.getElementById("messages");
        messagesDiv.innerHTML += `<p>${event.data}</p>`;
      });
      socket.addEventListener("server-status", (event) => {
        console.log("Server-status:", event);
        //const messagesDiv = document.getElementById('messages');
        //messagesDiv.innerHTML += `<p>${event.data}</p>`;
      });
      // Invia un messaggio al server quando il pulsante viene cliccato
      document.getElementById("sendButton").addEventListener("click", () => {
        const input = document.getElementById("messageInput");
        const message = input.value;
        socket.send(message);
        input.value = ""; // Pulisci il campo di input
      });
    </script>
  </body>
</html>
