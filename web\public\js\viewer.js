import createGeometryViewerModule from "/js/GeometryViewer.js";
if (!window.MODELS) {
  throw new Error("window.MODELS is not defined");
}
//const { vtuModel, plyModel } = window.MODELS;
let canvas_width = (window.CANVAS_SIZE && window.CANVAS_SIZE.width) || undefined;
let canvas_height = (window.CANVAS_SIZE && window.CANVAS_SIZE.height) || undefined;

const start = Date.now();
const Module = await createGeometryViewerModule({
  preRun: [
    (runtime) => {
      if (runtime._setDefaultExpandVTKCanvasToContainer) {
        runtime._setDefaultExpandVTKCanvasToContainer(false);
      }
      if (runtime._setDefaultInstallHTMLResizeObserver) {
        runtime._setDefaultInstallHTMLResizeObserver(false);
      }
    },
  ],
  locateFile: (path) => path,
  print: (text) => {
    const elapsed = Date.now() - start;
    console.log(`[${elapsed} ms] ${text}`);
  },
  printErr: (text) => console.error(text),
});

// Instantiate the viewer
const viewer = new Module.GeometryViewer();

window.resizeRender = async () => {
  /* Non più usato ora gestito da css
  if (canvas_height && canvas_width) {
    viewer.setSize(canvas_width, canvas_height);
  }
  */
  await viewer.render();
};

const sizedRender = window.resizeRender;
const vtkContainer = document.getElementById("vtk-3d-canvas");
const loadingScreenDiv = document.getElementById("loadingScreen");
window.viewerInit = async () => {
  await viewer.initialize();
  await viewer.start();
  if (window.MODELS) {
    for (const resource of window.MODELS) {
      switch (resource.type) {
        case "HEART-MODEL": {
          await loadHeartFile(resource.url);
          break; // Esci dal case
        }
        case "CS-MODEL": {
          await loadCoronaryFile(resource.url);
          break; // Esci dal case
        }
        case "LAST-ACT-TIME": {
          await loadNamedFile(resource.url, "LAST-ACT-TIME");
          break; // Esci dal case
        }
        case "LV-LEAD": {
          await loadNamedFile(resource.url, "LV_TARGET");
          break;
        }
        case "RV-LEAD": {
          await loadNamedFile(resource.url, "RV_TARGET");
          break;
        }
        default: {
          console.log("NON GESTITO:", resource.type, resource.url);
          break; // Gestisci eventuali altri casi
        }
      }
    }
    //Aggiunge tutti gli attori nel vtkRender...
    await viewer.addActorsToRender();
  }

  //FIXME: check if exists
  if (loadingScreenDiv) {
    loadingScreenDiv.classList.add("hidden");
  }
  if (vtkContainer.classList) {
    vtkContainer.classList.remove("hidden");
  }
  if (canvas_height && canvas_width) {
    vtkContainer.width = canvas_width;
    vtkContainer.height = canvas_height;
    vtkContainer.style.width = canvas_width + "px";
    vtkContainer.style.height = canvas_height + "px";
    viewer.setSize(canvas_width, canvas_height);
  }
  await viewer.resetView(0.0);
  //await viewer.resetView(0.9);
  await sizedRender();
};

// Simple keyboard controls
let wire = false;
window.addEventListener("keydown", async (e) => {
  if (e.key === "w") {
    wire = !wire;
    await viewer.setRepresentation(wire ? 1 : 2);
    //viewer.setSize(1080, 900);
    //await viewer.render();
    await sizedRender();
  }
  if (e.key === "r") {
    await viewer.resetView(0.9);
    //await viewer.render();
    await sizedRender();
  }
});

window.setCameraView = async function (viewName) {
  const posDelta = 100000;
  switch (viewName) {
    case "AP":
      viewer.setPosition(0, 0, posDelta);
      viewer.setViewUp(0, 1, 0);
      break;
    case "PA":
      viewer.setPosition(0, 0, posDelta * -1);
      viewer.setViewUp(0, 1, 0);
      break;
    case "LL":
      viewer.setPosition(posDelta, 0, 0);
      viewer.setViewUp(0, 1, 0);
      break;
  }
  viewer.resetView(0.9);
  //await viewer.render();
  await sizedRender();
};

window.setActorVisibility = function (index, visible) {
  viewer.setActorVisibility(index, visible);
};
window.setActorOpacity = function (index, value) {
  viewer.setActorOpacity(index, value);
};
window.setActorMapName = function (index, map) {
  viewer.setActorMapName(index, map);
};
window.setPosition = function (x, y, z) {
  viewer.setPosition(x, y, z);
};
let newIds = 0;
window.addRandomPoint = function () {
  viewer.addNamedActor("NEW_POINT_" + newIds++, 73.7632, -157.233, -567.091);
};
window.addNamedActor = function (name, x, y, z) {
  viewer.addNamedActor(name, x, y, z);
};
window.setTargetPosition = function (name, x, y, z) {
  viewer.setTargetPosition(name, x, y, z);
};
window.getActorDataAsBase64 = function (actorName) {
  const result = viewer.getActorDataAsBase64(actorName);
  console.log("Result:", result);
  return result;
};
// Carica il modello vtu del cuore intero
const loadHeartFile = async (file) => {
  const response = await fetch(file);
  const arrayBuffer = await response.arrayBuffer();
  // Allocate memory in WASM
  const size = arrayBuffer.byteLength;
  const ptr = Module._malloc(size);
  Module.HEAPU8.set(new Uint8Array(arrayBuffer), ptr);
  // Load the data into the viewer
  await viewer.loadHeartModelFromMemory(ptr, size);
  Module._free(ptr);
};

const loadCoronaryFile = async (file) => {
  const plyResponse = await fetch(file);
  const plyArrayBuffer = await plyResponse.arrayBuffer();
  // Allocate memory for PLY
  const plySize = plyArrayBuffer.byteLength;
  const plyPtr = Module._malloc(plySize);
  Module.HEAPU8.set(new Uint8Array(plyArrayBuffer), plyPtr);
  // Load the PLY data into the viewer with a different actor ID
  await viewer.loadCoronaryModelFromMemory(file, plyPtr, plySize);
  Module._free(plyPtr);
};
const loadNamedFile = async (file, name) => {
  const plyResponse = await fetch(file);
  const plyArrayBuffer = await plyResponse.arrayBuffer();
  // Allocate memory for PLY
  const plySize = plyArrayBuffer.byteLength;
  const plyPtr = Module._malloc(plySize);
  Module.HEAPU8.set(new Uint8Array(plyArrayBuffer), plyPtr);
  // Load the PLY data into the viewer with a different actor ID
  await viewer.loadVtkModelFromMemory(file, plyPtr, plySize, name);
  console.log("DONE LOAD VTK_SOURCE", file, plySize);
  Module._free(plyPtr);
};

// emit event on window
window.dispatchEvent(new Event("viewerLoaded"));
//await window.resizeRender();
