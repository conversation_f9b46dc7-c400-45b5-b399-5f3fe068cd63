<div id="breadcrumbs" class="flex space-x-2 text-white font-semibold text-xl pt-2 px-4">
  <% const isDisabled = typeof disableBreadcrumb !== 'undefined' && disableBreadcrumb; %>
  <% function breadcrumbLink(href, text) { %>
    <% if (isDisabled) { %>
      <span class="text-red opacity-60 cursor-not-allowed"><%= text %></span>
    <% } else { %>
      <a href="<%= href %>" class="hover:underline text-red"><%= text %></a>
    <% } %>
  <% } %>
  <% breadcrumbLink('/', 'Home'); %>
  <!-- Qua poi lo gestiremo a segmenti o altro?? todo/tocheck -->
  <% if (path.includes('patients')) { %>
    <span class="text-white">&gt;</span>
    <% breadcrumbLink('/patients/search', t('breadcrumbs.patient.search')); %>
  <% } %>
  <% if (path.includes('patients/new')) { %>
    <span class="text-white">&gt;</span>
    <% breadcrumbLink('/patients/new', t('breadcrumbs.patient.new')); %>
  <% } %>
  <% if (selectedPatient.id) { %>
    <span class="text-white">&gt;</span>
    <% breadcrumbLink(`/patients/${selectedPatient.id}`, t('breadcrumbs.patient.selected')); %>
  <% } %>
  <% if (path.includes('/ecg')) { %>
    <span class="text-white">&gt;</span>
    <% breadcrumbLink(`/patients/${selectedPatient.id}/ecg`, t('breadcrumbs.patient.ecg')); %>
  <% } %>
  <% if (path.includes('patient-map')) { %>
    <span class="text-white">&gt;</span>
    <% breadcrumbLink(`/patients/${selectedPatient.id}/maps`, t('breadcrumbs.patient.activationMap')); %>
  <% } %>
</div>
