---
# file: docker-compose.yaml
services:
  db:
    container_name: oracle-xe-11
    image: gvenzl/oracle-xe:********-slim-faststart
    environment:
      ORACLE_PASSWORD: test
      APP_USER: ${USERORACLE}
      APP_USER_PASSWORD: ${PASSORACLE}
      TZ: Europe/Rome
    networks:
      - db_net
    ports: #per il debug esterno espongo la 1533 <- qui fare attenzione quindi..
      - 1533:1521
    volumes: 
      #Qui mappo gli scripts che verranno eseguiti alla CREAZIONE del container 
      - ./database/container-entrypoint-initdb.d:/container-entrypoint-initdb.d
    healthcheck:
      test: healthcheck.sh
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped
  app:
    container_name: nice-backend
    build:
      context: .
      dockerfile: ./Dockerfile
    ports:
    - ${PORT}:${PORT}
    - ${WEB_SOCKET_PORT}:${WEB_SOCKET_PORT}
    healthcheck:
      test: curl -I --fail --noproxy '*' http://localhost:${PORT}${VIRTUALDIRPATH:-/}health-check || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
    restart: ${DOCKER_RESTART:-no}
    volumes:
    - ./logs:${LOGPATH}
    #- ${LOGPATH}:${LOGPATH} <- questo su windows NON VA!
    - ${STORAGE_PATH}:/app/storage
    - .env:/app/.env
    environment:
    - CONNECTSTRING=db:1521/XE
    - IP_ADDRESS=0.0.0.0
    - NODE_ENV=production
    networks:
    - db_net
networks:
  db_net:
    driver: bridge
...
