
import fs from 'fs';
const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf-8'));
const { version } = packageJson;

function localsMiddleware(req, res, next) {
  //logger.debug("Req.session.id: %s", req.session.id);
  if (req.headers["referer"]) {
    res.locals.backUrl = req.headers["referer"];
  } else {
    res.locals.backUrl = "/";
  }
  res.locals.username = req.session.username;
  res.locals.selectedPatient = req.session.selectedPatient;
  res.locals.path = req.path;
  res.locals.title = "";
  res.locals.icon = "";
  res.locals.version = version;

  res.locals.lastSearchTerm = req.session.lastSearchTerm;

  res.locals.getStorageUrl = function (idPatient, mapId, resType, uuid) {
    let resourceURL = "";
    switch (resType) {
      case 1:
        resourceURL = `/storage/${idPatient}/${uuid}`;
        break;
      default:
        resourceURL = `/storage/${idPatient}/map-${mapId}/${uuid}`;
        break;
    }
    return resourceURL;
  };
  next();
}

export default localsMiddleware;
