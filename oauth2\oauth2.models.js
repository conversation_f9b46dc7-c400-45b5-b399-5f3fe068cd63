'use strict';
const logger = require('medcommon/logger');
const oracle = require('medcommon/oracle');

const queries = require('./oauth2.queries');

const { spacedArray } = require('medcommon/utils');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');

const algorithm = 'aes-256-ctr';
const iv = crypto.randomBytes(16);
const secretKey = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3';

class OAuthModel {
  dbOptions = {};

  constructor(options = {}) {
    logger.info('OAuthModel.constructor()');
    this.elk = options;
    //logger.info("OAuthModel.elk:%O", this.elk);
    this.dbOptions = {
      elk: this.elk,
    };
    this.jwtSecretKey = process.env.JWT_SECRET_KEY || process.env.OAUTH2_JWT_SECRET || '01234567890';
    this.jwtIssuer = process.env.OAUTH_ADMIN_HOSTNAME || process.env.OAUTH2_JWT_ISSUER || 'http://localhost';
  }

  static from(other, elk = {}) {
    return new OAuthModel({ elk });
  }

  //#region Metodi lettura User/Client/
  /**
   * Legge dalla tabella utenti (via vista/sp) i dati utenti tramite username e password forniti.
   *
   * Ritorna:
   * - Se non è stato trovato/valido -> false
   * - Altrimenti -> oggetto json con tutti i dati utente (senza la password)
   *
   * @param {string} username Nome utente
   * @param {string} password Password utente
   * @return {Object|Boolean}
   * @api public
   */
  getUser = async function (username, password) {
    logger.verbose('-> getUser(username:%s, password:*******)', username, this.elk);
    let toRet = false;
    try {
      let params = [username, password];
      let result = await oracle.dbSingleRow(queries.getUserSql, params, { ...this.elk, dntLog: true });
      if (result) {
        toRet = new OAuthUser(result);
      }
    } catch (error) {
      logger.error('Error on getUser:%O', error, { error, ...this.elk });
    }
    logger.debug('getUser:%O', toRet, this.elk);
    logger.verbose('<- getUser[id:%s]', toRet.id || toRet, this.elk);
    return toRet;
  };
  getClient = async function (clientId, clientSecret) {
    logger.verbose('-> getClient(%s, %s)', clientId, clientSecret, this.elk);
    let toRet = {};
    try {
      let params = [clientId, clientSecret || '%'];
      let result = await oracle.dbSingleRow(queries.getClientSql, params, { elk: this.elk });
      if (result) {
        let grants = spacedArray(result.GRANT_TYPES);
        let redirectUris = spacedArray(result.REDIRECT_URI);
        let scopes = spacedArray(result.SCOPE);
        toRet = {
          id: clientId,
          //clientSecret: result.CLIENT_SECRET || null, --> sembra opzionale vedremo....
          grants: grants,
          redirectUris: redirectUris,
          scope: scopes,
          user_id: result.USER_ID,
          accessTokenLifetime: 60 * 60, // 1 ora
          refreshTokenLifetime: 60 * 60 * 24 * 30, // 30 giorni
        };
      }
    } catch (error) {
      logger.error('Error:%O', error, { error, ...this.elk });
      toRet = false;
    }
    logger.verbose('<- getClient[id:%s]', toRet.id || toRet, this.elk);
    return toRet;
  };
  /** Viene richiamata quanto in token metto grant_type = client_credential
   *  Viene letta cercato il client nella tabella clients.
   *  Poi viene letto user id dalla tabella access_tokens.
   *  //SMRACH: Testato funzionante.
   * @param {*} user
   */
  getUserFromClient = async function (client) {
    let toRet = false;
    logger.verbose('-> getUserFromClient(%O)', client.id, this.elk);
    let params = [client.id];
    let result = await oracle.dbSingleRow(queries.getUserFromClientSql, params, { elk: this.elk });
    if (result) {
      toRet = new OAuthUser(result);
    }
    logger.verbose('<- getUserFromClient[id:%s]', toRet.id || toRet, this.elk);
    return toRet;
  };
  getUserFromToken = async function (token) {
    logger.verbose('-> getUserFromToken(%s)', token, this.elk);
    let toRet = false;
    try {
      let params = [token];
      let result = await oracle.dbSingleRow(queries.getUserFromTokenSql, params, { elk: this.elk });
      //toRet = result || false;
      if (result) {
        toRet = new OAuthUser(result);
      }
    } catch (error) {
      logger.error('error on getUserFromToken:%O', error, { error, ...this.elk });
    }
    logger.debug('getUserFromToken.user:%O', toRet, this.elk);
    logger.verbose('<- getUserFromToken[id:%s]', toRet.id || toRet, this.elk);
    return toRet;
  };
  //#endregion

  //#region Gestione oggetto TOKEN
  /**
 * Invoked to save an access token and optionally a refresh token, depending on the grant type.
 * This model function is required for all grant types.
 * @param {*} token 
 * @param {*} client 
 * @param {*} user 
 * @returns {*} { accessToken: (String),
        accessTokenExpiresAt: (Date),
        client: (Object),
        refreshToken: (optional String),
        refreshTokenExpiresAt: (optional Date),
        user (Object) }
 */
  //code.user, client, code.authorizationCode, code.scope)
  saveToken = async function (token, client, user) {
    logger.verbose('-> saveToken(%s, %s, %s)', token.accessToken, client.id, user.id, this.elk);
    logger.debug('-> saveToken(%s, %s, %s)', token, client, user, this.elk);
    if (!user.id) {
      logger.error('************* UTENTE NULLLLLLLL *************************');
    }
    let connection = await oracle.getDBConnection(false, this.elk);
    let connOptions = { doCommit: false, connection: connection, elk: this.elk };
    try {
      let insScope = (token.scope || []).join(' ');
      let accessParams = [token.accessToken, client.id, user.id, token.accessTokenExpiresAt, insScope];
      let result1 = await oracle.dbSingleRow(queries.insertAccessTokenSql, accessParams, connOptions);
      if (token.refreshToken) {
        let refreshParams = [token.refreshToken, client.id, user.id, token.refreshTokenExpiresAt, insScope];
        let result2 = await oracle.dbSingleRow(queries.insertRefreshTokenSql, refreshParams, connOptions);
      }
      await connection.commit();
      connection.close();
    } catch (error) {
      logger.error('Error on saveToken:%O', error, { error, ...this.elk });
      await connection.rollback();
      throw error;
    }
    let jwtData = {
      user: { id: user.id },
      origin: this.swapQuad(token.accessToken),
      scope: token.scope,
    };
    let jwtToken = jwt.sign(jwtData, this.jwtSecretKey, { expiresIn: '1h', issuer: this.jwtIssuer });

    var toRet = {
      accessToken: token.accessToken,
      accessTokenExpiresAt: token.accessTokenExpiresAt,
      refreshToken: token.refreshToken,
      refreshTokenExpiresAt: token.refreshTokenExpiresAt,
      scope: token.scope,
      token_type: 'Bearer',
      client: { id: client.id },
      user: { id: user.id },
      /*accessQuery: {
        //client: client,
        user: user,
      },*/
      jwt: jwtToken,
    };
    logger.debug('saveToken.token: %O', toRet);
    logger.verbose('<- saveToken[accessToken: %s]', toRet.accessToken || toRet, this.elk);
    return toRet;
  };
  getAccessToken = async function (token) {
    logger.verbose('-> getAccessToken(%s)', token, this.elk);
    let toRet = false;
    try {
      let params = [token];
      let result = await oracle.dbSingleRow(queries.getAccessTokenSql, params, { elk: this.elk });
      if (result) {
        toRet = {
          accessToken: result.ACCESS_TOKEN,
          accessTokenExpiresAt: result.EXPIRES,
          client: { id: result.CLIENT_ID },
          user: { id: result.USER_ID },
          scope: spacedArray(result.SCOPE),
        };
        if (toRet.accessToken) {
          let fullUser = await this.getUserFromToken(toRet.accessToken);
          toRet.user = fullUser;
        }
      }
    } catch (error) {
      logger.error('Error:%O', error, { error, ...this.elk });
    }
    logger.debug('accessToken.token: %O', toRet, this.elk);
    logger.verbose('<- accessToken[%s]', toRet.accessToken || toRet, this.elk);
    return toRet;
  };
  getRefreshToken = async function (token) {
    logger.verbose('-> getRefreshToken(%O)', token, this.elk);
    let toRet = false;
    try {
      let params = [token];
      let result = await oracle.dbSingleRow(queries.getRefreshTokenSql, params, { elk: this.elk });
      if (result) {
        toRet = {
          refreshToken: result.REFRESH_TOKEN,
          refreshTokenExpiresAt: result.EXPIRES,
          client: { id: result.CLIENT_ID },
          user: { id: result.USER_ID },
          scope: spacedArray(result.SCOPE),
        };
        let newClient = await this.getClient(result.CLIENT_ID);
        toRet.client = newClient;
      }
    } catch (error) {
      logger.error('Error:%O', error, { error, ...this.elk });
    }
    logger.verbose('<- getRefreshToken[toRet:%s]', toRet.refreshToken || toRet, this.elk);
    return toRet;
  };
  /**
   * Sebbene il nome dica solo Token va inteso come refresh_token in quanto questa function viene
   * richiamata quando si richiede il getRefreshToken.
   * @param {*} token
   * @returns
   */
  revokeToken = async function (token) {
    logger.verbose('-> revokeToken(%O)', token.refreshToken, this.elk);
    let toRet = false;
    try {
      let refToken = token['refreshToken'];
      let params = [refToken];
      toRet = await oracle.dbRowsAffected(queries.revokeRefreshTokenSql, params, { ...this.elk });
      await this.logRequestToDb(
        '/api/oauth/token',
        'revoke_token',
        '0.0.0.0',
        '0.0.0.0',
        'api.model.revokeToken',
        token.accessToken,
        token.refreshToken,
        this.elk
      );
    } catch (error) {
      logger.error('Error:%O', error, this.elk);
    }
    logger.verbose('<- revokeToken[toRet:%O]', toRet, this.elk);
    return toRet;
  };
  //#endregion

  swapQuad = (token) => {
    let toRet = '';
    let myArray = token.split('');
    for (let i = 0; i < 16; i++) {
      let start = 4 * i;
      let block = myArray[start + 3] + myArray[start + 2] + myArray[start + 1] + myArray[start];
      toRet += block;
    }
    return toRet;
  };

  //#region Gestione AuthorizazionCode
  saveAuthorizationCode = async function (code, client, user) {
    logger.verbose('-> saveAuthorizationCode(%s, %s, %s)', code.authorizationCode, client.id, user.id, this.elk);
    let toRet = code;
    try {
      let scope = (Array.isArray(code.scope) ? code.scope : [code.scope || '']).join(' ');
      let params = [
        code.authorizationCode,
        client.id,
        user.id,
        code.expiresAt,
        scope,
        code.redirectUri,
        code.codeChallenge,
        code.codeChallengeMethod,
      ];
      let result = await oracle.dbRowsAffected(queries.saveAuthorizationCodeSql, params, { ...this.elk });
    } catch (error) {
      logger.error('Catch Error on saveAuthorizationCode:%O', error, { error, ...this.elk });
      toRet = false;
    }
    logger.debug('saveAuthorizationCode: %O', toRet);
    logger.verbose('<- saveAuthorizationCode[code:%s]', toRet.authorizationCode || toRet, this.elk);
    return toRet;
  };
  /** Legge dal db i dati dell'authorizationCode precedentemente generato e salvato su db.
   * @param {string} authorizationCode Codice di autorizzazione
   * @return {JsonObject}  Se trovato restituisce l'oggetto altrimenti false.
   */
  getAuthorizationCode = async function (authorizationCode) {
    logger.verbose('-> getAuthorizationCode(%s)', authorizationCode, this.elk);
    let toRet = false;
    try {
      let params = [authorizationCode];
      let result = await oracle.dbSingleRow(queries.getAuthorizationCodeSql, params, { elk: this.elk });
      if (result) {
        toRet = {
          code: {
            code: result.AUTHORIZATION_CODE,
          },
          authorizationCode: result.AUTHORIZATION_CODE,
          codeChallenge: result.CHALLENGE,
          codeChallengeMethod: result.CHALLENGE_METHOD,
          expiresAt: result.EXPIRES,
          scope: spacedArray(result.SCOPE),
          redirectUri: result.REDIRECT_URI,
          client: { id: result.CLIENT_ID },
          user: { id: result.USER_ID },
        };
      }
    } catch (error) {
      logger.error('Error on getAuthorizationCode:%O', error, { error, ...this.elk });
      toRet = false;
    }
    logger.debug('getAuthorizationCode:%O', toRet, this.elk);
    logger.verbose('<- getAuthorizationCode[code:%O]', (toRet.code && toRet.code.code) || toRet, this.elk);
    return toRet;
  };
  revokeAuthorizationCode = async function (authorizationCode) {
    logger.verbose('-> revokeAuthorizationCode(%s)', authorizationCode.code.code, this.elk);
    let toRet = false;
    try {
      let params = [authorizationCode.code.code];
      toRet = await oracle.dbRowsAffected(queries.revokeAuthorizationCodeSql, params, { ...this.elk });
    } catch (error) {
      logger.error('Error:%O', error);
    }
    logger.debug('revoceAuthorizationCode: %O', authorizationCode, this.elk);
    logger.verbose('<- revokeAuthorizationCode[toRet:%s]', toRet, this.elk);
    return toRet;
  };
  //#endregion

  //#region Gestione DeviceCode
  /** Crea un nuovo codice temporaneo e lo salva su db
   * per essere poi convertito in un authorization_code valido.
   */
  createDeviceCode = async function (userId) {
    logger.verbose('-> createDeviceCode(%s)', userId, this.elk);
    let toRet = false;
    try {
      let newCode = crypto.randomBytes(40).toString('hex');
      logger.verbose('NEW device_code generated: %s', newCode, this.elk);
      toRet = await this.saveDeviceCode(newCode, userId);
      logger.verbose('QrCode SAVE result: %s', toRet, this.elk);
    } catch (error) {
      logger.error('Error:%O', error);
    }
    return toRet;
  };

  /** Legge dal db i dati del deviceCode precedentemente generato e salvato su db.
   * @param {string} deviceCode Codice qrcode da pagina di login.
   * @return {JsonObject}  Se trovato restituisce l'oggetto altrimenti false.
   */
  getDeviceCode = async function (deviceCode) {
    logger.verbose('-> getDeviceCode(%s)', deviceCode, this.elk);
    let toRet = false;
    try {
      let params = [deviceCode];
      let result = await oracle.dbSingleRow(queries.getDeviceCodeSql, params, { elk: this.elk });
      if (result) {
        toRet = {
          code: result.DEVICE_CODE,
          expiresAt: result.EXPIRES,
          user: { id: result.USER_ID },
        };
      }
    } catch (error) {
      logger.error('Error on getDeviceCode:%O', error, { error, ...this.elk });
      toRet = false;
    }
    logger.debug('getDeviceCode:%O', toRet, this.elk);
    logger.verbose('<- getDeviceCode[code:%O]', (toRet.code && toRet.code.code) || toRet, this.elk);
    return toRet;
  };
  revokeDeviceCode = async function (deviceCode) {
    logger.verbose('-> revokeDeviceCode(%s)', deviceCode, this.elk);
    let toRet = false;
    try {
      let params = [deviceCode];
      toRet = await oracle.dbRowsAffected(queries.revokeDeviceCodeSql, params, { ...this.elk });
    } catch (error) {
      logger.error('Error:%O', error);
    }
    logger.debug('revokeDeviceCode: %O', deviceCode, this.elk);
    logger.verbose('<- revokeDeviceCode[toRet:%s]', toRet, this.elk);
    return toRet;
  };
  saveDeviceCode = async function (deviceCode, userId) {
    logger.verbose('-> saveDeviceCode(%s, %s)', deviceCode, userId, this.elk);
    let toRet = deviceCode;
    let expiresAt = new Date(Date.now() + 2 * 60 * 1000); //2 minuti di scadenza...
    try {
      let params = [deviceCode, userId, expiresAt];
      let result = await oracle.dbRowsAffected(queries.saveDeviceCodeSql, params, { ...this.elk });
    } catch (error) {
      logger.error('Catch Error on saveDeviceCode:%O', error, { error, ...this.elk });
      toRet = false;
    }
    logger.debug('saveDeviceCode: %O', toRet);
    logger.verbose('<- saveDeviceCode[code:%s]', toRet || toRet, this.elk);
    return toRet;
  };
  //#endregion

  //#region Gestione Scope
  /**
   * ValidateScope viene richiamato quando faccio saveToken PRIMA di tutte le altre operazioni.
   * const validatedScope = await this.validateScope(user, client, requestedScope);
   * @param {*} user
   * @param {*} client
   * @param {*} validateScope
   * @returns
   */
  validateScope = async function (user, client, scope) {
    logger.info('🖐 implementare il validateScope(%s, %s, %O)', user.id, client.scope, scope, this.elk);
    // Normalizza client.scope in array di stringhe
    let validScopes = [];
    if (typeof client.scope === 'string') {
      validScopes = client.scope.split(' ').filter((s) => s.length > 0);
    } else if (Array.isArray(client.scope)) {
      validScopes = client.scope;
    }

    // Normalizza scope richiesto in array di stringhe
    let requestedScopes = [];
    if (!scope) {
      // se non c'è scope richiesto, ritorna quelli validi
      return validScopes;
    } else if (typeof scope === 'string') {
      requestedScopes = scope.split(' ').filter((s) => s.length > 0);
    } else if (Array.isArray(scope)) {
      requestedScopes = scope;
    }

    // Controlla che ogni scope richiesto sia valido o sia un dettaglio di uno scope valido
    const isValid = requestedScopes.every((reqScope) => {
      return validScopes.some((validScope) => {
        return reqScope === validScope || reqScope.startsWith(validScope + ':');
      });
    });

    return isValid ? requestedScopes : false;
  };
  /**
   * Questo viene richiamato quando controllo il token (es. authorize, etc.)
   */
  verifyScope = async function (token, scope) {
    logger.info('🖐 implementare il verifyScope(%s, %s)', token.accessToken, scope, this.elk);
    return true;
  };
  //#endregion

  encrypt = (text) => {
    const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
    const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);
    return {
      iv: iv.toString('hex'),
      content: encrypted.toString('hex'),
    };
  };
  decrypt = (hash) => {
    const decipher = crypto.createDecipheriv(algorithm, secretKey, Buffer.from(hash.iv, 'hex'));
    const decrpyted = Buffer.concat([decipher.update(Buffer.from(hash.content, 'hex')), decipher.final()]);
    return decrpyted.toString();
  };

  //Mi serve che verificare che il client_id fornito esista e mi serve l'oggetto 'ben' formattato...
  apiGetClient = async function (clientId, logData = {}) {
    let toRet = false;
    let params = [clientId];
    try {
      let result = await oracle.dbSingleRow(queries.getClientApiSql, params, logData);
      if (result) {
        toRet = new OAuthClient(result);
      }
    } catch (error) {
      logger.error('Error on getClientApi: %O', error, { error, ...logData });
    }
    return toRet;
  };

  async isTokenValid(accessToken, logData = {}) {
    let toRet = false;
    try {
      let result = await oracle.dbSingleRow(queries.isTokenValidSql, [accessToken], logData);
      toRet = result.ISVALID == 1 ? true : false;
    } catch (error) {
      logger.error('Error on isTokenValid: %O', error, { error, ...logData });
    }
    logger.verbose('isTokenValid[%s]:%s', accessToken, toRet, logData);
    return toRet;
  }

  logRequestToDb = async function (endPoint, grant, hostIp, hostName, hostAgent, accToken, refToken, logData = {}) {
    try {
      //ENDPOINT, GRANT_TYPE, SOURCE_IP, SOURCE_HOST, SOURCE_AGENT, ACCESS_TOKEN, REFRESH_TOKEN
      let params = [endPoint, grant, hostIp, hostName, hostAgent, accToken, refToken];
      await oracle.dbRowsAffected(queries.insertAccessLogSql, params, logData);
    } catch (error) {
      logger.error('Error on apiInsertClient: %O', error, { error, ...logData });
    }
  };
  /** Recupera i singoli parametri di una determinata app dato il client_id */
  apiGetClientConfigs = async function (clientId, logData = {}) {
    let clientConfigs = [];
    try {
      let result = await oracle.dbExecute(queries.getClientConfigsSql, [clientId], logData);
      if (result.rows.length) {
        result.rows.forEach((element) => {
          clientConfigs.push(new OAuthClientConfig(element));
        });
      }
    } catch (error) {
      logger.error('Error on apiGetClientConfigs: %O', error, { error, ...logData });
    }
    return clientConfigs;
  };
}

class OAuthClient {
  constructor(data) {
    this.id = data.CLIENT_ID || data.id || crypto.randomUUID();
    this.secret = data.CLIENT_SECRET || data.secret;
    this.uri = data.REDIRECT_URI || data.uri || null;
    this.scope = data.SCOPE || data.scope || null;
    this.grants = data.GRANT_TYPES || data.grants || null;
    this.userId = data.USER_ID || data.userId || null;
    this.name = data.CLIENT_NAME || data.name || null;
    this.description = data.CLIENT_DESCRIPTION || data.description || null;
  }

  uriList = () => (this.uri || '').split(' ');

  createNewUUID = () => crypto.randomUUID();

  /**
   * Allineare con i parametri delle query
   * @returns Array of values
   */
  oracleSqlParams = () => {
    return {
      CLIENT_ID: { val: this.id },
      CLIENT_SECRET: { val: this.secret },
      REDIRECT_URI: { val: this.uri },
      GRANT_TYPES: { val: this.grants },
      SCOPE: { val: this.scope },
      USER_ID: { val: this.userId },
      CLIENT_NAME: { val: this.name },
      CLIENT_DESCRIPTION: { val: this.description },
    };
  }; //[this.id, this.name, this.secret, this.uri, this.grants, this.scope, this.userId, this.description];
}

class OAuthClientConfig {
  constructor(data) {
    this.id = data.CLIENT_ID || data.id;
    this.name = data.CONFIG_NAME || data.name;
    this.value = data.CONFIG_VALUE || data.value;
    this.type = data.CONFIG_TYPE || data.type;
    this.clientName = data.CLIENT_NAME || data.clientName;
    this.clientDescription = data.CLIENT_DESCRIPTION || data.clientDescription;
  }
}

class OAuthUser {
  constructor(data) {
    this.id = data.ID || data.USER_ID || data.id;
    this.sub = this.id;
    this.username = data.USERNAME || data.username || '';
    this.name = data.NOMINATIVO || data.name || '';
    this.isAdmin = data.IS_ADMIN || data.SYS_M == 'S' || data.isAdmin || false;
    this.family_name = '';
    this.given_name = '';
    this.email = data.EMAIL || data.mail || '';
    this.gender = data.SESSO || data.gender || '';
    this.birthdate = data.DATA_NASCITA || data.birthdate || '';
    this.medarchiver = {
      reparto: data.REPARTO || null,
      idRuolo: data.ID_RUOLO || null,
      canLog: data.LOG == 'S',
      canLogW: data.LOG_W == 'S',
      permessi: data.PERMESSI || '',
      isMedico: data.MEDICO == 'S' || data.isMedico || false,
      titolo: data.TITOLO || '',
    };
  }
}

module.exports = {
  OAuthModel: OAuthModel,
  OAuthClient: OAuthClient,
  OAuthUser: OAuthUser,
  OAuthClientConfig: OAuthClientConfig,
};
//Qui sotto ci sono i campi previsti per l'endpoint di introspezione UserInfo
/*let testUser = {
    "sub": "3XXJmjO2q9DQCmGWV8-dyv_EzpOT-m_IAc3NX5nuYXA",
    "name": "Simon Roberts",
    "family_name": "Roberts",
    "given_name": "Simon",
    "picture": "https://graph.microsoft.com/v1.0/me/photo/$value",
    "email": "<EMAIL>"
}*/
/*
    sub 	string 	Subject - Identifier for the End-User at the Issuer.
    name 	string 	End-User's full name in displayable form including all name parts, possibly including titles and suffixes, ordered according to the End-User's locale and preferences.
    given_name 	string 	Given name(s) or first name(s) of the End-User. Note that in some cultures, people can have multiple given names; all can be present, with the names being separated by space characters.
    family_name 	string 	Surname(s) or last name(s) of the End-User. Note that in some cultures, people can have multiple family names or no family name; all can be present, with the names being separated by space characters.
    middle_name 	string 	Middle name(s) of the End-User. Note that in some cultures, people can have multiple middle names; all can be present, with the names being separated by space characters. Also note that in some cultures, middle names are not used.
    nickname 	string 	Casual name of the End-User that may or may not be the same as the given_name. For instance, a nickname value of Mike might be returned alongside a given_name value of Michael.
    preferred_username 	string 	Shorthand name by which the End-User wishes to be referred to at the RP, such as janedoe or j.doe. This value MAY be any valid JSON string including special characters such as @, /, or whitespace. The RP MUST NOT rely upon this value being unique, as discussed in Section 5.7.
    profile 	string 	URL of the End-User's profile page. The contents of this Web page SHOULD be about the End-User.
    picture 	string 	URL of the End-User's profile picture. This URL MUST refer to an image file (for example, a PNG, JPEG, or GIF image file), rather than to a Web page containing an image. Note that this URL SHOULD specifically reference a profile photo of the End-User suitable for displaying when describing the End-User, rather than an arbitrary photo taken by the End-User.
    website 	string 	URL of the End-User's Web page or blog. This Web page SHOULD contain information published by the End-User or an organization that the End-User is affiliated with.
    email 	string 	End-User's preferred e-mail address. Its value MUST conform to the RFC 5322 [RFC5322] addr-spec syntax. The RP MUST NOT rely upon this value being unique, as discussed in Section 5.7.
    email_verified 	boolean 	True if the End-User's e-mail address has been verified; otherwise false. When this Claim Value is true, this means that the OP took affirmative steps to ensure that this e-mail address was controlled by the End-User at the time the verification was performed. The means by which an e-mail address is verified is context specific, and dependent upon the trust framework or contractual agreements within which the parties are operating.
    gender 	string 	End-User's gender. Values defined by this specification are female and male. Other values MAY be used when neither of the defined values are applicable.
    birthdate 	string 	End-User's birthday, represented as an ISO 8601-1 [ISO8601‑1] YYYY-MM-DD format. The year MAY be 0000, indicating that it is omitted. To represent only the year, YYYY format is allowed. Note that depending on the underlying platform's date related function, providing just year can result in varying month and day, so the implementers need to take this factor into account to correctly process the dates.
    zoneinfo 	string 	String from IANA Time Zone Database [IANA.time‑zones] representing the End-User's time zone. For example, Europe/Paris or America/Los_Angeles.
    locale 	string 	End-User's locale, represented as a BCP47 [RFC5646] language tag. This is typically an ISO 639 Alpha-2 [ISO639] language code in lowercase and an ISO 3166-1 Alpha-2 [ISO3166‑1] country code in uppercase, separated by a dash. For example, en-US or fr-CA. As a compatibility note, some implementations have used an underscore as the separator rather than a dash, for example, en_US; Relying Parties MAY choose to accept this locale syntax as well.
    phone_number 	string 	End-User's preferred telephone number. E.164 [E.164] is RECOMMENDED as the format of this Claim, for example, +**************** or +56 (2) 687 2400. If the phone number contains an extension, it is RECOMMENDED that the extension be represented using the RFC 3966 [RFC3966] extension syntax, for example, +****************;ext=5678.
    phone_number_verified 	boolean 	True if the End-User's phone number has been verified; otherwise false. When this Claim Value is true, this means that the OP took affirmative steps to ensure that this phone number was controlled by the End-User at the time the verification was performed. The means by which a phone number is verified is context specific, and dependent upon the trust framework or contractual agreements within which the parties are operating. When true, the phone_number Claim MUST be in E.164 format and any extensions MUST be represented in RFC 3966 format.
    address 	JSON object 	End-User's preferred postal address. The value of the address member is a JSON [RFC8259] structure containing some or all of the members defined in Section 5.1.1.
    updated_at 	number 	Time the End-User's information was last updated. Its value is a JSON number representing the number of seconds from 1970-01-01T00:00:00Z as measured in UTC until the date/time. 
*/
