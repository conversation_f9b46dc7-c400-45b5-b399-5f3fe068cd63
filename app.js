import logger from 'medcommon/logger.js';
import config from 'medcommon/config.js';
import AppExpressServer from './app-express-server.js';
import PatientModel from './api/models/patient.js';

const niceBackend = new AppExpressServer({});

process.on("SIGINT", async () => {
  niceBackend
    .stop()
    .then(() => process.exit(0))
    .catch((error) => {
      logger.error(error);
      process.exit(1);
    });
});
//#region questi process.on sono opzionali vedere se tenere...
process.on('uncaughtException', (err) => {
  logger.error('Errore non gestito:%O', err);
  // opzionalmente chiudere il processo:
  // process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Promise rifiutata senza catch:%O', reason);
});
//#endregion

process.on('exit', code => {
  //SMRACH: meglio tenere perchè sotto docker aiuta in caso di problemi.
  logger.warn(`Process exiting with code ${code}`);
});

(async () => {
  try {
    config.checkEnvVariables();
    //const myList = await new PatientModel().getAll();
    //const myList = await new PatientModel().findFree(['B%'], 'LAST_NAME LIKE :1');
    await niceBackend.start();
  } catch (error) {
    logger.error(error);
    process.exit(1);
  }
})();
