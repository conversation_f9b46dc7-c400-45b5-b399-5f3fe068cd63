import fs from 'fs'
import logger from 'medcommon/logger'

import moment from 'moment-timezone';

// Imposta il fuso orario locale
// TODO: fixme - check env
moment.tz.setDefault('Europe/Rome'); // Sostituisci con il tuo fuso orario



function loadTranslations(locale) {
  const filePath = './web/locales/' + locale + '.json'
  logger.debug("loadTranslations:%s", locale);
  try {
    return JSON.parse(fs.readFileSync(filePath, 'utf-8'))
  } catch {
    return {}
  }
}

function checkTranslations(locale) {
  const filePath = './web/locales/' + locale + '.json'
  logger.debug("checkTranslations:%s", locale);
  return fs.existsSync(filePath);
}

export function translationMiddleware(req, res, next) {
  let lang = req.query.lang || req.headers['accept-language']?.split(',')[0] || 'en'
  if (!checkTranslations(lang)) {
    lang = lang.split('-')[0]; 
  }
  if (!checkTranslations(lang)) {
    lang = 'en';
  }
  const translations = loadTranslations(lang)
  res.locals.lang = lang
  req.lang = lang
  
  // Updated translation function to support nested keys
  res.locals.t = function (key, ...vars) {
    // Get nested value using bracket notation (e.g., 'breadcrumbs.patient.search' -> translations['breadcrumbs']['patient']['search'])
    const getValue = (obj, path) => {
      if (typeof path === 'string' && path.includes('.')) {
        const keys = path.split('.');
        for (let i = 0; i < keys.length; i++) {
          if (obj == null) return undefined;
          obj = obj[keys[i]];
        }
        return obj;
      } else {
        return obj[path];
      }
    }
    logger.debug("getValue:%s", key);
    let value = getValue(translations, key);
    let str = value !== undefined ? value : key;
    // Replace variables, vars being a list
    for (const v of vars) {
      logger.debug("replace:%s", v);
      // replace once
      str = str.replace('%s', v);
    }
    str = str.replace(/\n/g, '<br/>');
    return str
  }
  
  // Update uppercase function to use the new t function
  res.locals.tup = (key, vars = {}) => res.locals.t(key, vars).toUpperCase();

  res.locals.locDate = (data) => moment(data).format(res.locals.t('dateFormat'));
  res.locals.locDateTime = (data) => moment(data).format(res.locals.t('dateTimeFormat'));
  res.locals.locDateTimeSeconds = (data) => moment(data).format(res.locals.t('dateTimeSecondsFormat'));

  next()
}
