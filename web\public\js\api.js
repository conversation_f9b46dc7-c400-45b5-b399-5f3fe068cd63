class ClearApi {
  constructor() {
    this.baseURL = '/api'
  }

  async getPatients() {
    const response = await fetch(this.baseURL + '/patients', {
      headers: {
        'Content-Type': 'application/json',
      },
    })
    if (!response.ok) {
      throw new Error('Failed to get patients')
    }
    return await response.json()
  }

  async deletePatient(patientId) {
    const response = await fetch(this.baseURL + '/patients/' + patientId, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    if (!response.ok) {
      throw new Error('Failed to delete patient')
    }
    return await response.json()
  }
}
export const Api = new ClearApi()
