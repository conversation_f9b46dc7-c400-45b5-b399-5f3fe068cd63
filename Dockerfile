FROM ghcr.io/oracle/oraclelinux8-instantclient:19
#FROM node:18-alpine
#FROM registry.medarchiver.com/node18-instantclient19:1.1

#installo node...
RUN yum install -y curl 
RUN curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
RUN yum install -y nodejs
RUN node -v && npm -v
RUN yum clean all

#procedo con il solito.
WORKDIR /app

COPY . .

RUN npm install || cat npm-debug.log || true
#RUN npm install @tailwindcss/cli@4.1.7
#RUN npm install nodemon --location=global
#RUN node swagger

RUN npm run css:build

CMD [ "node", "app.js" ]

