import { AbstractDbCrudModel } from "cruder/abstractCrud";
import <PERSON><PERSON> from "joi";

class MapResourceModel extends AbstractDbCrudModel {
  static schema = {
    schemaName: "NICE",
    tableName: "MAP_RESOURCES",
    keys: ["ID_MAP"],
    fields: ["ID_RESOURCE"],
    mapping: {
      ID_MAP: "idMap",
      ID_RESOURCE: "idResource",
    },
    joi: Joi.object({}),
  };
  constructor(data = {}, dbType = "oracle") {
    super(data, dbType);
  }
}
export default MapResourceModel;
