<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("ecgPage.heading") %> <% icon = "fa-solid fa-heartbeat" %> <%- include('components/head') %>
    <style>
      #magnifierCanvas {
        position: absolute;
        pointer-events: none;
        border: 2px solid #333;
        display: none;
      }
    </style>
  </head>
  <body>
    <%- include('components/header') %>
    <div class="card w-full rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between cursor-pointer overflow-hidden">
      <div class="flex items-left w-screen px-5 py-6">
        <p>ID ECG: 1</p>
        <p>&nbsp;</p>
        <p>DATA: dd-mm-yyyy HH-MM:SS <%-include('components/badge-text', {badgeColor:'bg-darkred-light', badgeText:'fix-me'})%></p>
      </div>
    </div>
    <div id="ecgDiv"></div>
    <!--<main class="relative h-screen">-->
    <!--<div class="flex items-center justify-center h-full">-->
    <canvas id="ecgCanvas" width="1000" height="1550" class="block"></canvas>
    <div id="offset-panel" class="bg-white shadow-sm rounded p-1 z-20 hidden">
      <input
        class="w-full h-6 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-500"
        type="range"
        id="offsetSlider"
        min="0"
        max="0"
        step="500"
        value="0"
      />
    </div>
    <!--</div>-->
    <div id="overlay" class="absolute inset-0 bg-gray-800 bg-opacity-70 flex flex-col items-center justify-center z-10">
      <div class="w-24 h-24 border-4 border-gray-300 border-t-darkred rounded-full animate-spin"></div>
      <!-- Testo di caricamento -->
      <p id="loadingText" class="text-white text-lg font-sans mt-4"><%= t('ecg.loading.message') %></p>
    </div>
    <!--</main>-->
    <script>
      const canvas = document.getElementById("ecgCanvas");
      const offsetPanel = document.getElementById("offset-panel");
      const offsetSlider = document.getElementById("offsetSlider");
      const overlay = document.getElementById("overlay");

      const rect = canvas.getBoundingClientRect();
      overlay.style.position = "absolute";
      overlay.style.left = `${rect.left + window.scrollX}px`;
      overlay.style.top = `${rect.top + window.scrollY}px`;
      overlay.style.width = `${rect.width}px`;
      overlay.style.height = `${rect.height}px`;

      offsetPanel.style.position = "absolute";
      offsetPanel.style.left = `10px`;
      offsetPanel.style.width = `1060px`;
      offsetPanel.style.top = `1850px`;
    </script>
    <script src="/js/lil-gui@0.20"></script>
    <script>
      var GUI = lil.GUI;
      const ecgDiv = document.getElementById("ecgDiv");
      const gui = new GUI({ container: ecgDiv, title: "Settings" });
      obj = {
        gridFineColor: "#111111",
        gridBoldColor: "#444444",
        gridStrokeStyle: "#000000",
        strokeStyle: "#00ff46"
      };
      gui.addColor(obj, "gridBoldColor").onChange((value) => {
        window.drawer.setBoldColor(value);
      });
      gui.addColor(obj, "gridFineColor").onChange((value) => {
        window.drawer.setFineColor(value);
      });
      gui.addColor(obj, "gridStrokeStyle").onChange((value) => {
        window.drawer.setGridStrokeStyle(value);
      });
      gui.addColor(obj, "strokeStyle").onChange((value) => {
        window.drawer.setStrokeStyle(value);
      });
      
      gui.open(false);
    </script>
    <script type="module">
      import { EcgData, EcgDrawer } from "/js/ecg-lib.js";

      const drawer = new EcgDrawer(canvas, {
        cols: 1,
        rows: 12,
        canvasWidth: 1080,
        canvasHeight: 1550,
        leadWidthMm: 280,
        leadHeightMm: 20,
        strokeWidth: 1.5,
        //showMm: true,
        //gridStrokeStyle: '#fefefe',
        gridFineColor: "#111111",
        gridBoldColor: "#444444",
        gridStrokeStyle: "#000000",
        strokeStyle: "#00ff46",
      });
      window.drawer = drawer;
      const ecgUrl = "<%=ecgUrl%>";

      window.onload = async () => {
        overlay.style.display = "flex";
        offsetPanel.style.display = "none";
        offsetSlider.value = 0;
        await new Promise((resolve) => requestAnimationFrame(resolve));
        let readData = await loadEcgFromUrl(ecgUrl); //'/12345A2.csv');
        if (readData) {
          drawer.setEcgData(readData);
          offsetSlider.max = drawer.getMaxDataOffset();
          if (ecgUrl.endsWith(".ecg")) {
            drawer.setSamplesPerSecond = 1000;
          }
        }
        overlay.style.display = "none";
        offsetPanel.style.display = "flex";
        //drawer.setScalingFactor(1.08);

        drawer.redraw();
      };
      //In caso di resize, ridisegno tutto.
      let resizeTimeout = null;
      window.addEventListener("resize", () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          drawer.redraw();
        }, 100); // 100ms dopo l'ultimo resize
      });

      offsetSlider.addEventListener("input", function (e) {
        drawer.setOffsetIndex(parseInt(this.value));
      });

      /** Carica da remoto i dati ecg e restituisce un oggetto EcgData.  */
      async function loadEcgFromUrl(url) {
        //await new Promise((resolve) => setTimeout(resolve, 500));
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
          const data = await response.text();
          const ecgData = new EcgData();
          if (url.endsWith(".csv")) {
            ecgData.fromCsvData(data);
          }
          if (url.endsWith(".ecg")) {
            ecgData.fromEcgData(data);
          }
          return ecgData;
        } catch (error) {
          console.error("Failed to load ECG data:", error);
          return null;
        }
      }
    </script>
  </body>
</html>
