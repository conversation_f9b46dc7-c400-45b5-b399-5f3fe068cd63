<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("ecgPage.heading") %> <% icon = "fa-solid fa-heartbeat" %> <%- include('components/head') %>
    <style>
      #magnifierCanvas {
        position: absolute;
        pointer-events: none;
        border: 2px solid #333;
        display: none;
      }
    </style>
  </head>
  <body>
    <%- include('components/header') %>
    <div class="flex flex-col w-full rounded-lg bg-darkgray p-2 m-2">
      <div class="flex flex-row w-full justify-between text-white p-2 m-2">
        <div class="flex">
          <p class="text-2xl"></p>
          <p class="text-2xl"><%=ecgData.resourceNotes%>&nbsp;-&nbsp;<%=locDateTime(ecgData.resourceDate)%></p>
        </div>
        <div class="flex">
          <p class="text-2xl">
            <%- include('./components/badge-text', { badgeColor: ecgData.resourceTag === 'before' ? 'bg-green-500' : ecgData.resourceTag === 'after' ?
            'bg-yellow-500' : 'bg-red-500', badgeText: t('badge.' + ecgData.resourceTag) }) %>
          </p>
        </div>
      </div>
    </div>
    <div id="ecgDiv" class="flex"></div>
    <canvas id="ecgCanvas" class="block"></canvas>
    <div id="offset-panel" class="bg-white shadow-sm rounded px-2 m-2 z-20 w-full hidden">
      <input
        class="w-full h-6 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-500 px-2"
        type="range"
        id="offsetSlider"
        min="0"
        max="0"
        step="500"
        value="0"
      />
    </div>

    <div id="overlay" class="absolute inset-0 bg-gray-800 bg-opacity-70 flex flex-col items-center justify-center z-10">
      <div class="w-24 h-24 border-4 border-gray-300 border-t-darkred rounded-full animate-spin"></div>
      <!-- Testo di caricamento -->
      <p id="loadingText" class="text-white text-lg font-sans mt-4"><%= t('ecg.loading.message') %></p>
    </div>
    <script>
      const canvas = document.getElementById("ecgCanvas");
      const offsetPanel = document.getElementById("offset-panel");
      const offsetSlider = document.getElementById("offsetSlider");
      const overlay = document.getElementById("overlay");

      const rect = canvas.getBoundingClientRect();
      overlay.style.position = "absolute";
      overlay.style.left = `${rect.left + window.scrollX}px`;
      overlay.style.top = `${rect.top + window.scrollY}px`;
      overlay.style.width = `${rect.width}px`;
      overlay.style.height = `${rect.height}px`;

      let readEcgData = undefined;

      function exportCsv() {
        const csvText = readEcgData.toCsv();
        console.log(csvText);
      }
    </script>
    <script src="/js/lil-gui@0.20"></script>
    <script>
      var GUI = lil.GUI;
      const ecgDiv = document.getElementById("ecgDiv");
      const gui = new GUI({ container: ecgDiv, title: "Settings" });
      obj = {
        gridBoldColor: "#444444",
        showMMgrid: false,
        gridFineColor: "#575757",
        gridStrokeStyle: "#000000",
        strokeStyle: "#00ff46",
        scalingFactor: 1,
        resetValues: function () {
          window.drawer.setBoldColor("#444444");
          window.drawer.setFineColor("#111111");
          window.drawer.setGridStrokeStyle("#000000");
          window.drawer.setStrokeStyle("#00ff46");
        },
        paddingMM: 10,
      };
      gui.addColor(obj, "gridBoldColor").onChange((value) => {
        window.drawer.setBoldColor(value);
      });
      gui.addColor(obj, "gridFineColor").onChange((value) => {
        window.drawer.setFineColor(value);
      });
      gui.addColor(obj, "gridStrokeStyle").onChange((value) => {
        window.drawer.setGridStrokeStyle(value);
      });
      gui.addColor(obj, "strokeStyle").onChange((value) => {
        window.drawer.setStrokeStyle(value);
      });
      gui.add(obj, "showMMgrid").onChange((value) => {
        window.drawer.setSmallGridVisible(value);
      });
      gui.add(obj, "scalingFactor", 0.7, 3, 0.01).onChange((value) => {
        window.drawer.setScalingFactor(value);
      });
      gui.add(obj, "paddingMM", -20, 20, 0.1).onChange((value) => {
        window.drawer.setPaddingMM(value);
      });
      gui.add(obj, "resetValues");
      gui.open(false);
    </script>
    <script type="module">
      import { EcgData, EcgDrawer } from "/js/ecg-lib.js";

      const drawer = new EcgDrawer(canvas, {
        cols: 1,
        rows: 12,
        canvasWidth: 1050,
        canvasHeight: 1400,
        leadWidthMm: 280,
        leadHeightMm: 20,
        strokeWidth: 1.5,
        //showMm: true,
        //gridStrokeStyle: '#fefefe',
        gridFineColor: "#575757",
        gridBoldColor: "#444444",
        gridStrokeStyle: "#000000",
        strokeStyle: "#00ff46",
      });
      window.drawer = drawer;
      const ecgUrl = "<%=ecgUrl%>";

      window.onload = async () => {
        overlay.style.display = "flex";
        offsetPanel.style.display = "none";
        offsetSlider.value = 0;
        await new Promise((resolve) => requestAnimationFrame(resolve));
        console.log("ECG URL:", ecgUrl);
        let readData = await loadEcgFromUrl(ecgUrl); //'/12345A2.csv');
        if (ecgUrl.endsWith(".ecg")) {
          drawer.setSamplesPerSecond(1000);
        }
        readEcgData = readData;
        if (readData) {
          drawer.setEcgData(readData);
          offsetSlider.max = drawer.getMaxDataOffset();
        }
        overlay.style.display = "none";
        offsetPanel.style.display = "flex";
        //drawer.setScalingFactor(1.08);

        drawer.redraw();
      };
      //In caso di resize, ridisegno tutto.
      let resizeTimeout = null;
      window.addEventListener("resize", () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          drawer.redraw();
        }, 100); // 100ms dopo l'ultimo resize
      });

      offsetSlider.addEventListener("input", function (e) {
        drawer.setOffsetIndex(parseInt(this.value));
      });

      /** Carica da remoto i dati ecg e restituisce un oggetto EcgData.  */
      async function loadEcgFromUrl(url) {
        //await new Promise((resolve) => setTimeout(resolve, 500));
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
          const data = await response.text();
          const ecgData = new EcgData();
          if (url.endsWith(".csv")) {
            ecgData.fromCsvData(data);
          }
          if (url.endsWith(".ecg")) {
            ecgData.fromEcgData(data);
          }
          return ecgData;
        } catch (error) {
          console.error("Failed to load ECG data:", error);
          return null;
        }
      }
    </script>
  </body>
</html>
