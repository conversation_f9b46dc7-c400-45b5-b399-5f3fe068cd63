const crypto = require("crypto");
const config = require("config");
const algorithm = "aes-256-ctr";
const secretKey = config.secretKey || ""; //fill-from-env!!
const iv = crypto.randomBytes(16);

const encrypt = (text) => {
  const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
  const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);
  return {
    iv: iv.toString("hex"),
    content: encrypted.toString("hex"),
  };
};
const decrypt = (hash) => {
  const decipher = crypto.createDecipheriv(algorithm, secretKey, Buffer.from(hash.iv, "hex"));
  const decrpyted = Buffer.concat([decipher.update(Buffer.from(hash.content, "hex")), decipher.final()]);
  return decrpyted.toString();
};
const decryptToken = async (req, res) => {
  /*
      #swagger.tags = ['Crypto']
  */
  try {
    let token = req.query.token;
    let buff = new Buffer.from(token, "base64");
    let cryptedString = buff.toString("ascii");
    let cryptedBody = JSON.parse(cryptedString);
    var decryptedBody = JSON.parse(decrypt(cryptedBody));
    res.status(200).json(decryptedBody);
  } catch (error) {
    res.status(500).json({ error: "cannot decrypt auth data" });
  }
};
module.exports = {
  encrypt,
  decrypt,
  decryptToken,
};
