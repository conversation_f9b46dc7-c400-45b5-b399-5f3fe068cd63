import { AbstractDbCrudModel } from "cruder/abstractCrud";
import  <PERSON><PERSON>  from 'joi';

class NiceUserModel extends AbstractDbCrudModel {
    static schema = {
        schemaName: "NICE",
        tableName: "NICE_USERS",
        keys: ["ID"],
        fields: ["USER_NAME", "FIRST_NAME", "LAST_NAME", "USER_PASSWORD", "LAST_LOGIN", "SALT"],
        mapping: {
            ID: "id",
            USER_NAME: "username",
            FIRST_NAME: "firstName",
            LAST_NAME: "lastName",
            USER_PASSWORD: "password",
            LAST_LOGIN: "lastLogin",
            SALT: "salt",
        },
        joi: Joi.object({
            USER_NAME: Joi.string().max(50).required(),
            FIRST_NAME: Joi.string().max(50).required(),
            LAST_NAME: Joi.string().max(50).required(),
            USER_PASSWORD: Joi.string().max(50).required(),
            SALT: Joi.string().max(50).required(),
        }),
    };
    constructor(data = {}, dbType = "oracle") {
        super(data, dbType);
    }
}
export default NiceUserModel;
