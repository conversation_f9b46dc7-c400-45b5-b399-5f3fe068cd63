"use strict";
const oracle = require("medcommon/oracle");
const logger = require("medcommon/logger");
const DEFAULT_PRUNE_INTERVAL_IN_SECONDS = 60 * 30; //Default ogni mezzora.
let elkData = {
    "log.origin.file.name": "medcommon/oracleSessionStore.js",
};
/** 
 * @returns {ExpressSessionStore}
 */
module.exports.OAuth2ExpiredPruner =
    class OAuth2ExpiredPruner {
        /** @type {false|number} */
        #pruneSessionInterval;
        /** @type {PGStorePruneDelayRandomizer|undefined} */
        #pruneSessionRandomizedInterval;
        #dbConnection;

        /** @param {} options */
        constructor(options = {}) {
            if (options.elk) {
                elkData = {
                    ...elkData,
                    ...options.elk,
                };
            }
            logger.info("OAuth2ExpiredPruner(%s)", options, elkData);
            if (options.pruneSessionInterval === false) {
                this.#pruneSessionInterval = false;
            } else {
                this.#pruneSessionInterval = (options.pruneSessionInterval || DEFAULT_PRUNE_INTERVAL_IN_SECONDS) * 1000;
            }
            this.closed = false;
            this.#initPruneTimer();
        }

        /**
         * @access public
         * @returns {Promise<void>}
         */
        async close() {
            logger.debug("close", elkData);
            this.closed = true;
            this.#clearPruneTimer();
            if (this.#dbConnection) {
                this.#dbConnection.close();
            }
        }

        async #initPruneTimer() {
            if (!this.#dbConnection) {
                this.#dbConnection = await oracle.getDBConnection().catch((error) => {
                    // Questo è normalmente vuoto perchè se non va la prima volta, 
                    // verrà fatto un tentativo la prossima volta, quando sarà attiva verrà poi riutilizzata
                });
            }
            if (this.#pruneSessionInterval && !this.pruneTimer) {
                logger.debug("#rearmPruneTimer", elkData);
                const delay = this.#pruneSessionRandomizedInterval ? this.#pruneSessionRandomizedInterval(this.#pruneSessionInterval) : this.#pruneSessionInterval;
                this.pruneTimer = setTimeout(() => {
                    this.pruneExpired();
                }, delay);
                this.pruneTimer.unref();
            }
        }

        #clearPruneTimer() {
            if (this.pruneTimer) {
                logger.debug("#clearPruneTimer", elkData);
                clearTimeout(this.pruneTimer);
                this.pruneTimer = undefined;
            }
        }

        /**
         * Does garbage collection for expired session in the database
         * @returns {void}
         * @access public
         */
        async pruneExpired() {
            logger.verbose("pruningOAuth2Expired", elkData);
            this.#clearPruneTimer();
            try {
                let queryAT = `DELETE FROM OAUTH2.ACCESS_TOKENS WHERE expires < current_timestamp`;
                await oracle.dbRowsAffected(queryAT, [], { connection: this.#dbConnection, doCommit: true, elk: elkData });
                let queryRT = `DELETE FROM OAUTH2.REFRESH_TOKENS WHERE expires < current_timestamp`;
                await oracle.dbRowsAffected(queryRT, [], { connection: this.#dbConnection, doCommit: true, elk: elkData });
                let queryAC = `DELETE FROM OAUTH2.AUTHORIZATION_CODES WHERE expires < current_timestamp`;
                await oracle.dbRowsAffected(queryAC, [], { connection: this.#dbConnection, doCommit: true, elk: elkData });
                let queryDC = `DELETE FROM OAUTH2.DEVICE_CODES WHERE expires < current_timestamp`;
                await oracle.dbRowsAffected(queryDC, [], { connection: this.#dbConnection, doCommit: true, elk: elkData });
                logger.info("Puliti records delle tabelle OAuth2 expired", elkData);
            } catch (error) {
                logger.error("ERROR on pruneSessions: %O", error, { error, ...elkData });
            }
            this.#initPruneTimer();
        }
    };