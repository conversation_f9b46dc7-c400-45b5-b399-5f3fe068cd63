<style>
  ::backdrop {
    background-color: rgba(0, 0, 0, 0.7);
  }
</style>

<dialog id="insertKeybindDialog" class="bg-[#1e1e1e] text-white rounded-lg shadow-lg w-[600px] fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
  <div class="flex flex-row justify-center p-4 pt-4">
    <div class="flex w-full text-4xl">
      <%=t('patient.addNew')%>
    </div>
    <button onclick="closeDialog()" class="text-lg text-white hover:text-gray-300">
      <i class="fa fa-times text-5xl"></i>
    </button>
  </div>
  <div class="p-4">
    <p class="text-2xl text-center"><%=t('patient.addConfirmation')%></p>
  </div>

  <!-- Action Buttons Row -->
  <div class="flex flex-row-reverse gap-6 p-4">
    <button type="button" onclick="confirmAction()" class="flex flex-1 px-4 py-2 rounded bg-darkred hover:bg-darkred-light">
      <%= t('confirm') %>
    </button>
    <button type="button" onclick="closeDialog()" class="flex flex-1 px-4 py-2 bg-gray-600 rounded hover:bg-gray-700">
      <%= t('cancel') %>
    </button>
  </div>
</dialog>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // listen for Insert key to show dialog
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Insert' || event.key === 'F2') {
                const dialog = document.getElementById('insertKeybindDialog');
                dialog.showModal();
            }
        });
    });
    function closeDialog() {
    const dialog = document.getElementById('insertKeybindDialog');
    dialog.close();
    }

    function confirmAction() {
    window.location.href = '/patients/new';
    }
</script>
