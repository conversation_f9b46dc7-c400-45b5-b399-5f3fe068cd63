<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>" class="h-screen">
  <head>
    <% title = t("patient.heading") %>
    <% icon = "fa-solid fa-user" %>
    <%- include('components/head') %>
  </head>
  <body class="h-screen">
    <%- include('components/header') %>
    <div class="flex flex-row items-center gap-x-12 w-full text-white px-4 pb-4">
      <!-- Edit and Delete buttons styled to match the UI -->
      <button class="flex-1 bg-zinc-700 hover:bg-zinc-600 text-white font-bold py-2 px-4 rounded flex items-center justify-center gap-2" onclick="window.location.href='/patients/<%=patient.id%>/edit'">
        <i class="fa-solid fa-pen"></i>
        <%= t("patient.edit") %>
      </button>
      <button class="flex-1 bg-red-700 hover:bg-red-600 text-white font-bold py-2 px-4 rounded flex items-center justify-center gap-2" onclick="deletePatient()">
        <i class="fa-solid fa-trash"></i>
        <%= t("patient.delete") %>
      </button>
    </div>
    <main class="h-full w-full overflow-y-auto">
      <div class="h-full w-full overflow-x-hidden bg-zinc-800">
      <!-- Tabs -->
      <div class="flex space-x-2 border-t border-gray-700">
        <button class="tab-link px-8 py-4 text-3xl font-semibold text-white bg-zinc-700 border-t-2 border-darkred" data-tab="resources">
          <%= t("resources.heading") %>
        </button>
        <a href="/patients/<%=patient.id %>/activationMaps">
        <button class="tab-link px-8 py-4 text-3xl font-semibold text-gray-300 border-t-2 border-transparent hover:text-white" data-tab="ecgMaps">
          <%= t("activationMaps.heading") %>
        </button>
        </a> 
      </div>
      <div>&nbsp;</div>
      <!-- Add new - Card -->
      <div class="flex flex-wrap gap-6 justify-center content-center items-center">
        <!-- Fine Add new - Card -->
        <a href="#" id="addResourceButton">
          <div class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between cursor-pointer overflow-hidden" data-id="">
              <div class="bg-[#2c2c2c] rounded-t-lg text-center">
          <div class="font-semibold uppercase tracking-wide p-2 text-gray-200 text-3xl">&nbsp;</div>
              </div>
              <div class="text-xl leading-tight flex flex-col">
          <p class="text-center"><i class="fa-solid fa-plus text-6xl py-2"></i></p>
          <p class="text-center text-3xl"><%=t('resources.addNew')%></p>
              </div>
              <div class="flex justify-end m-2">&nbsp;</div>
          </div>
        </a>
        <% if (list && list.length > 0) { %> <% list.forEach(resource => { %>
          <%if (resource.idResourceType == 1) {%>
            <a href="/patients/<%=patient.id%>/ecg?resid=<%=resource.id%>">
          <%} else {%>
            <a href="/patients/<%=patient.id%>/dicom?resid=<%=resource.id%>">
          <%}%>
          <div class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between cursor-pointer overflow-hidden" data-id="<%= resource.id %>">
            <div class="bg-[#2c2c2c] rounded-t-lg text-center">
              <div class="font-semibold uppercase tracking-wide p-2 text-gray-200 text-3xl"><%= t(resource.idResourceType == 1 ? 'ecg' : 'ctScan') %></div>
            </div>
            <div class="text-sm leading-tight flex flex-col justify-between">
              <p class="text-center p-4 text-3xl">
                <%= locDateTime(resource.resourceDate)%>
              </p>
              <p class="text-center p-4 text-xl">
                <%= resource.resourceNotes%>
              </p>
            </div>
            <div class="flex justify-end m-2">
              <% let currColor = resource.resourceTag === 'before' ? 'bg-green-500' : resource.resourceTag === 'after' ? 'bg-yellow-500' : 'bg-red-500'; %>
              <%- include('./components/badge-text', {badgeColor:currColor, badgeText: t('badge.' + resource.resourceTag)})  %>
            </div>
          </div>
          </a>
        <% }); } %> 
      </div>
    </div>
    </main>
    <%- include('components/add-resource-modal') %>

    <%- include('components/delete-dialog') %>
    <script>
      function deletePatient() {
        const deleteModal = document.getElementById('deleteModal');
        deleteModal.showModal();
      }
    </script>
  </body>
</html>
