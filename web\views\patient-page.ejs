<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>" class="h-screen">
  <head>
    <% title = t("patient.heading") %>
    <% icon = "fa-solid fa-user" %>
    <%- include('components/head') %>
  </head>
  <body class="h-screen">
    <%- include('components/header') %>
    <%- include('components/patient-btns-editdelete') %>
    <%- include('components/patient-tabs') %>
    <main class="h-full w-full overflow-y-auto">
      <div class="h-full w-full overflow-x-hidden bg-zinc-800">
      <!-- Add new - Card -->
      <div class="flex flex-wrap gap-6 justify-center content-center items-center">
        <!-- Fine Add new - Card -->
        <a href="#" id="addResourceButton">
          <div class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between cursor-pointer overflow-hidden" data-id="">
              <div class="bg-[#2c2c2c] rounded-t-lg text-center">
          <div class="font-semibold uppercase tracking-wide p-2 text-gray-200 text-3xl">&nbsp;</div>
              </div>
              <div class="text-xl leading-tight flex flex-col">
          <p class="text-center"><i class="fa-solid fa-plus text-6xl py-2"></i></p>
          <p class="text-center text-3xl"><%=t('resources.addNew')%></p>
              </div>
              <div class="flex justify-end m-2">&nbsp;</div>
          </div>
        </a>
        <% if (list && list.length > 0) { %> <% list.forEach(resource => { %>
          <div class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between overflow-hidden" data-id="<%= resource.id %>">
            <div class="bg-[#2c2c2c] rounded-t-lg flex items-center justify-between p-1">
              <div class="font-semibold uppercase tracking-wide p-1 text-gray-200 text-3xl">
                <span class="mr-2"><%= t(resource.idResourceType == 1 ? 'ecg' : 'ctScan') %></span>
              </div>
              <div class="px-3">
                <button onclick="" class="text-white hover:text-gray-300">
                  <i class="fa-solid fa-ellipsis-vertical text-2xl"></i>
                </button>
              </div>
            </div>
          <%if (resource.idResourceType == 1) {%>
            <a href="/patients/<%=patient.id%>/ecg?resid=<%=resource.id%>" class="cursor-pointer">
          <%} else {%>
            <a href="/patients/<%=patient.id%>/dicom?resid=<%=resource.id%>" class="cursor-pointer">
          <%}%>
            <div class="flex flex-col h-[270px] justify-evenly">
              <p class="p-4 text-3xl text-center">
                <%= locDateTime(resource.resourceDate)%>
              </p>
              <p class="text-center p-4 text-xl">
                <%= resource.resourceNotes%>
              </p>
              <div class="flex justify-end m-2">
                <% let currColor = resource.resourceTag === 'before' ? 'bg-green-500' : resource.resourceTag === 'after' ? 'bg-yellow-500' : 'bg-red-500'; %>
                <%- include('./components/badge-text', {badgeColor:currColor, badgeText: t('badge.' + resource.resourceTag)})  %>
              </div>
            </div></a>
          </div>
        <% }); } %> 
      </div>
    </div>
    </main>
    <%- include('components/add-resource-modal') %>

    <%- include('components/delete-dialog') %>
    <script>
      function deletePatient() {
        const deleteModal = document.getElementById('deleteModal');
        deleteModal.showModal();
      }
    </script>
  </body>
</html>
