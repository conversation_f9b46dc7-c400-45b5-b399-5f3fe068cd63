CREATE SEQUENCE  "NICE"."S_MAPS_ID"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 61;
CREATE SEQUENCE  "NICE"."S_NICE_USERS"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1;
CREATE SEQUENCE  "NICE"."S_PATIENTS_ID"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 41;
CREATE SEQUENCE  "NICE"."S_PROCESSING_JOBS_ID"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 41;
CREATE SEQUENCE  "NICE"."S_RESOURCE_LK_ID"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1;
CREATE SEQUENCE  "NICE"."S_RESOURCES_ID"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 161;
CREATE SEQUENCE  "NICE"."S_SIMULATIONS_ID"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 41;

CREATE TABLE "NICE"."COOKIE_SESSIONS" (	
  "SID" VARCHAR2(32 BYTE), 
	"EXPIRE" TIMESTAMP (6), 
	"SESS" VARCHAR2(4000 BYTE)
);
CREATE TABLE "NICE"."MAP_RESOURCES" (	
  "ID_MAP" NUMBER(38,0), 
	"ID_RESOURCE" NUMBER(38,0)
);
CREATE TABLE "NICE"."MAPS" (	
  "ID" NUMBER, 
	"DESCRIPTION" VARCHAR2(1000 BYTE), 
	"ID_USER_CREATE" NUMBER, 
	"CREATION_DATE" DATE, 
	"STATUS" NUMBER DEFAULT 0, 
	"MAP_TAG" VARCHAR2(100 BYTE), 
	"ID_PATIENT" NUMBER, 
	"TYPE" NUMBER DEFAULT 0
);
COMMENT ON COLUMN "NICE"."MAPS"."TYPE" IS 'Tipo mappa: asse,simulation, implant, etc.';
CREATE TABLE "NICE"."NICE_USERS" (	
  "ID" NUMBER, 
	"USER_NAME" VARCHAR2(1000 BYTE), 
	"FIRST_NAME" VARCHAR2(100 BYTE), 
	"LAST_NAME" VARCHAR2(100 BYTE), 
	"USER_PASSWORD" VARCHAR2(1000 BYTE), 
	"LAST_LOGIN" DATE, 
	"SALT" VARCHAR2(100 BYTE)
);
CREATE TABLE "NICE"."PATIENTS" (
  "ID" NUMBER, 
	"CODE" VARCHAR2(100 BYTE), 
	"FIRST_NAME" VARCHAR2(200 BYTE), 
	"LAST_NAME" VARCHAR2(200 BYTE), 
	"BIRTH_DATE" DATE, 
	"GENDER" CHAR(1 BYTE), 
	"EF" NUMBER, 
	"ESV" NUMBER, 
	"STATUS_TAG" VARCHAR2(100 BYTE), 
	"DELETED" CHAR(1 BYTE) DEFAULT 'N'
);
COMMENT ON COLUMN "NICE"."PATIENTS"."DELETED" IS 'Y o N';
CREATE TABLE "NICE"."PROCESSING_JOBS" (
  "ID" NUMBER, 
	"ID_PATIENT" NUMBER, 
	"ID_MAP" NUMBER, 
	"STAGE" VARCHAR2(100 BYTE), 
	"CREATED_AT" DATE DEFAULT sysdate, 
	"JOB_STATUS" NUMBER DEFAULT 0, 
	"ID_ECG" VARCHAR2(100 BYTE) DEFAULT 'default', 
	"JOB_DATA" VARCHAR2(1000 BYTE)
);
COMMENT ON COLUMN "NICE"."PROCESSING_JOBS"."JOB_DATA" IS 'Dati utili per esecuzione lavoro (JSON!)';
CREATE TABLE "NICE"."RESOURCE_LK" (
  "ID" NUMBER, 
	"DESCRIPTION" VARCHAR2(1000 BYTE)
);
CREATE TABLE "NICE"."RESOURCES" (
  "ID" NUMBER, 
	"ID_PATIENT" NUMBER, 
	"ID_USER_CREATE" NUMBER, 
	"ID_USER_UPDATE" NUMBER, 
	"ID_RESOURCE_TYPE" NUMBER, 
	"RESOURCE_DATE" DATE, 
	"RESOURCE_NOTES" VARCHAR2(1000 BYTE), 
	"RESOURCE_TAG" VARCHAR2(100 BYTE), 
	"RESOURCE_ORDER" NUMBER, 
	"UNIQUE_ID" VARCHAR2(1000 BYTE)
);
CREATE TABLE "NICE"."SIMULATIONS" (
  "ID" NUMBER, 
	"DESCRIPTION" VARCHAR2(100 BYTE), 
	"ID_MAP" NUMBER, 
	"ID_MAP_RESULT" NUMBER, 
	"STATUS" NUMBER DEFAULT -1, 
	"DATA" VARCHAR2(1000 BYTE), 
	"CREATED_AT" DATE DEFAULT sysdate, 
	"ID_PATIENT" NUMBER
);
CREATE OR REPLACE FORCE VIEW "NICE"."V_MAP" ("MAP_ID", "MAP_DESCRIPTION", "MAP_TAG", "MAP_DATE", "ID_RESOURCE", "ID_PATIENT", "ID_RESOURCE_TYPE", "RESOURCE_DATE", "RESOURCE_ORDER", "RESOURCE_NOTES", "RESOURCE_TAG", "UNIQUE_ID") AS 
  select 
m.id as "MAP_ID",
m.description as "MAP_DESCRIPTION",
m.map_tag as "MAP_TAG",
m.creation_date as "MAP_DATE",
r.id as "ID_RESOURCE",
m.id_patient,
r.id_resource_type,
r.resource_date,
r.resource_order,
r.resource_notes,
r.resource_tag,
r.unique_id
from 
maps m, 
map_resources mr , 
resources r 
where 
mr.id_map = m.id 
and r.id = mr.id_resource
-- per maggior sicurezza?
and r.id_patient = m.id_patient
;

CREATE OR REPLACE FORCE VIEW "NICE"."V_MAP_RESOURCES" (
  "ID_MAP", 
  "DESCRIPTION", 
  "MAP_TAG", 
  "ID_PATIENT", 
  "STATUS", 
  "ID_RESOURCE", 
  "RESOURCE_DATE", 
  "RESOURCE_NOTES", 
  "RESOURCE_TAG", 
  "RESOURCE_ORDER", 
  "ID_RESOURCE_TYPE", 
  "ID_RESOURCE_TYPE_TEXT", 
  "UNIQUE_ID") AS 
  SELECT 
    mr."ID_MAP", 
    m.description, 
    m.map_tag, 
    m.id_patient,
    m.status,  
    mr."ID_RESOURCE", 
    r.resource_date,
    r.resource_notes,
    r.resource_tag,
    r.resource_order,
    r.id_resource_type,
    rl.description AS id_resource_type_text,
    r.unique_id
  FROM map_resources mr 
  JOIN maps m ON mr.id_map = m.ID
  JOIN resources r ON mr.id_resource = r.ID
  JOIN resource_lk rl ON r.id_resource_type = rl.ID;

CREATE OR REPLACE FORCE VIEW "NICE"."V_MAP_RESOURCES_NEW" (
  "ID_MAP", "MAP_DESCRIPTION", "MAP_TAG", "ID_PATIENT", "ID_RESOURCE", "RESOURCE_DATE", "RESOURCE_NOTES", "RESOURCE_TAG", "RESOURCE_ORDER", "ID_RESOURCE_TYPE", "ID_RESOURCE_TYPE_TEXT", "UNIQUE_ID") AS 
SELECT mr."ID_MAP", m.description AS "MAP_DESCRIPTION", m.map_tag, m.id_patient, mr."ID_RESOURCE", r.resource_date,
        r.resource_notes, r.resource_tag, r.resource_order, r.id_resource_type, rl.description AS id_resource_type_text,
        r.unique_id
    FROM map_resources mr JOIN maps m ON mr.id_map = m.ID
        JOIN resources r ON mr.id_resource = r.ID
        JOIN resource_lk rl ON r.id_resource_type = rl.ID;

CREATE OR REPLACE FORCE VIEW "NICE"."V_PATIENTS" 
  ("ID", "CODE", "FIRST_NAME", "LAST_NAME", "BIRTH_DATE", "GENDER", "EF", "ESV") AS 
  select "ID","CODE","FIRST_NAME","LAST_NAME","BIRTH_DATE","GENDER","EF","ESV" from patients;

CREATE OR REPLACE FORCE VIEW "NICE"."V_PROCESSING_JOBS" 
  ("ID", "ID_PATIENT", "ID_MAP", "STAGE", "CREATED_AT", "JOB_STATUS", "ID_ECG", "JOB_DATA", "CODE_PATIENT") AS 
  select pj."ID",pj."ID_PATIENT",pj."ID_MAP",pj."STAGE",pj."CREATED_AT",pj."JOB_STATUS",pj."ID_ECG",pj."JOB_DATA", pa.code as code_patient
  from processing_jobs pj,
       patients pa
  where pj.id_patient = pa.id;

CREATE OR REPLACE FORCE VIEW "NICE"."V_RESOURCES" 
  ("ID", "ID_PATIENT", "PATIENT", "CODE", "USER_CREATE", "USER_UPDATE", "RESOURCE_TYPE_DESC", "RESOURCE_TYPE_ID", "RESOURCE_DATE", "RESOURCE_NOTES", "RESOURCE_TAG", "RESOURCE_ORDER") AS 
  select r.id,
       r.id_patient,
       p.first_name || ' ' || p.last_name as patient,
       p.code,
       u1.first_name || ' ' || u1.last_name as user_create,
       u2.first_name || ' ' || u2.last_name as user_update,
       lk.description as resource_type_desc,
       lk.id as resource_type_id,
       r.resource_date,
       r.resource_notes,
       r.resource_tag,
       r.resource_order
  from resources r,
       nice_users u1,
       nice_users u2,
       patients p,
       resource_lk lk
  where r.id_user_create = u1.id
  and r.id_user_update = u2.id
  and r.id_patient = p.id
  and r.id_resource_type = lk.id;

CREATE UNIQUE INDEX "NICE"."PK_MAPS_ID" ON "NICE"."MAPS" ("ID");
CREATE UNIQUE INDEX "NICE"."SIMULATIONS_PK" ON "NICE"."SIMULATIONS" ("ID");
CREATE UNIQUE INDEX "NICE"."PK_NICE_USERS_ID" ON "NICE"."NICE_USERS" ("ID");
CREATE UNIQUE INDEX "NICE"."PK_RESOURCE_LK_ID" ON "NICE"."RESOURCE_LK" ("ID");
CREATE UNIQUE INDEX "NICE"."IDX_COOKIE_SESSIONS" ON "NICE"."COOKIE_SESSIONS" ("SID");
CREATE UNIQUE INDEX "NICE"."PK_PATIENTS_ID" ON "NICE"."PATIENTS" ("ID");
CREATE UNIQUE INDEX "NICE"."PK_RESOURCE_ID" ON "NICE"."RESOURCES" ("ID");
CREATE UNIQUE INDEX "NICE"."PROCESSING_JOBS_PK" ON "NICE"."PROCESSING_JOBS" ("ID");
set define off;

ALTER TABLE "NICE"."RESOURCES" ADD CONSTRAINT "PK_RESOURCE_ID" PRIMARY KEY ("ID") USING INDEX "NICE"."PK_RESOURCE_ID"  ENABLE;
ALTER TABLE "NICE"."MAP_RESOURCES" MODIFY ("ID_MAP" NOT NULL ENABLE);
ALTER TABLE "NICE"."MAP_RESOURCES" MODIFY ("ID_RESOURCE" NOT NULL ENABLE);
ALTER TABLE "NICE"."PROCESSING_JOBS" MODIFY ("ID" NOT NULL ENABLE);
ALTER TABLE "NICE"."PROCESSING_JOBS" MODIFY ("ID_PATIENT" NOT NULL ENABLE);
ALTER TABLE "NICE"."PROCESSING_JOBS" MODIFY ("STAGE" NOT NULL ENABLE);
ALTER TABLE "NICE"."PROCESSING_JOBS" MODIFY ("CREATED_AT" NOT NULL ENABLE);
ALTER TABLE "NICE"."PROCESSING_JOBS" MODIFY ("ID_ECG" NOT NULL ENABLE);
ALTER TABLE "NICE"."PROCESSING_JOBS" ADD CONSTRAINT "PROCESSING_JOBS_PK" PRIMARY KEY ("ID") USING INDEX ENABLE;
ALTER TABLE "NICE"."PATIENTS" ADD CONSTRAINT "PK_PATIENTS_ID" PRIMARY KEY ("ID") USING INDEX "NICE"."PK_PATIENTS_ID" ENABLE;
ALTER TABLE "NICE"."PATIENTS" MODIFY ("DELETED" NOT NULL ENABLE);
ALTER TABLE "NICE"."NICE_USERS" ADD CONSTRAINT "PK_NICE_USERS_ID" PRIMARY KEY ("ID") USING INDEX "NICE"."PK_NICE_USERS_ID" ENABLE;
ALTER TABLE "NICE"."RESOURCE_LK" ADD CONSTRAINT "PK_RESOURCE_LK_ID" PRIMARY KEY ("ID") USING INDEX "NICE"."PK_RESOURCE_LK_ID" ENABLE;
ALTER TABLE "NICE"."MAPS" ADD CONSTRAINT "PK_MAPS_ID" PRIMARY KEY ("ID") USING INDEX "NICE"."PK_MAPS_ID" ENABLE;
ALTER TABLE "NICE"."SIMULATIONS" MODIFY ("ID" NOT NULL ENABLE);
ALTER TABLE "NICE"."SIMULATIONS" MODIFY ("DESCRIPTION" NOT NULL ENABLE);
ALTER TABLE "NICE"."SIMULATIONS" MODIFY ("ID_MAP" NOT NULL ENABLE);
ALTER TABLE "NICE"."SIMULATIONS" MODIFY ("ID_MAP_RESULT" NOT NULL ENABLE);
ALTER TABLE "NICE"."SIMULATIONS" MODIFY ("STATUS" NOT NULL ENABLE);
ALTER TABLE "NICE"."SIMULATIONS" ADD CONSTRAINT "SIMULATIONS_PK" PRIMARY KEY ("ID") USING INDEX ENABLE;
ALTER TABLE "NICE"."SIMULATIONS" MODIFY ("CREATED_AT" NOT NULL ENABLE);
ALTER TABLE "NICE"."SIMULATIONS" MODIFY ("ID_PATIENT" NOT NULL ENABLE);
ALTER TABLE "NICE"."MAP_RESOURCES" ADD CONSTRAINT "MAP_RESOURCES_RESOURCES_FK" FOREIGN KEY ("ID_RESOURCE") REFERENCES "NICE"."RESOURCES" ("ID") ENABLE;
ALTER TABLE "NICE"."MAP_RESOURCES" ADD CONSTRAINT "MAP_RESOURCES_MAPS_FK" FOREIGN KEY ("ID_MAP") REFERENCES "NICE"."MAPS" ("ID") ENABLE;
ALTER TABLE "NICE"."MAPS" ADD CONSTRAINT "FK_MPAS_ID_USER_CREATE" FOREIGN KEY ("ID_USER_CREATE") REFERENCES "NICE"."NICE_USERS" ("ID") ENABLE;
ALTER TABLE "NICE"."MAPS" ADD CONSTRAINT "FK_MAPS_ID_PATIENTS" FOREIGN KEY ("ID_PATIENT") REFERENCES "NICE"."PATIENTS" ("ID") ENABLE;
ALTER TABLE "NICE"."RESOURCES" ADD CONSTRAINT "FK_NICE_USERS_ID_USER_CREATE" FOREIGN KEY ("ID_USER_CREATE") REFERENCES "NICE"."NICE_USERS" ("ID") ENABLE;
ALTER TABLE "NICE"."RESOURCES" ADD CONSTRAINT "FK_NICE_USERS_ID_USER_UPDATE" FOREIGN KEY ("ID_USER_UPDATE") REFERENCES "NICE"."NICE_USERS" ("ID") ENABLE;
ALTER TABLE "NICE"."RESOURCES" ADD CONSTRAINT "FK_PATIENTS_ID" FOREIGN KEY ("ID_PATIENT") REFERENCES "NICE"."PATIENTS" ("ID") ENABLE;
ALTER TABLE "NICE"."RESOURCES" ADD CONSTRAINT "FK_RESOURCE_LK" FOREIGN KEY ("ID_RESOURCE_TYPE") REFERENCES "NICE"."RESOURCE_LK" ("ID") ENABLE;

