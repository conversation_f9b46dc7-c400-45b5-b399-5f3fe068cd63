const winston = require("winston");
const moment = require("moment-timezone");
const config = require("./config");
const dateFormat = require("dateformat");
const DailyRotateFile = require("winston-daily-rotate-file");
const { ecsFormat } = require("@elastic/ecs-winston-format");
const fs = require("fs");
const path = require("path");
let logPath = config.logpath;
let cleanAppName = config.application.toLowerCase().split(" ").join("_");
let logFileName = "%DATE%_" + cleanAppName + ".log";
let currFileName = "";

/** Decora l'oggetto info con i campi di log med per portarli su ELK */
const appendMedFieldsFormat = winston.format((info, opts) => {
  info["log.icon"] = levelIcon(info.level || "");
  info["log.file.path"] = currFileName;
  info["log.file.logger"] = "winston";
  //The name of the function or method which originated the log event.
  //log.origin.filename -> nome del file della function sotto.
  //log.origin.function
  //organization.id   Unique identifier for the organization.
  //organization.name  Organization name.
  //organization.name.text  type: match_only_text
  //server ...
  //service ...
  info.service = {
    type: "nodejs",
  };
  info.process = {
    name: config.application,
    pid: process.pid,
  };
  info.source = {
    source: {
      ip: config.ipAddress,
      port: parseInt(config.port),
    },
  };
  //Rimosso come da indicazioni di Matteo, li mette lui tramite regole logstash.
  /*info.customer = {
    name: config.site,
    code: config.code,
  };*/
  return info;
});

const appendTimeStamp = winston.format((info, opts) => {
  info.timestamp = moment().tz("Europe/Rome").format("YYYY-MM-DDTHH:mm:ss.SSSZ");
  return info;
});

let dailyFileTransport = new DailyRotateFile({
  filename: logFileName,
  dirname: logPath,
  datePattern: "YYYYMMDD",
  zippedArchive: false,
  level: config.logFileLevel,
  json: true,
  stringify: (obj) => JSON.stringify(obj),
});
function levelIcon(level) {
  switch (level) {
    case "debug":
      return "🐛";
    case "info":
      return "💡";
    case "warn":
      return "⚠️";
    case "http":
      return "🌏";
    case "error":
      return "⛔";
    case "verbose":
      return "✔";
    case "silly":
      return "🦒";
    default:
      return "ℹ️";
  }
  /*alternative..
    "?", "?","?","❌","⚠️", "?","ℹ️","?","?");
    '✅️ Database loaded')('? ️Database connected')('? Error connecting to database: ')('➕ Injecting dependencies')
  */
}

const elkFormat = winston.format.combine(appendMedFieldsFormat(), winston.format.splat(), ecsFormat({ convertReqRes: true }));

const consolelogFormat = winston.format.combine(
  appendTimeStamp(),
  appendMedFieldsFormat(),
  winston.format.splat(),
  winston.format.colorize(),
  winston.format.cli(),
  winston.format.printf((info) => {
    let hasTransId = info.transaction !== undefined && info.transaction.id !== undefined;
    let hasTransTime = info.transaction !== undefined && info.transaction.processingTime !== undefined;
    let cleanMessage = info.message.trim().replace("undefined", "");
    cleanMessage = cleanMessage.replace("undefined", "");
    info.message = cleanMessage;
    const baseMessage = `${info.timestamp} [${info.level}] ${info.message}`;
    const transMessage = hasTransId ? ` [${info.transaction.id}]` : ``;
    const transTime = hasTransTime ? ` ( ${info.transaction.processingTime}ms )` : ``;
    return `${baseMessage}${transMessage}${transTime}`;
  })
);

const consoleTransport = new winston.transports.Console({
  colorize: "all",
  format: consolelogFormat,
  timestamp: true,
  level: config.logConsoleLevel,
});

const logger = winston.createLogger({
  levels: winston.config.npm.levels,
  format: elkFormat,
  transports: [dailyFileTransport, consoleTransport],
});

/** Contatore interno delle richieste cosi da tracciare flusso chiamate */
let internalTransactionId = 0;

/** Middleware di log di OGNI richiesta fatta al server express.
 *    Viene ignorato il metodo 'HEAD', il livello nel logger deve avere attivo il tipo http
 *    Viene passato SEMPRE req, res che poi elk formatta in modo appropriato.
 *    E' consigliabile inserirlo come prima richiesta di tutto (cookie, pubblic, views, etc, cosi da monitare proprio tutto.)
 *
 * Viene attaccato alla res l'ascolto dell'evento on'finish'.
 * Su req.transaction abbiamo le seguenti proprietà (tracciate da elk):
 * - "req.transaction.id" - 'numero_transazione' progressivo incrementato ad ogni richiesta in entrata
 * - "req.transaction.start_timestamp: - 'date_time' entrata richiesta http
 * - "req.transaction.start_timestamp: - 'date_time' uscita (conclusione) richiesta http
 * - "req.transaction.processingTime: - tempo impiegato per elaborare la  richiesta http
 *
 */
async function MedElkLogMiddleWare(req, res, next) {
  if (req.method != "HEAD") {
    const requestStart = Date.now();
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
    let realIp = req.header["X-Forwarded-For"];
    let reqIpAddr = realIp || req.ip;
    let fullUrl = req.protocol + "://" + req.get("host");
    internalTransactionId++;
    req.fullUrl = fullUrl;
    req.realIp = reqIpAddr;
    req.transaction = {};
    req.transaction.id = "pid." + process.pid + "." + internalTransactionId.toString();
    req.transaction.start_timestamp = moment(requestStart).tz("Europe/Rome").format("YYYY-MM-DDTHH:mm:ss.SSSZ");

    logger.http(`${req.realIp} => --- ${req.method} ${req.url}`, { transaction: req.transaction, req });

    //mi attacco all'evento 'finish' per loggare la conclusione della richiesta.
    res.on("finish", () => {
      const requestStop = Date.now();
      req.transaction.stop_timestamp = moment(requestStop).tz("Europe/Rome").format("YYYY-MM-DDTHH:mm:ss.SSSZ");
      req.transaction.processingTime = requestStop - requestStart;
      logger.http(`${req.realIp} <= ${res.statusCode} ${req.method} ${req.originalUrl}`, { transaction: req.transaction, req, res });
    });
  }
  next();
}
//Ho messo il check variabile .env , ma se non usato generava un file di log vuoto inutilmente.
if (process.env.LOG_PROXY_ENDPOINT) {
  let elkLogger = new winston.createLogger({
    levels: {
      error: 100,
      warning: 200,
      info: 300,
      http: 400,
      verbose: 500,
      debug: 600,
      silly: 700,
      fine: 800,
      finer: 900,
      finest: 999,
    },
    format: ecsFormat({ convertReqRes: true }),
    transports: [
      new DailyRotateFile({
        filename: `%DATE%_generic_med_app.log`,
        dirname: logPath,
        datePattern: "YYYYMMDD",
        zippedArchive: true,
        level: "finest",
        json: true,
        stringify: (obj) => JSON.stringify(obj),
      }),
    ],
  });
  /** Middleware che 'travasa' in modo trasparente un qualsiasi json inviato nel body sul file di log,
   * che verrà poi importato da filebeat.
   */
  // #swagger.tags = ['Logger Proxy']
  // #swagger.summary = 'Endpoint di proxy per le app.'
  function LogProxyMiddleWare(req, res) {
    if (req.method == "PUT") {
      try {
        if (req.body.logItem) {
          let { type, message } = req.body.logItem;
          elkLogger.log(type, message, { flutter_log: { ...req.body.logItem }, req });
        } else {
          let { level, message } = req.body;
          elkLogger.log(level, message, { ...req.body, req });
        }
      } catch (error) {}
    }
    return res.sendStatus(204).end();
  }
} else {
  function LogProxyMiddleWare(req, res) {
    return res.status(204).end();
  }
}

// Create the directory if it does not exist
if (logPath && !fs.existsSync(logPath)) {
  fs.mkdirSync(logPath, { recursive: true });
  logger.info(`Creato il percorso dei logs: ${logPath}`);
}
logger.info("Logger attivo. Percorso salvataggio files di log:%s", logPath);

module.exports = logger;

module.exports.logProxyMiddleWare = LogProxyMiddleWare;
module.exports.medElkLogMiddleWare = MedElkLogMiddleWare;
