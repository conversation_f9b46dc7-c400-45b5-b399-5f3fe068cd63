require("dotenv").config();
const logger = require("medcommon/logger");
const CustomOAuthServer = require("./oauth2.server");
const TokenExchangeGrantType = require("./oauth2.token.exchange.grant");
const { OAuthModel } = require("./oauth2.models");
const CustomOAuthSocketIO = require("./oauth2.socketio");
const medOAuthModel = new OAuthModel();
const { authenticate } = require("ldap-authentication");

const oauthOptions = {
  model: medOAuthModel,
  continueMiddleware: true,
  useErrorHandler: true,
  grants: ["password", "refresh_token", "client_credentials", "authorization_code", "token_exchange"],
  accessTokenLifetime: 60 * 60, // 1 ora
  refreshTokenLifetime: 60 * 60 * 24 * 30, // 30 giorni
  allowEmptyState: true,
  allowExtendedTokenAttributes: true,
  requireClientAuthentication: { password: false },
  extendedGrantTypes: {
    token_exchange: TokenExchangeGrantType,
  },
};
const oauthServer = new CustomOAuthServer(oauthOptions);

const socketIoOptions = {
  // After upgrading all client to Socket.IO 3.x, EIO3 will not be allowed.
  allowEIO3: true,
  cors: {
    //origin: checkOrigin,
    origin: "*",
    methods: ["GET", "POST"],
    allowedHeaders: ["content-type"],
    // It looks like Socket.IO needs it for cookies.
    credentials: true,
  },
};
const oauthSocketIO = new CustomOAuthSocketIO(socketIoOptions);

const tokenOptions = {
  allowExtendedTokenAttributes: true,
  requireClientAuthentication: {
    password: false,
    refresh_token: false,
    client_credentials: false,
  },
};

//FIXME:
//const { OAuth2ExpiredPruner } = require("./oauth2.autopruner");
//const pruner = new OAuth2ExpiredPruner({});

//#region Metodi di CONVALIDA utente / admin
/** Preleva dal body della request l'utente e la password e la convalida tramite il provider indicato. */
async function userAuthentication(req) {
  let toRet = false;
  let from = "unknown";
  if (req.session.ssoUser && req.session.ssoUser.id) {
    toRet = { user: req.session.ssoUser };
    from = "session";
  } else {
    toRet = await oauth2UserAuthenticationFromDB(req);
    //Questa è ok, funziona.
    //toRet = await oauth2UserAuthenticationFromLDAP(req);
  }
  logger.verbose("userAuthentication from %s -> USER=%O", from, (toRet.user && toRet.id) || toRet, { transaction: req.transaction });
  return toRet;
}

/**
 *
 */
async function oauth2UserAuthenticationFromDB(req) {
  let toRet = false;
  let { username, password } = req.body;
  if (username && password) {
    username = username.trim();
    password = password.trim();
    // TODO: fix this 
    let result = await OAuthModel.from(medOAuthModel, { transaction: req.transaction }).getUser(username, password);
    if (result) {
      toRet = { user: result };
      from = "db";
    }
  } else {
    logger.error("Error on userAuthentication, username or password blank.", { transaction: req.transaction });
  }
  return toRet;
}
const ldapOptions__ = {
  ldapOpts: { url: "ldap://test.mrach.it" },
  userDn: "cn=<%username%>,ou=users,dc=mrach,dc=it",
  userSearchBase: "dc=mrach,dc=it",
  usernameAttribute: "cn",
  //attributes: ['dn', 'sn', 'cn', 'uid'],
  // Puoi aggiungere altri parametri come searchFilter, etc.
};
async function oauth2UserAuthenticationFromLDAP(req) {
  let toRet = false;
  let { username, password } = req.body;
  let ldapoptions = {};
  try {
    let ldapOptions = JSON.parse(process.env.LDAP_OPTIONS);
    for (const key in ldapOptions) {
      if (typeof ldapOptions[key] === "string") {
        // Controlla se la stringa contiene il placeholder
        if (ldapOptions[key].includes("<%username%>")) {
          ldapoptions[key] = ldapOptions[key].replace("<%username%>", username);
        } else {
          ldapoptions[key] = ldapOptions[key];
        }
      } else {
        ldapoptions[key] = ldapOptions[key];
      }
    }
    let opts = {
      ...ldapoptions,
      userPassword: `${password}`,
      username: `${username}`,
    };
    //SMRACH: Sul mio ldap funziona....
    let ldapUser = await authenticate(opts);
    //{
    /*ldapOpts: { url: "ldap://test.mrach.it" },
    userDn: `cn=${username},ou=users,dc=mrach,dc=it`,
    userSearchBase: "dc=mrach,dc=it",
    usernameAttribute: "cn",
    attributes: ['dn', 'sn', 'cn'],*/
    //  ...options,
    //  userPassword: `${password}`,
    //  username: `${username}`,
    //});
    //TODO: DAL DNS LDAP CONVERTIRE IN OGGETTO OAuthUser
    toRet = { user: ldapUser };
    logger.info("UTENTE LDAP:%O", ldapUser);
  } catch (err) {
    logger.error("Error on userAuthentication:%O", err, { transaction: req.transaction });
  }
  return toRet;
}
//#endregion

async function oauth2UserAuthentication(req, res, next) {
  let result = await userAuthentication(req);
  res.locals.user = result.user;
  next();
}

async function oauth2Authenticate(req, res, next) {
  oauthServer.authenticate({
    model: new OAuthModel({ transaction: req.transaction }),
  })(req, res, next);
}

async function oauth2Authorize(req, res, next) {
  logger.warn("oauthServer.authorize");
  oauthServer.authorize({
    model: new OAuthModel({ transaction: req.transaction }),
    allowEmptyState: true,
    authenticateHandler: { handle: (req) => res.locals.user },
  })(req, res, next);
}

async function oauth2Token(req, res, next) {
  logger.verbose("OAUTH2 POST /TOKEN");
  oauthServer.token({
    tokenOptions,
    model: new OAuthModel({ transaction: req.transaction }),
  })(req, res, next);
}

module.exports = {
  oauthServer: oauthServer,
  medOAuthModel: medOAuthModel,
  oauthSocketIO: oauthSocketIO,
  oauth2UserAuthentication: oauth2UserAuthentication,
  oauth2Authenticate: oauth2Authenticate,
  oauth2Authorize: oauth2Authorize,
  oauth2Token: oauth2Token,
  tokenOptions: tokenOptions,
  userAuthentication: userAuthentication,
};
