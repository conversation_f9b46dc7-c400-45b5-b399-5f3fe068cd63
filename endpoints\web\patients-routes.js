import express from "express";
import logger from "medcommon/logger";
import oracle from "medcommon/oracle";
import path from "path";
import fs from "fs";
import PatientModel from "../../api/models/patient.js";
import PatientResourceModel from "../../api/models/patient-resource.js";
import VMapResourceModel from "../../api/models/view-map-resource.js";
import MapModel from "../../api/models/map.js";
import CommonUtils from "../../api/utils.js";

const utils = new CommonUtils();

import { getStorageUrl } from "../../middlewares/locals.js";
import multer from "multer";

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

import mapCtrls from "./activations-maps-controllers.js";
import simCtlrs from "./simulations-controllers.js";

const patientRoutes = express.Router();

patientRoutes
  .route("/new")
  .get(async (req, res) => {
    return res.render("patient-form", {
      patient: null,
      disableBreadcrumb: true,
    });
  })
  .post(async (req, res) => {
    const respId = await oracle.dbSingleRow("SELECT S_PATIENTS_ID.nextval id from dual");
    const newId = respId["ID"];
    const patient = new PatientModel(req.body);
    const jsonVals = patient.jsonValues();
    patient.id = newId;
    patient.code = newId;
    patient.birthDate = new Date(req.body.BIRTH_DATE);
    const result = await patient.save();
    if (result) {
      res.redirect(`/patients/${newId}`);
    } else {
      res.render("patient-form", {
        patient: req.body,
      });
    }
  });

patientRoutes
  .route("/search")
  .get(async (req, res) => {
    req.session.selectedPatient = {};
    res.locals.selectedPatient = req.session.selectedPatient;
    return res.render("patients-search");
  })
  .post(async (req, res) => {
    let { term } = req.query;
    let toRet = [];
    const normalizedQuery = term ? term.trim().toLowerCase() : "";
    toRet = await new PatientModel().findFree(
      [`%${normalizedQuery}%`],
      "(lower(LAST_NAME) || ' ' || lower(FIRST_NAME) LIKE :1 OR lower(STATUS_TAG) LIKE :1) AND DELETED = 'N'",
      "LAST_NAME, FIRST_NAME"
    );
    req.session.lastSearchTerm = term;
    return res.json(toRet).end();
  });

patientRoutes.use("/:id", async (req, res, next) => {
  const patientId = req.params.id;
  logger.verbose("Patient Next() middleware. PatientId: %s", patientId);
  const patient = await new PatientModel().findByKeys([`${patientId}`]);
  req.session.selectedPatient = patient;
  res.locals.patient = req.session.selectedPatient;
  res.locals.selectedPatient = req.session.selectedPatient;
  next();
});

patientRoutes.route("/:id/activationMaps").get(mapCtrls.getActivactionMaps).post(mapCtrls.postCreateActivationMap);

patientRoutes
  .route("/:id/edit")
  .get(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("GET /%s/edit", patientId);
    //TODO: cambiare in formatJson:true e provare.
    const patient = await new PatientModel().findByKeys([`${patientId}`]);
    let jsonPatients = patient.jsonValues();
    return res.render("patient-form", {
      patient: patient.jsonValues(),
    });
  })
  .post(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("POST /%s/edit", patientId);
    let result = false;
    //FIXME:Blocco l'edit dei primi quattro patienti X DEMO!!!!!!!!!!
    if (parseInt(patientId, 10) > 4) {
      const patient = new PatientModel(req.body);
      patient.id = patientId;
      patient.code = patientId;
      patient.deleted = "N";
      patient.birthDate = new Date(req.body.BIRTH_DATE);
      result = await patient.update();
    }
    if (result) {
      res.redirect(`/patients/${patientId}`);
    } else {
      res.render("patient-form", {
        patient: req.body,
      });
    }
  });

patientRoutes.post("/:id/delete", async (req, res) => {
  const patientId = req.params.id;
  logger.debug("DELETE /%s", patientId);
  await new PatientModel().delete([`${patientId}`]);
  res.redirect("/patients/search");
});

patientRoutes.get("/:id/dicom", async (req, res) => {
  const patientId = req.params.id;
  const resId = req.query.resid;
  const data = await new PatientResourceModel().findFree([resId], "ID = :1", "", true);
  const uid = data[0].uuid;
  logger.debug("GET /%s/resources/%s", patientId, resId);
  return res.render("dicom", {
    patient: patientId,
    resourceId: resId,
    dicomUrl: process.env.DICOM_URL + "studyUIDsList=" + uid,
  });
});

patientRoutes.get("/:id/patient-map", async (req, res) => {
  const patientId = req.params.id;
  const mapID = req.query.mapId;
  const title = req.query.title || "viewer.heading";
  return res.render("patient-map-ecg", { title, patientId: patientId, mapId: mapID });
});

// patientRoutes.route("/:id/implantation").get(async (req, res) => {
//   const patientId = req.params.id;
//   const implantations = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 2", "", true);
//   return res.render("patient-implantation", { patient: res.locals.selectedPatient, patientId: patientId, initialAssessments: maps });
// });
patientRoutes.route("/:id/implantation").get(async (req, res) => {
  const patientId = req.params.id;
  // const implantationMap = await new MapModel().findFirst([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 2", "", true);
  // if (!implantationMap) {
  //   // create it and put it in db
  //   const newId = await utils.getSequenceNextVal("S_MAPS_ID");
  //   const newMap = new MapModel({
  //     id: newId,
  //     idPatient: patientId,
  //     description: "Implantation",
  //     status: -1,
  //     creationDate: new Date(),
  //     idUserCreate: 1,
  //     type: 2,
  //   });
  //   await newMap.save();
  //   const newImplantation = new ImplantationModel({
  //     id: await utils.getSequenceNextVal("S_IMPLANTATIONS_ID"),
  //     idMap: newId,
  //     idMapResult: newId,
  //     idPatient: patientId,
  //     status: -1,
  //     createdAt: new Date(),
  //     data: JSON.stringify({ leads: {} }),
  //   });
  //   await newImplantation.save();
  // }
  const maps = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 0", "", true);
  // const implantation = await new ImplantationModel().findFirst([`${patientId}`], "ID_PATIENT = :1", "", true);
  let implantation = {
    id: 1,
    idMap: 1,
    idPatient: patientId,
    description: "Implantation",
    status: -1,
    createdAt: new Date(),
  };
  if (maps.length > 0) {
    implantation.idMapResult = maps[0].id;
    //   await implantation.update();
  }
  const resources = await new VMapResourceModel().findFree([`${implantation.idMapResult}`], "ID_MAP = :1", "", true);
  let resourcesData = [];
  resources.forEach((item) => {
    const url = res.locals.getStorageUrl(patientId, implantation.idMapResult, item.idResourceType, item.uniqueId);
    let toAdd = {
      type: item.idResourceTypeText,
      url: url,
    };
    resourcesData.push(toAdd);
  });
  implantation.data = {
    leads: {
      LV: {
        axis: "0.770;2.890;1.000;3.000",
        ventricular: "0.770;2.890;1.000;3.000",
      },
      RV: [
        {
          axis: "0.770;2.890;1.000;3.000",
          ventricular: "0.770;2.890;1.000;3.000",
        },
        {
          axis: "0.770;2.890;1.000;3.000",
          ventricular: "0.770;2.890;1.000;3.000",
        },
      ],
    },
  };
  return res.render("patient-implantation", {
    patient: res.locals.selectedPatient,
    patientId: patientId,
    initialAssessments: maps,
    resources: JSON.stringify(resourcesData),
    implantation,
  });
});
// patientRoutes.route("/:id/implantation-new").get(async (req, res) => {
//   const patientId = req.params.id;
//   const implantations = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 2", "", true);
//   return res.render("patient-implantation", { patient: res.locals.selectedPatient, patientId: patientId, initialAssessments: maps });
// }).post(async (req, res) => {

// });

patientRoutes.route("/:id/simulations").get(simCtlrs.getSimulations);
patientRoutes.route("/:id/simulation").get(simCtlrs.getSimulation).post(simCtlrs.postSimulation);
patientRoutes.route("/:id/simulation-new").get(simCtlrs.getNewSimulation).post(simCtlrs.postNewSimulation);

patientRoutes.route("/:id/vtk-viewer").get(async function (req, res) {
  const patientId = req.params.id;
  const mapId = req.query.mapId;
  //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
  const mapResources = await new VMapResourceModel().findFree([`${mapId}`], "ID_MAP = :1", "", true);
  let resources = [];
  mapResources.forEach((item) => {
    const url = res.locals.getStorageUrl(patientId, mapId, item.idResourceType, item.uniqueId);
    let toAdd = {
      type: item.idResourceTypeText,
      url: url,
    };
    resources.push(toAdd);
  });
  if (mapResources) {
    res.render("activation-map", { patientId: patientId, mapId: mapId, resources: JSON.stringify(resources) });
  } else {
    return res.redirect("/404");
  }
});

patientRoutes.get("/:id/ecg", async (req, res) => {
  const patientId = req.params.id;
  const resId = req.query.resid;
  const data = await new PatientResourceModel().findFree([resId], "ID = :1", "", true);
  const ecgUrl = res.locals.getStorageUrl(patientId, 0, 1, data[0].uuid);
  logger.debug("GET /%s/ecg", patientId, data[0]);
  return res.render("ecg", { ecgData: data[0], ecgUrl: ecgUrl });
});

patientRoutes.get("/:id/ecg-list", async (req, res) => {
  const patientId = req.params.id;
  const data = await new PatientResourceModel().findFree([patientId], "ID_PATIENT = :1 AND ID_RESOURCE_TYPE = 1", "", true);
  logger.debug("GET /%s/ecg-list", patientId, data[0]);
  return res.send(data).end();
});

patientRoutes.route("/:id/ecg-small").get(async function (req, res) {
  const patientId = req.params.id;
  const mapId = req.query.mapId;
  //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
  const mapResources = await new VMapResourceModel().findFree([`${mapId}`], "ID_MAP = :1 AND ID_RESOURCE_TYPE = 1", "", true);
  const data = mapResources[0];
  const ecgUrl = res.locals.getStorageUrl(patientId, mapId, 1, data.uniqueId);
  logger.debug("GET /%s/ecg", patientId, data);
  return res.render("ecg-small", { ecgUrl: ecgUrl });
});

patientRoutes
  .route("/:id/resources")
  .get(async (req, res) => {
    const patientId = req.params.id;
    const resources = await new PatientResourceModel().findFree(
      [`${patientId}`],
      "ID_PATIENT = :1 AND (ID_RESOURCE_TYPE = 1 OR ID_RESOURCE_TYPE = 2)",
      "RESOURCE_ORDER",
      true
    );
    return res.render("patient-page", {
      patient: res.locals.selectedPatient,
      list: resources,
    });
  })
  .post(upload.single("file"), async (req, res) => {
    // get file and type from formdata
    const patientId = req.params.id;
    const notes = req.body.notes;
    const fileDbType = req.query.fileDbType;
    const file = req.file;
    logger.debug("POST /%s/resources", patientId);
    logger.debug("File to upload: %O", file);
    logger.debug("File Db Type: %s", fileDbType);
    if (file) {
      // write into fs
      const projectRoot = process.cwd();
      const filePath = path.join(projectRoot, getStorageUrl(patientId, 0, fileDbType, file.originalname));
      const dirPath = filePath.slice(0, -file.originalname.length);
      fs.mkdirSync(dirPath, { recursive: true });
      fs.writeFileSync(filePath, file.buffer);
      logger.debug("File saved to %s", filePath);

      const newId = await utils.getSequenceNextVal("S_RESOURCES_ID");
      // TODO: rivedere i valori hardcoded in presenza di specifiche a riguardo
      const resData = {
        id: newId,
        idPatient: patientId,
        idResourceType: fileDbType,
        resourceTag: "before",
        resourceDate: new Date(),
        idUserCreate: 1,
        idUserUpdate: 1,
        resourceOrder: 1,
        resourceNotes: notes || "",
        uuid: file.originalname,
      };
      const result = await new PatientResourceModel(resData).save();
      if (result) {
        res.status(200).json({ success: true, message: "File uploaded successfully", resourceId: newId }).end();
      } else {
        res.status(500).json({ success: false, message: "Error saving resource to database" }).end();
      }
    }
    return;
  });
patientRoutes.get("/:id", async (req, res) => {
  const patientId = req.params.id;
  if (process.env.SUMMARY_URL) {
    const url = process.env.SUMMARY_URL + "?patientId=" + patientId;
    return res.render("fullscreen-iframe", { url });
  }
  res.redirect(`/patients/${patientId}/resources`);
});
export default patientRoutes;
