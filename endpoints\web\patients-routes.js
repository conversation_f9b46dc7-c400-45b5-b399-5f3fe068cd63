import express from "express";
import logger from "medcommon/logger";
import oracle from "medcommon/oracle";
import PatientModel from "../../api/models/patient.js";
import PatientResourceModel from "../../api/models/patient-resource.js";
import MapResourceModel from "../../api/models/map-resource.js";

const patientRoutes = express.Router();

patientRoutes
  .route("/new")
  .get(async (req, res) => {
    return res.render("patient-form", {
      patient: null,
      disableBreadcrumb: true,
    });
  })
  .post(async (req, res) => {
    const respId = await oracle.dbSingleRow("SELECT S_PATIENTS_ID.nextval id from dual");
    const newId = respId["ID"];
    const patient = new PatientModel(req.body);
    const jsonVals = patient.jsonValues();
    patient.id = newId;
    patient.code = newId;
    patient.birthDate = new Date(req.body.BIRTH_DATE);
    const result = await patient.save();
    if (result) {
      res.redirect(`/patients/${newId}`);
    } else {
      res.render("patient-form", {
        patient: req.body,
      });
    }
  });

patientRoutes
  .route("/search")
  .get(async (req, res) => {
    req.session.selectedPatient = {};
    res.locals.selectedPatient = req.session.selectedPatient;
    return res.render("patients-search");
  })
  .post(async (req, res) => {
    let { term } = req.query;
    let toRet = [];
    //if (term && term.length > 0) {
    const normalizedQuery = term ? term.trim().toLowerCase() : "";
    //toRet = patients.filter((p) => p.firstName.toLowerCase().includes(normalizedQuery) || p.lastName.toLowerCase().includes(normalizedQuery));
    toRet = await new PatientModel().findFree(
      [`%${normalizedQuery}%`],
      "lower(LAST_NAME) || ' ' || lower(FIRST_NAME) LIKE :1 OR lower(STATUS_TAG) LIKE :1",
      "LAST_NAME, FIRST_NAME"
    );
    //}
    req.session.lastSearchTerm = term;
    return res.json(toRet).end();
  });

patientRoutes.get("/:id", async (req, res, next) => {
  const patientId = req.params.id;
  logger.verbose("Patient Next() middleware. PatientId: %s", patientId);
  const patient = await new PatientModel().findByKeys([`${patientId}`]);
  req.session.selectedPatient = patient;
  res.locals.selectedPatient = req.session.selectedPatient;
  next();
});

patientRoutes
  .route("/:id/activationMaps")
  .get(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("GET /%s/activationMaps", patientId);
    // const activationMaps = await new PatientMapViewModel().findFree([`${patientId}`], "ID_PATIENT = :1", "CREATION_DATE DESC", true);
    // const resources = await new PatientResourceModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND (ID_RESOURCE_TYPE = 1 OR ID_RESOURCE_TYPE = 2)", "RESOURCE_ORDER", true);
    const resources = await new MapResourceModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND ID_RESOURCE_TYPE IN (1,2)", "RESOURCE_ORDER", true);
    let activationMaps = {};
    resources.forEach((resource) => {
      if (!activationMaps[resource.idMap]) {
        if (resource.idResourceType === 1) {
          resource.resourceDateEcg = resource.resourceDate;
          resource.resourceTagEcg = resource.resourceTag;
        }
        if (resource.idResourceType === 2) {
          resource.resourceDateTc = resource.resourceDate;
          resource.resourceTagTc = resource.resourceTag;
        }
        activationMaps[resource.idMap] = resource;
      } else {
        if (resource.idResourceType === 1) {
          activationMaps[resource.idMap].resourceDateEcg = resource.resourceDate;
          activationMaps[resource.idMap].resourceTagEcg = resource.resourceTag;
        }
        if (resource.idResourceType === 2) {
          activationMaps[resource.idMap].resourceDateTc = resource.resourceDate;
          activationMaps[resource.idMap].resourceTagTc = resource.resourceTag;
        }
      }
    });
    let activationMapsList = Object.values(activationMaps);
    return res.render("patient-maps", {
      patient: res.locals.selectedPatient,
      list: activationMapsList,
      resources: resources,
    });
  })
  .post(async (req, res) => {
    const ecgSelezionato = req.body.ecg;
    logger.debug("Hai selezionato ECG ID:", ecgSelezionato);
    const tacSelezionata = req.body.tac;
    logger.debug("Hai selezionato TAC ID:", tacSelezionata);

    //TODO: Qui avviare la chiamata ai servizi di generazione modelli
    // passando i parametri selezionati a video e generare pagina di status.
    return res.redirect("/todo");
  });

patientRoutes
  .route("/:id/edit")
  .get(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("GET /%s/edit", patientId);
    //let patient = patients.find((p) => p.code === patientId);
    //fs.writeFileSync(mockDataFile, JSON.stringify(patients));
    //patients = JSON.parse(fs.readFileSync(mockDataFile, 'utf-8'));
    const patient = await new PatientModel().findByKeys([`${patientId}`]);
    let jsonPatients = patient.jsonValues();
    return res.render("patient-form", {
      patient: patient.jsonValues(),
    });
  })
  .post(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("POST /%s/edit", patientId);
    const patient = new PatientModel(req.body);
    patient.id = patientId;
    patient.code = patientId;
    patient.birthDate = new Date(req.body.BIRTH_DATE);
    const result = await patient.update();
    if (result) {
      res.redirect(`/patients/${patientId}`);
    } else {
      res.render("patient-form", {
        patient: req.body,
      });
    }
  });

patientRoutes.post("/:id/delete", async (req, res) => {
  const patientId = req.params.id;
  logger.debug("DELETE /%s", patientId);
  await new PatientModel().delete([`${patientId}`]);
  res.redirect("/patients/search");
});

patientRoutes.get("/:id/dicom", async (req, res) => {
  const patientId = req.params.id;
  const resId = req.query.resid;
  const data = await new PatientResourceModel().findFree([resId], "ID = :1", "", true);
  const uid = data[0].uuid;
  logger.debug("GET /%s/resources/%s", patientId, resId);
  return res.render("dicom", {
    patient: patientId,
    resourceId: resId,
    dicomUrl: process.env.DICOM_URL + "studyUIDsList=" + uid,
  });
});

patientRoutes.get("/:id/ecg", async (req, res) => {
  const patientId = req.params.id;
  const resId = req.query.resid;
  const data = await new PatientResourceModel().findFree([resId], "ID = :1", "", true);
  const ecgUrl = res.locals.getStorageUrl(patientId, 0, 1, data[0].uuid);
  logger.debug("GET /%s/ecg", patientId, data[0]);
  return res.render("ecg", { ecgUrl: ecgUrl });
});

patientRoutes.get("/:id/patient-map", async (req, res) => {
  const patientId = req.params.id;
  const mapID = req.query.mapId;
  const title = req.query.title || "viewer.heading";
  return res.render("patient-map-ecg", { title, patientId: patientId, mapId: mapID });
});

patientRoutes.route("/:id/simulation").get(async function (req, res) {
  const patientId = req.params.id;
  const mapId = req.query.mapId;
  //Map id corrisponde al initial assessment di riferimento.
  //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
  const mapResources = await new MapResourceModel().findFree([`${mapId}`], "ID_MAP = :1", "", true);
  let resources = [];
  mapResources.forEach((item) => {
    const url = res.locals.getStorageUrl(patientId, mapId, item.idResourceType, item.uniqueId);
    let toAdd = {
      type: item.idResourceTypeText,
      url: url,
    };
    resources.push(toAdd);
  });
  if (mapResources) {
    res.render("simulation", { patientId: patientId, mapId: mapId, resources: JSON.stringify(resources) });
  } else {
    return res.redirect("/404");
  }
});

patientRoutes.route("/:id/vtk-viewer").get(async function (req, res) {
  const patientId = req.params.id;
  const mapId = req.query.mapId;
  //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
  const mapResources = await new MapResourceModel().findFree([`${mapId}`], "ID_MAP = :1", "", true);
  let resources = [];
  mapResources.forEach((item) => {
    const url = res.locals.getStorageUrl(patientId, mapId, item.idResourceType, item.uniqueId);
    let toAdd = {
      type: item.idResourceTypeText,
      url: url,
    };
    resources.push(toAdd);
  });
  if (mapResources) {
    res.render("activation-map", { patientId: patientId, mapId: mapId, resources: JSON.stringify(resources) });
  } else {
    return res.redirect("/404");
  }
});

patientRoutes.route("/:id/ecg-small").get(async function (req, res) {
  const patientId = req.params.id;
  const mapId = req.query.mapId;
  //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
  const mapResources = await new MapResourceModel().findFree([`${mapId}`], "ID_MAP = :1 AND ID_RESOURCE_TYPE = 1", "", true);
  const data = mapResources[0];
  const ecgUrl = res.locals.getStorageUrl(patientId, mapId, 1, data.uniqueId);
  logger.debug("GET /%s/ecg", patientId, data);
  return res.render("ecg-small", { ecgUrl: ecgUrl });
});

patientRoutes.get("/:id", async (req, res) => {
  const patientId = req.params.id;
  const resources = await new PatientResourceModel().findFree(
    [`${patientId}`],
    "ID_PATIENT = :1 AND (ID_RESOURCE_TYPE = 1 OR ID_RESOURCE_TYPE = 2)",
    "RESOURCE_ORDER",
    true
  );
  return res.render("patient-page", {
    patient: res.locals.selectedPatient,
    list: resources,
  });
});
export default patientRoutes;
