import express from "express";
import logger from "medcommon/logger";
import oracle from "medcommon/oracle";
import PatientModel from "../../api/models/patient.js";
import PatientResourceModel from "../../api/models/patient-resource.js";
import VMapResourceModel from "../../api/models/view-map-resource.js";
import path from "path";
import fs from "fs";
import SimulationModel from "../../api/models/simulation.js";
import ImplantationModel from "../../api/models/implantantion.js";
import MapModel from "../../api/models/map.js";
import utils from "../../api/utils.js";
import { getStorageUrl } from "../../middlewares/locals.js";
import multer from "multer";
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

import JobSchedulerSocketModel from "../../api/models/job-scheduler.js";

const patientRoutes = express.Router();

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

patientRoutes
  .route("/new")
  .get(async (req, res) => {
    return res.render("patient-form", {
      patient: null,
      disableBreadcrumb: true,
    });
  })
  .post(async (req, res) => {
    const respId = await oracle.dbSingleRow("SELECT S_PATIENTS_ID.nextval id from dual");
    const newId = respId["ID"];
    const patient = new PatientModel(req.body);
    const jsonVals = patient.jsonValues();
    patient.id = newId;
    patient.code = newId;
    patient.birthDate = new Date(req.body.BIRTH_DATE);
    const result = await patient.save();
    if (result) {
      res.redirect(`/patients/${newId}`);
    } else {
      res.render("patient-form", {
        patient: req.body,
      });
    }
  });

patientRoutes
  .route("/search")
  .get(async (req, res) => {
    req.session.selectedPatient = {};
    res.locals.selectedPatient = req.session.selectedPatient;
    return res.render("patients-search");
  })
  .post(async (req, res) => {
    let { term } = req.query;
    let toRet = [];
    const normalizedQuery = term ? term.trim().toLowerCase() : "";
    toRet = await new PatientModel().findFree(
      [`%${normalizedQuery}%`],
      "(lower(LAST_NAME) || ' ' || lower(FIRST_NAME) LIKE :1 OR lower(STATUS_TAG) LIKE :1) AND DELETED = 'N'",
      "LAST_NAME, FIRST_NAME"
    );
    req.session.lastSearchTerm = term;
    return res.json(toRet).end();
  });

patientRoutes.use("/:id", async (req, res, next) => {
  const patientId = req.params.id;
  logger.verbose("Patient Next() middleware. PatientId: %s", patientId);
  const patient = await new PatientModel().findByKeys([`${patientId}`]);
  req.session.selectedPatient = patient;
  res.locals.patient = req.session.selectedPatient;
  res.locals.selectedPatient = req.session.selectedPatient;
  next();
});

patientRoutes.route("/:id/resources").post(upload.single("file"), async (req, res) => {
  // get file and type from formdata
  const patientId = req.params.id;
  const notes = req.body.notes;
  const fileDbType = req.query.fileDbType;
  const file = req.file;
  logger.debug("POST /%s/resources", patientId);
  logger.debug("File to upload: %O", file);
  logger.debug("File Db Type: %s", fileDbType);
  if (file) {
    // write into fs
    const projectRoot = process.cwd();
    const filePath = path.join(projectRoot, getStorageUrl(patientId, 0, fileDbType, file.originalname));
    const dirPath = filePath.slice(0, -file.originalname.length);
    fs.mkdirSync(dirPath, { recursive: true });
    fs.writeFileSync(filePath, file.buffer);
    logger.debug("File saved to %s", filePath);
    const newIdResult = await oracle.dbSingleRow("SELECT S_RESOURCES_ID.nextval id from dual");
    const newId = newIdResult["ID"];
    const resource = new PatientResourceModel();

    // TODO: rivedere i valori hardcoded in presenza di specifiche a riguardo
    resource.id = newId;
    resource.idPatient = patientId;
    resource.idResourceType = fileDbType;
    resource.resourceTag = "before";
    resource.resourceDate = new Date();
    resource.idUserCreate = 1;
    resource.idUserUpdate = 1;
    resource.resourceOrder = 1;
    resource.resourceNotes = notes || "";
    resource.uuid = file.originalname;
    const result = await resource.save();
    if (result) {
      res.status(200).json({ success: true, message: "File uploaded successfully", resourceId: newId }).end();
    } else {
      res.status(500).json({ success: false, message: "Error saving resource to database" }).end();
    }
  }
});

patientRoutes
  .route("/:id/activationMaps")
  .get(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("GET /%s/activationMaps", patientId);
    const maps = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE=0", "", true);
    let activationMaps = {};
    for (let i = 0; i < maps.length; i++) {
      const map = maps[i];
      const newData = { ...map };
      newData.idMap = map.id;
      const ecgInfo = await new VMapResourceModel().findFirst([`${map.id}`], "ID_MAP=:1 AND ID_RESOURCE_TYPE=1", "", true);
      newData.ecgInfo = ecgInfo;
      const tacInfo = await new VMapResourceModel().findFirst([`${map.id}`], "ID_MAP=:1 AND ID_RESOURCE_TYPE=8", "", true);
      newData.tcInfo = tacInfo;
      activationMaps[map.id] = newData;
    }
    const ecgList = await new PatientResourceModel().findFree([patientId], "ID_PATIENT = :1 AND ID_RESOURCE_TYPE = 1", "", true);
    const ctList = await new PatientResourceModel().findFree([patientId], "ID_PATIENT = :1 AND ID_RESOURCE_TYPE = 8", "", true);
    let activationMapsList = Object.values(activationMaps);
    return res.render("patient-maps", {
      //patient: res.locals.selectedPatient,
      list: activationMapsList,
      resources: { ecgList: ecgList, ctList: ctList },
    });
  })
  .post(async (req, res) => {
    const patientId = req.params.id;
    const ecgSelezionato = req.body.ecg;
    logger.debug("Hai selezionato ECG ID:", ecgSelezionato);
    const tacSelezionata = req.body.ctScan;
    logger.debug("Hai selezionato TAC ID:", tacSelezionata);

    //TODO: Qui avviare la chiamata ai servizi di generazione modelli
    // passando i parametri selezionati a video e generare pagina di status.
    const mapId = await oracle.dbSingleRow("SELECT S_MAPS_ID.nextval id from dual");
    const map = {
      id: mapId["ID"],
      idPatient: patientId,
      idEcg: ecgSelezionato,
      idTac: tacSelezionata,
      notes: req.body.notes || "",
    };
    const connection = oracle.getDBConnection();
    try {
      const connOpts = {}; // connection: connection, doCommit: false };

      const insMapSql = "INSERT INTO MAPS (ID, ID_PATIENT, DESCRIPTION, STATUS, ID_USER_CREATE) VALUES (:1, :2, :3, :4, :5)";
      await oracle.dbExecute(insMapSql, [map.id, map.idPatient, map.notes, -1, 1]);

      //Lego le due risorse alla nuova mappa.
      const mapResSql = "INSERT INTO MAP_RESOURCES(ID_MAP, ID_RESOURCE) VALUES (:1, :2)";
      await oracle.dbExecute(mapResSql, [map.id, ecgSelezionato]);
      await oracle.dbExecute(mapResSql, [map.id, tacSelezionata]);

      const jobSchedSql = "insert into NICE.processing_jobs (id, id_map, id_patient, stage) values(nice.s_processing_jobs_id.nextval, :1, :2, :3)";
      await oracle.dbExecute(jobSchedSql, [map.id, map.idPatient, "create_segmentation"]);
      await delay(1500); // Aspetta 2 secondi
      await oracle.dbExecute(jobSchedSql, [map.id, map.idPatient, "segmentation"]);
      await delay(1500); // Aspetta 2 secondi
      await oracle.dbExecute(jobSchedSql, [map.id, map.idPatient, "create_actmap_stage"]);
      await delay(1500); // Aspetta 2 secondi

      //L'ultima operazione fà il commit di tutto..
      //connOpts["doCommit"] = true;
      await oracle.dbExecute(jobSchedSql, [map.id, map.idPatient, "actmap_stage"]);

      //creo lo storage path della mappa:
      const projectRoot = process.cwd();
      const dirPath = path.join(projectRoot, `storage`, `${map.idPatient}`, `map-${map.id}`);
      fs.mkdirSync(dirPath, { recursive: true });
    } catch (error) {
      logger.error("Error on create-map:%O", error);
    }
    return res.redirect("/patients/" + patientId + "/activationMaps");
  });

patientRoutes
  .route("/:id/edit")
  .get(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("GET /%s/edit", patientId);
    //TODO: cambiare in formatJson:true e provare.
    const patient = await new PatientModel().findByKeys([`${patientId}`]);
    let jsonPatients = patient.jsonValues();
    return res.render("patient-form", {
      patient: patient.jsonValues(),
    });
  })
  .post(async (req, res) => {
    const patientId = req.params.id;
    logger.debug("POST /%s/edit", patientId);
    let result = false;
    //FIXME:Blocco l'edit dei primi quattro patienti X DEMO!!!!!!!!!!
    if (parseInt(patientId, 10) > 4) {
      const patient = new PatientModel(req.body);
      patient.id = patientId;
      patient.code = patientId;
      patient.deleted = "N";
      patient.birthDate = new Date(req.body.BIRTH_DATE);
      result = await patient.update();
    }
    if (result) {
      res.redirect(`/patients/${patientId}`);
    } else {
      res.render("patient-form", {
        patient: req.body,
      });
    }
  });

patientRoutes.post("/:id/delete", async (req, res) => {
  const patientId = req.params.id;
  logger.debug("DELETE /%s", patientId);
  await new PatientModel().delete([`${patientId}`]);
  res.redirect("/patients/search");
});

patientRoutes.get("/:id/dicom", async (req, res) => {
  const patientId = req.params.id;
  const resId = req.query.resid;
  const data = await new PatientResourceModel().findFree([resId], "ID = :1", "", true);
  const uid = data[0].uuid;
  logger.debug("GET /%s/resources/%s", patientId, resId);
  return res.render("dicom", {
    patient: patientId,
    resourceId: resId,
    dicomUrl: process.env.DICOM_URL + "studyUIDsList=" + uid,
  });
});

patientRoutes.get("/:id/ecg", async (req, res) => {
  const patientId = req.params.id;
  const resId = req.query.resid;
  const data = await new PatientResourceModel().findFree([resId], "ID = :1", "", true);
  const ecgUrl = res.locals.getStorageUrl(patientId, 0, 1, data[0].uuid);
  logger.debug("GET /%s/ecg", patientId, data[0]);
  return res.render("ecg", { ecgData: data[0], ecgUrl: ecgUrl });
});

patientRoutes.get("/:id/ecg-list", async (req, res) => {
  const patientId = req.params.id;
  const data = await new PatientResourceModel().findFree([patientId], "ID_PATIENT = :1 AND ID_RESOURCE_TYPE = 1", "", true);
  logger.debug("GET /%s/ecg-list", patientId, data[0]);
  return res.send(data).end();
});

patientRoutes.get("/:id/patient-map", async (req, res) => {
  const patientId = req.params.id;
  const mapID = req.query.mapId;
  const title = req.query.title || "viewer.heading";
  return res.render("patient-map-ecg", { title, patientId: patientId, mapId: mapID });
});

// patientRoutes.route("/:id/implantation").get(async (req, res) => {
//   const patientId = req.params.id;
//   const implantations = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 2", "", true);
//   return res.render("patient-implantation", { patient: res.locals.selectedPatient, patientId: patientId, initialAssessments: maps });
// });
patientRoutes.route("/:id/implantation").get(async (req, res) => {
  const patientId = req.params.id;
  const implantationMap = await new MapModel().findFirst([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 2", "", true);
  if (!implantationMap) {
    // create it and put it in db
    const newId = await utils.getSequenceNextVal("S_MAPS_ID");
    const newMap = new MapModel({
      id: newId,
      idPatient: patientId,
      description: "Implantation",
      status: -1,
      creationDate: new Date(),
      idUserCreate: 1,
      type: 2,
    });
    await newMap.save();
    const newImplantation = new ImplantationModel({
      id: await utils.getSequenceNextVal("S_IMPLANTATIONS_ID"),
      idMap: newId,
      idPatient: patientId,
      status: -1,
      createdAt: new Date(),
      data: JSON.stringify({ leads: {} }),
    });
    await newImplantation.save();
  }
  const maps = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 1", "", true);
  const implantationRow = await new ImplantationModel().findFirst([`${patientId}`], "ID_PATIENT = :1", "", true);
  const resources = await new VMapResourceModel().findFree([`${implantationRow.idMapResult}`], "ID_MAP = :1", "", true); 
  let resourcesData = [];
  resources.forEach((item) => {
    const url = res.locals.getStorageUrl(patientId, implantationRow.idMapResult, item.idResourceType, item.uniqueId);
    let toAdd = {
      type: item.idResourceTypeText,
      url: url,
    };
    resourcesData.push(toAdd);
  });
  const implantation = {
    leads: {
      LV: {
        axis: "0.770;2.890;1.000;3.000",
        ventricular: "0.770;2.890;1.000;3.000",
      },
      RV: [
        {
          axis: "0.770;2.890;1.000;3.000",
          ventricular: "0.770;2.890;1.000;3.000",
        },
        {
          axis: "0.770;2.890;1.000;3.000",
          ventricular: "0.770;2.890;1.000;3.000",
        },
      ],
    }
  };
  return res.render("patient-implantation", { patient: res.locals.selectedPatient, patientId: patientId, initialAssessments: maps, resources: JSON.stringify(resourcesData), implantation });
});
// patientRoutes.route("/:id/implantation-new").get(async (req, res) => {
//   const patientId = req.params.id;
//   const implantations = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE = 2", "", true);
//   return res.render("patient-implantation", { patient: res.locals.selectedPatient, patientId: patientId, initialAssessments: maps });
// }).post(async (req, res) => {

// });

patientRoutes.route("/:id/simulations").get(async (req, res) => {
  const patientId = req.params.id;
  logger.debug("GET /%s/simulations", patientId);
  const resources = await new SimulationModel().findFree([`${patientId}`], "ID_PATIENT = :1", "", true);
  return res.render("simulations", {
    patient: res.locals.selectedPatient,
    patientId: patientId,
    list: resources,
  });
});

patientRoutes.route("/:id/simulation").get(async (req, res) => {
  const patientId = req.params.id;
  const simId = req.query.simId;
  logger.debug("GET /%s/simulations", patientId);
  const simulation = await new SimulationModel().findFirst([simId], "ID=:1", "", true);
  const resultMapId = simulation.idMapResult;
  const mapResources = await new VMapResourceModel().findFree([`${resultMapId}`], "ID_MAP = :1", "", true);
  let resources = [];
  mapResources.forEach((item) => {
    const url = res.locals.getStorageUrl(item.idPatient, resultMapId, item.idResourceType, item.uniqueId);
    let toAdd = {
      type: item.idResourceTypeText,
      url: url,
    };
    resources.push(toAdd);
  });
  simulation.leads = JSON.parse(simulation.data);
  return res.render("simulation", {
    patientId: patientId,
    simulation: simulation,
    resources: JSON.stringify(resources),
  });
});

patientRoutes
  .route("/:id/simulation-new")
  .get(async function (req, res) {
    const patientId = req.params.id;
    const mapId = req.query.mapId;
    //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
    const mapResources = await new VMapResourceModel().findFree([`${mapId}`], "ID_MAP = :1", "", true);
    let resources = [];
    mapResources.forEach((item) => {
      const url = res.locals.getStorageUrl(patientId, mapId, item.idResourceType, item.uniqueId);
      let toAdd = {
        type: item.idResourceTypeText,
        url: url,
      };
      resources.push(toAdd);
    });
    if (mapResources) {
      return res.render("simulation-new", {
        patientId: patientId,
        simData: {},
        mapId: mapId,
        resources: JSON.stringify(resources),
      });
    } else {
      return res.redirect("/404");
    }
  })
  .post(async function (req, res) {
    const patientId = req.params.id;
    const mapId = req.query.mapId;

    const newIdSim = await utils.getSequenceNextVal("S_SIMULATIONS_ID");
    const mapIdResult = await utils.getSequenceNextVal("S_MAPS_ID");
    const { rVentricular, rvx, rvy, rvz, lVentricular, lvx, lvy, lvz } = req.body;
    const leadData = {
      RV: {
        axis: `${rvx},${rvy},${rvz}`,
        ventricular: `${rVentricular}`,
      },
      LV: {
        axis: `${lvx},${lvy},${lvz}`,
        ventricular: `${lVentricular}`,
      },
    };
    const { notes } = req.body;
    const simData = {
      id: newIdSim,
      idMap: mapId, //La mappa di origine
      idPatient: patientId,
      idMapResult: mapIdResult, //La mappa del risultato.
      status: -1,
      description: notes,
      createdAt: new Date(),
      data: JSON.stringify(leadData),
    };
    const resultMapData = {
      id: mapIdResult,
      description: notes,
      idPatient: patientId,
      status: -1,
      creationDate: new Date(),
      mapTag: "before",
      idUserCreate: 1,
      type: 1, // 1 <- tipo mappa risultato simulazione.
    };
    //creo lo storage path della mappa:
    const projectRoot = process.cwd();
    const dirPath = path.join(projectRoot, `storage`, `${patientId}`, `map-${mapIdResult}`);
    fs.mkdirSync(dirPath, { recursive: true });

    const simResult = await new SimulationModel(simData).save();
    const mapRresult = await new MapModel(resultMapData).save();

    const ventriculars = `${rVentricular},${lVentricular}`;
    //il parametro da inviare alla simulazione.
    const ppos = ventriculars.replaceAll(",", ";");

    const newJobId1 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const jobData1 = {
      id: newJobId1,
      idPatient: patientId,
      idMap: mapId,
      jobStatus: 0,
      stage: "create_simulation",
      idEcg: newIdSim,
      createdAt: new Date(),
      jobData: JSON.stringify({
        idSim: newIdSim,
        ppol: ppos,
      }),
    };

    const sched1 = await new JobSchedulerSocketModel(jobData1).save();
    await delay(1500); // Aspetta un tot.

    const newJobId2 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const jobData2 = {
      id: newJobId2,
      idPatient: patientId,
      idMap: mapIdResult, // <- la mappa di destinazione.
      jobStatus: 0,
      stage: "simulation",
      idEcg: newIdSim,
      createdAt: new Date(),
      jobData: JSON.stringify({
        idSim: newIdSim,
        ppol: ppos,
      }),
    };
    const sched2 = await new JobSchedulerSocketModel(jobData2).save();

    res.redirect(`/patients/${patientId}/simulations`);
  });

patientRoutes.route("/:id/vtk-viewer").get(async function (req, res) {
  const patientId = req.params.id;
  const mapId = req.query.mapId;
  //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
  const mapResources = await new VMapResourceModel().findFree([`${mapId}`], "ID_MAP = :1", "", true);
  let resources = [];
  mapResources.forEach((item) => {
    const url = res.locals.getStorageUrl(patientId, mapId, item.idResourceType, item.uniqueId);
    let toAdd = {
      type: item.idResourceTypeText,
      url: url,
    };
    resources.push(toAdd);
  });
  if (mapResources) {
    res.render("activation-map", { patientId: patientId, mapId: mapId, resources: JSON.stringify(resources) });
  } else {
    return res.redirect("/404");
  }
});

patientRoutes.route("/:id/ecg-small").get(async function (req, res) {
  const patientId = req.params.id;
  const mapId = req.query.mapId;
  //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
  const mapResources = await new VMapResourceModel().findFree([`${mapId}`], "ID_MAP = :1 AND ID_RESOURCE_TYPE = 1", "", true);
  const data = mapResources[0];
  const ecgUrl = res.locals.getStorageUrl(patientId, mapId, 1, data.uniqueId);
  logger.debug("GET /%s/ecg", patientId, data);
  return res.render("ecg-small", { ecgUrl: ecgUrl });
});

patientRoutes.get("/:id", async (req, res) => {
  const patientId = req.params.id;
  const url = process.env.SUMMARY_URL + '?patientId=' + patientId;
  return res.render("fullscreen-iframe", { url });
});

patientRoutes.get("/:id/resources", async (req, res) => {
  const patientId = req.params.id;
  const resources = await new PatientResourceModel().findFree(
    [`${patientId}`],
    "ID_PATIENT = :1 AND (ID_RESOURCE_TYPE = 1 OR ID_RESOURCE_TYPE = 2)",
    "RESOURCE_ORDER",
    true
  );
  return res.render("patient-page", {
    patient: res.locals.selectedPatient,
    list: resources,
  });
});
export default patientRoutes;
