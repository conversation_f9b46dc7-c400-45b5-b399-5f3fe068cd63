<button id="hamburgerBtn" class="absolute top-16 right-4 bg-darkred text-white px-4 py-2 rounded-lg flex items-center justify-center w-12 h-12">
  <i class="fa-solid fa-bars text-xl"></i>
</button>

<!-- show options with checkbox: Ventricles, CoronarySinus, Epicardium, LV Endocardium, RV Endocardium, Suggested Target Points  -->
<div id="hamburgerMenu" class="absolute top-20 right-4 bg-darkgray p-2 rounded-xl w-[300px] hidden">
  <div class="flex flex-col gap-2">
    <!-- close button -->
    <button onclick="hamburgerMenu.classList.add('hidden')" class="self-end text-white hover:text-gray-300">
      <i class="fa fa-times text-2xl"></i>
    </button>
    <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
      <div class="flex items-center gap-4">
        <input type="checkbox" id="ventricles" data-id="WHOLE_HEART" checked class="w-6 h-6 accent-darkred" />
        <span class="text-lg text-white">Ventricles</span>
      </div>
      <select id="ventriclesOptions" data-id="WHOLE_HEART" class="bg-darkgray-light text-white border border-gray-300 rounded p-1">
        <option value="Solid color">Anatomy</option>
        <option value="Activation_time" selected>Activation Map</option>
        <option value="AHA_new">AHA sectorization</option>
        <option value="For LDBR">Epi-endo surfaces</option>
        <option value="Neibors for epi-base">Neibors for epi-base</option>
      </select>
      <input type="range" id="ventriclesSlider" data-id="WHOLE_HEART" min="0" max="100" value="100" class="w-full" />
    </div>
    <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
      <div class="flex items-center gap-4">
        <input type="checkbox" id="coronarySinus" data-id="CORONARY_SINUS" checked class="w-6 h-6 accent-darkred" />
        <span class="text-lg text-white">Coronary Sinus</span>
      </div>
      <!-- Combobox -->
      <select id="coronarySinusOptions" data-id="CORONARY_SINUS" class="bg-darkgray-light text-white border border-gray-300 rounded p-1">
        <option value="Solid color">Anatomy</option>
        <option value="Normals">Normals</option>
      </select>
      <!-- Slider -->
      <input type="range" id="coronarySinusSlider" data-id="CORONARY_SINUS" min="0" max="100" value="90" class="w-full" />
    </div>
    <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white disabled">
      <div class="flex items-center gap-4">
        <input type="checkbox" id="epicardium" data-id="EPICARDIUM" class="w-6 h-6 accent-darkred" />
        <span class="text-lg text-white">Epicardium</span>
      </div>
      <!-- Combobox -->
      <select id="epicardiumOptions" data-id="EPICARDIUM" class="bg-darkgray-light text-white border border-gray-300 rounded p-1" style="display:none">
        <option value="Solid color">Anatomy</option>
        <option value="Activation_time" selected>Activation Map</option>
        <option value="AHA_new">AHA sectorization</option>
      </select>
      <!-- Slider -->
      <input type="range" id="epicardiumSlider" data-id="EPICARDIUM" min="0" max="100" value="100" class="w-full" style="display:none" />
    </div>
    
    <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
      <div class="flex items-center gap-4">
        <input type="checkbox" id="lvEndocardium" data-id="LV_ENDOCARDIUM" class="w-6 h-6 accent-darkred" />
        <span class="text-lg text-white">LV Endocardium</span>
      </div>
      <!-- Combobox -->
      <select id="lvEndocardiumOptions" data-id="LV_ENDOCARDIUM" class="bg-darkgray-light text-white border border-gray-300 rounded p-1" style="display:none">
        <option value="Solid color">Anatomy</option>
        <option value="Activation_time" selected>Activation Map</option>
        <option value="AHA_new">AHA sectorization</option>
      </select>
      <!-- Slider -->
      <input type="range" id="lvEndocardiumSlider" data-id="LV_ENDOCARDIUM" min="0" max="100" value="100" class="w-full" style="display:none" />
    </div>

    <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
      <div class="flex items-center gap-4">
        <input type="checkbox" id="rvEndocardium" data-id="RV_ENDOCARDIUM" class="w-6 h-6 accent-darkred" />
        <span class="text-lg text-white">RV Endocardium</span>
      </div>
      <!-- Combobox -->
      <select id="rvEndocardiumOptions" data-id="RV_ENDOCARDIUM" class="bg-darkgray-light text-white border border-gray-300 rounded p-1" style="display:none">
        <option value="Solid color">Anatomy</option>
        <option value="Activation_time" selected>Activation Map</option>
        <option value="AHA_new">AHA sectorization</option>
      </select>
      <!-- Slider -->
      <input type="range" id="rvEndocardiumSlider" data-id="RV_ENDOCARDIUM" min="0" max="100" value="100" class="w-full" style="display:none"/>
    </div>
    <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
      <div class="flex items-center gap-4">
        <input type="checkbox" id="suggestedTargetPoints" data-id="LV_TARGET" class="w-6 h-6 accent-darkred" />
        <span class="text-lg text-white">Suggested Target Points</span>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const hamburgerBtn = document.getElementById('hamburgerBtn');
    const hamburgerMenu = document.getElementById('hamburgerMenu');

    hamburgerBtn.addEventListener('click', (event) => {
      hamburgerMenu.classList.toggle('hidden');
    });
  });
</script>
