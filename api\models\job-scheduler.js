import { AbstractDbCrudModel } from "cruder/abstractCrud";
import  <PERSON><PERSON>  from 'joi';

class JobSchedulerSocketModel extends AbstractDbCrudModel {
    static schema = {
        schemaName: "NICE",
        tableName: "PROCESSING_JOBS",
        keys: ["ID"],
        fields: ["ID_PATIENT", "ID_MAP", "ID_ECG", "STAGE", "CREATED_AT", "JOB_STATUS", "JOB_DATA"],
        mapping: {
            ID: "id",
            ID_PATIENT: "idPatient",
            ID_MAP: "idMap",
            ID_ECG: "idEcg",
            STAGE: "stage",
            CREATED_AT: "createdAt",
            JOB_STATUS: "jobStatus",
            JOB_DATA: 'jobData'
        },
        joi: Joi.object({
            
        }),
    };
    constructor(data = {}, dbType = "oracle") {
        super(data, dbType);
    }
}
export default JobSchedulerSocketModel;
