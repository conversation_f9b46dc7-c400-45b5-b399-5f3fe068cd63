const dummy = require("dotenv").config();
const fs = require("fs");

const values = {};

values.imRunningInDocker = fs.existsSync("/.dockerenv") || false;

values.basePath = process.env.VIRTUALDIRPATH || "";
values.proxyList = process.env.PROXY_LIST || "";

//Il tipo del db da utilizzare in alcune librerie multi-db.
values.medCommonDbType = process.env.MEDCOMMON_DB || process.env.DB_TYPE || "oracle";

//#region ORACLE (Prefisso ORA_)
values.userOracle = process.env.ORA_USER || process.env.USERORACLE || "";
values.passOracle = process.env.ORA_PASS || process.env.PASSORACLE || "";
values.connectString = process.env.ORA_STRING || process.env.CONNECTSTRING || "";
values.oraPoolIncrement = Number(process.env.ORA_POOL_INCREMENT) || 1;
values.oraPoolsMax = Number(process.env.ORA_POOL_MAX) || 100;
values.oraPoolsMin = Number(process.env.ORA_POOL_MIN) || 1;
values.instantClient = process.env.ORA_INSTANT_CLIENT || process.env.INSTANTCLIENT;
//Flag per impostare o meno il package utente.
values.setPackageUtente = process.env.SET_PACKAGE_UTENTE || "S"; // Default sempre attivo.
//Flag per impostare o meno set nls date su ogni connessione staccata.
values.setNlsDateFormat = process.env.SET_NLS_DATE_FORMAT || "S"; // Default sempre attivo.
//#endregion

if (values.userOracle && values.passOracle) {
  values.oracleConfig = {
    user: values.userOracle,
    password: values.passOracle,
    connectString: values.connectString,
    poolMin: values.oraPoolsMin,
    poolMax: values.oraPoolsMax,
    poolIncrement: values.oraPoolIncrement,
    events: true,
  };
}

if (process.env.MARIADB_HOST && process.env.MARIADB_USER) {
  values.mariaConfig = {
    host: process.env.MARIADB_HOST || "",
    user: process.env.MARIADB_USER || "",
    password: process.env.MARIADB_PASSWORD || "",
    database: process.env.MARIADB_DB || "",
    connectionLimit: Number(process.env.MARIADB_CONN_LIMIT) || 20,
  };
}
if (process.env.POSTGRES_HOST && process.env.POSTGRES_USER) {
  values.postgresConfig = {
    host: process.env.POSTGRES_HOST || "",
    user: process.env.POSTGRES_USER || "",
    password: process.env.POSTGRES_PASSWORD || "",
    port: process.env.POSTGRES_PORT || 5432,
    database: process.env.POSTGRES_DB || undefined,
    ssl: process.env.POSTGRES_SSL ? true : false,
  };
}

values.ipAddress = process.env.IP_ADDRESS;
values.port = process.env.PORT;
values.node_env = process.env.NODE_ENV || "";

values.site = process.env.SITE || "";
values.application = process.env.APPLICATION || "";
values.code = process.env.CODE || "";

//Percorso dei file di log del logger.
values.logpath = process.env.LOGPATH;
//Livello dei log salvati su file.
values.logFileLevel = process.env.LOG_FILE_LEVEL || "silly";
//Livello dei messaggi inviati alla console.
values.logConsoleLevel = process.env.LOG_CONSOLE_LEVEL || "silly";
//Attiva l'endpoint di proxy dei logs (es. per apps flutter, etc. da inserire in un express endpoint)
values.logProxyEndpoint = process.env.LOG_PROXY_ENDPOINT || false;

values.oAuthHost = process.env.OAUTH_HOST || "http://localhost:5101/oauth2/oauth/token";

//Chiave segreta utilizzata dalla cripto (in oauth2 server per crittare/decrittare cose)
values.secretKey = process.env.SECRET_KEY || "";
values.cookieSecret = process.env.COOKIE_SECRET || "";

values.jwtSecretKey = process.env.JWT_SECRET_KEY || process.env.OAUTH2_JWT_SECRET || process.env.JWT_SECRET || "ABCDEF0123456789";
values.jwtIssuer = process.env.JWT_ISSUER_NAME || process.env.OAUTH2_JWT_ISSUER || process.env.OAUTH_ADMIN_HOSTNAME || "http://localhost";

values.serveWebHosting = process.env.DO_WEB_HOSTING || "N"; //Default sempre disattivato.
values.serveFlutterWebHost = process.env.DO_FLUTTER_WEBHOST || "N"; //Default sempre disattivato.

/**
 * Funzione che fà il loop di tutti i valori impostati e stampa l'esito.
 * @param {*} closeOnError
 * @returns
 */
function checkConfigValues(closeOnError) {
  let logger = require("medcommon/logger");
  let hasErrors = false;
  logger.info("Configuration (.env) check started:");
  for (const [key, value] of Object.entries(values)) {
    if (value == null) {
      logger.error(`❗ REQUIRED variable "${key}" value is missing!`);
      hasErrors = true;
    } else if (value === "") {
      logger.warn(`🖐  ${key} => EMPTY.`);
    } else {
      logger.info(`✅ config.${key} = %O`, value);
    }
  }
  if (closeOnError && hasErrors) {
    logger.error(`Configuration missing some keys. Exit process.`);
    process.exit(1);
  }
  return hasErrors;
}

module.exports = {
  ...values,
  checkEnvVariables: checkConfigValues,
};
