const logger = require("medcommon/logger");
const config = require("medcommon/config");
const DbAdapter = require("./db-adapter");

class DbMocker extends DbAdapter {
  #connPool;

  constructor() {
    super();
  }

  async init() {
    logger.info("DbMocker.init()");
  }

  /**
 * Recupera una connessione Oracle dal pool di connessioni.
 * @returns Oggetto connessione.
 */
  async getDBConnection() {
    logger.verbose("DbMocker.getDBConnection()");
  }

  async dbExecute(query, params = [], options = {}) {
    logger.verbose("DbMocker.dbExecute", query, params, options);
    let toRet = this.defDbExecuteResponse();
    let conn = await this.getDBConnection();
    toRet.dbOperation = this.detectQueryOperation(query);
    toRet.success = true;
    switch (toRet.dbOperation) {
      case 'SELECT':
        break;
      case 'INSERT':
      case 'UPDATE':
      case 'DELETE':
        toRet.rowsAffected = 1;
        break;
      default:
        // opzionale, nel caso in cui voglia gestire altri casi
        break;
    }
    return toRet;
  }

  formatTableName(schema = {}) {
    return schema.tableName;
  }

  formatAllFieldsNames(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    return allFields.join(',')
  }

  //Caso insert (a,b,c,d)
  formatAllFieldsParams(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    const allStr = allFields.map((key) => `<%${key}%>`).join(',');
    return allStr;
  }
  formatUpdateParams(schema = {}) {
    const updStr = schema.fields.map((key) => `${key}=<%${key}%>`).join(',');
    return updStr;
  }
  formatWhereParams(schema = {}) {
    const keys = schema.keys || [];
    const keysStr = schema.keys.map((key) => `${key}=<%${key}%>`).join(" AND ");
    return keysStr;
  }
  formatDbParam = (name, index) => `<%${name}%>`;
  formatDbField = (name) => `${name}`;
  dbParamRegex = () => /<%(.*?)%>/;
}
module.exports = DbMocker;
