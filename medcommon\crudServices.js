"use strict";

const { dbExecute, createParameter } = require("./dbManager");
const logger = require("./logger");

exports.create = (queries) => {
  const instance = {};

  //instance.createINParameter = (options) => createParameterIN(options);
  //instance.createOUTParameter = (options) => createParameterOUT(options);
  //instance.createINOUTParameter = (options) => createParameterINOUT(options);

  instance.createParameter = (options) => createParameter(options);

  /**
   * Run the listSql query
   */
  instance.getAll = (extraOptions) =>
    new Promise(function (resolve, reject) {
      let sqlStr = queries.listSql;
      let sqlParams = [];
      if (extraOptions) {
        if (extraOptions.query) sqlStr = extraOptions.query;
        if (extraOptions.queryParams) sqlParams = extraOptions.queryParams;
      }
      dbExecute(sqlStr, sqlParams, extraOptions).then(resolve).catch(reject);
    });

  instance.getByKeys = (keysArray, extraOptions) =>
    new Promise(function (resolve, reject) {
      dbExecute(queries.selectSql, keysArray, extraOptions)
        .then((data) => {
          if (data && data.rows && data.rows.length == 1) {
            resolve(data.rows[0]);
          } else {
            reject({
              status: 404,
              message: "Nessun record corrispondente con la chiave/chiavi forniti.",
            });
          }
        })
        .catch(reject);
    });

  instance.createNew = (dataArray, extraOptions) =>
    new Promise(function (resolve, reject) {
      dbExecute(queries.insertSql, dataArray, extraOptions)
        .then((data) => {
          if (data) {
            resolve(data);
          } else {
            reject({ message: "Operazione di creazione non riuscita." });
          }
        })
        .catch(reject);
    });

  instance.updateData = (dataArray, extraOptions) =>
    new Promise(function (resolve, reject) {
      dbExecute(queries.updateSql, dataArray, extraOptions)
        .then((data) => {
          if (data && data.affectedRows != 0) {
            resolve(data);
          } else {
            reject({
              status: 404,
              message: "Nessun record corrispondente con la chiave/chiavi forniti.",
            });
          }
        })
        .catch(reject);
    });

  instance.deleteData = (keysArray, extraOptions) =>
    new Promise(function (resolve, reject) {
      dbExecute(queries.deleteSql, keysArray, extraOptions)
        .then((data) => {
          if (data && data.affectedRows != 0) {
            resolve(data);
          } else {
            reject({
              status: 404,
              message: "Nessun record corrispondente con la chiave/chiavi forniti.",
            });
          }
        })
        .catch(reject);
    });

  instance.customQuery = (query, keysArray, extraOptions) =>
    new Promise(function (resolve, reject) {
      dbExecute(query, keysArray, extraOptions)
        .then((data) => {
          if (data && data.affectedRows != 0) {
            resolve(data);
          } else {
            reject({
              status: 404,
              message: "Nessun record corrispondente con la chiave/chiavi forniti.",
            });
          }
        })
        .catch(reject);
    });
  return instance;
};
