#pragma once

#include <memory>
#include <string>

class vtkDataSet;
class GeometryViewer
{
public:
  GeometryViewer();
  ~GeometryViewer();

  void Initialize();
  void Render();
  void ResetView(double zoom = 0.9);
  void SetSize(int width, int height);
  void SetPosition(int x, int y, int z);
  void SetViewUp(int x, int y, int z);

  /** Questo carica il modello del cuore intero */
  void LoadHeartModelFromMemory(std::uintptr_t buffer, std::size_t nbytes);
  /** Questo carica il modello dell'arteria coronarica */
  void LoadCoronaryModelFromMemory(const std::string &filename, std::uintptr_t buffer, std::size_t nbytes);

  void LoadVtkModelFromMemory(const std::string &filename, std::uintptr_t buffer, std::size_t nbytes, const std::string &actorname);
  
  std::string GetActorDataAsBase64(const std::string &actorName);

  void Start();
  void Halt();
  void Resume();

  void SetMouseWheelMotionFactor(float sensitivity);
  void AddActorsToRender();
  void SetActorVisibility(std::string actorName, bool visible);
  void SetActorOpacity(std::string actorName, double opacity);
  void SetActorMapName(std::string actorName, std::string opacity);

  void AddNamedActor(std::string actorName, double x, double y, double z);

  void SetTargetPosition(std::string actorName, double x, double y, double z);

private:
  class Internal;
  std::unique_ptr<Internal> P;
};

#ifdef __EMSCRIPTEN__
#include <emscripten/bind.h>
EMSCRIPTEN_BINDINGS(GeometryViewerJSBindings)
{
  emscripten::class_<GeometryViewer>("GeometryViewer")
      .constructor<>()
      .function("loadCoronaryModelFromMemory", &GeometryViewer::LoadCoronaryModelFromMemory)
      .function("loadHeartModelFromMemory", &GeometryViewer::LoadHeartModelFromMemory)
      .function("loadVtkModelFromMemory", &GeometryViewer::LoadVtkModelFromMemory)
      .function("addActorsToRender", &GeometryViewer::AddActorsToRender)
      .function("initialize", &GeometryViewer::Initialize)
      .function("render", &GeometryViewer::Render)
      .function("resetView", &GeometryViewer::ResetView)
      .function("setSize", &GeometryViewer::SetSize)
      .function("setViewUp", &GeometryViewer::SetViewUp)
      .function("setPosition", &GeometryViewer::SetPosition)
      .function("start", &GeometryViewer::Start)
      .function("halt", &GeometryViewer::Halt)
      .function("resume", &GeometryViewer::Resume)
      .function("setActorVisibility", &GeometryViewer::SetActorVisibility)
      .function("setActorOpacity", &GeometryViewer::SetActorOpacity)
      .function("setActorMapName", &GeometryViewer::SetActorMapName)
      .function("getActorDataAsBase64", &GeometryViewer::GetActorDataAsBase64)
      .function("addNamedActor", &GeometryViewer::AddNamedActor)
      .function("setTargetPosition", &GeometryViewer::SetTargetPosition)
      .function("setMouseWheelMotionFactor", &GeometryViewer::SetMouseWheelMotionFactor);
}
#endif
