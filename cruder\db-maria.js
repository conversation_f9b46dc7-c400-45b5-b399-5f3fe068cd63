const config = require("medcommon/config");
const logger = require("medcommon/logger");
const DbAdapter = require("./db-adapter");
const mariadb = require("mariadb");

class DbMaria extends DbAdapter {
  #connPool;

  constructor() {
    super();
  }

  async init() {
    try {
      //this.#connPool = mariadb.createPool(config.mariaConfig);
    }
    catch (error) {
      logger.error("ERROR on DbMaria.init(): %O", error);
    }
  }

  /**
 * Recupera una connessione Oracle dal pool di connessioni.
 * @returns Oggetto connessione.
 */
  async getDBConnection() {
    //let conn = await this.#connPool.getConnection();
    let conn = await mariadb.createConnection(config.mariaConfig);
    logger.debug("DbMaria.getDbConnection -> from connPool: %O", conn.info.threadId);
    return conn;
  }

  async dbExecute(query, params = [], options = {}) {
    logger.debug("DbMaria.dbExecute", query, params, options);
    let toRet = this.defDbExecuteResponse();
    toRet.dbOperation = this.detectQueryOperation(query);
    let conn;
    try {
      conn = await this.getDBConnection();
      const result = await conn.query(query, params);
      toRet = this.fillResponseFromResult(result);
    } catch (error) {
      logger.error("ERROR on DbMaria.dbExecute:%O", error);
      toRet.success = false;
      toRet.error = error.message;
    } finally {
      try {
        await conn.end();
      } catch (err) {
        logger.error("ERROR on DbMaria.conn.end(). %s", err.message);
      }
    }
    return toRet;
  }

  fillResponseFromResult(result) {
    let toRet = this.defDbExecuteResponse();
    //logger.info("DbMaria.dbExecute result:%O", result);
    if (result) {
      const resJson = {};
      if (Array.isArray(result)) {
        resJson.rows = result;
        resJson.rowsAffected = result.length;
      }
      if (result.affectedRows && result.affectedRows > 0) {
        resJson.rowsAffected = result.affectedRows;
      }
      resJson.success = true;
      toRet = { ...toRet, ...resJson };
    }
    return toRet;
  }

  async getNewTransaction() {
    const conn = await this.getDBConnection();
    // Inizia la transazione
    await conn.beginTransaction();
    return conn;
  }
  async transactExecute(conn, query, params) {
    logger.debug("DbMaria.transactExecute", query, params);
    let toRet = this.defDbExecuteResponse();
    toRet.dbOperation = this.detectQueryOperation(query);
    if (conn) {
      try {
        const result = await conn.query(query, params);
        //logger.info("DbMaria.transactExecute result:%O", result);
        toRet = this.fillResponseFromResult(result);
      } catch (error) {
        logger.error("ERROR on DbMaria.transactExecute:%O", error);
        toRet.success = false;
        toRet.error = error.message;
      }
    }
    return toRet;
  }

  async commit(conn) {
    if (conn) await conn.commit();
  }

  async rollback(conn) {
    if (conn) await conn.rollback();
  }

  /** Rilascia nel modo nativo la connessione.
   * @param {*} conn 
   */
  async release(conn) {
    if (conn) conn.release();
  }

  //#region Formattazione specifica queries del client db
  formatTableName(schema = {}) {
    return schema.tableName;
  }
  formatAllFieldsNames(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    return allFields.join(',')
  }
  //Caso insert into table (....) values(§,§,§,§)
  formatAllFieldsParams(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    const allStr = allFields.map((key) => `?`).join(',');
    return allStr;
  }
  //Caso update table set (A=§, B=§) where .... 
  formatUpdateParams(schema = {}) {
    const updStr = schema.fields.map((key) => `${key}=?`).join(',');
    return updStr;
  }
  //Caso della clausola WHERE => A=§ AND B=§ AND C=§
  formatWhereParams(schema = {}) {
    const keys = schema.keys || [];
    const keysStr = schema.keys.map((key) => `${key}=?`).join(" AND ");
    return keysStr;
  }
  formatDbParam = (name, index) => "?";
  dbParamRegex = () => /\?/g;
  //#endregion
  formatDbParam = (name, index) => `?`;
  formatDbField = (name) => `${name}`;
  dbParamRegex = () => /\?/g;
}
module.exports = DbMaria;
