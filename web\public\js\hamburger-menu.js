let timeout;
function debounce(func, delay) {
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), delay);
  };
}
function handleCheckboxChange(event) {
  const checkboxId = event.target.id; // Ottieni l'ID del checkbox
  const isChecked = event.target.checked; // Ottieni lo stato (checked o not checked)
  const dataId = event.target.getAttribute("data-id");
  console.log(`Checkbox ${checkboxId} (data-id: ${dataId}) is now ${isChecked ? "checked" : "unchecked"}`);
  const combobox = document.querySelector(`select[data-id="${dataId}"]`);
  const slider = document.querySelector(`input[type="range"][data-id="${dataId}"]`);
  if (combobox && slider) {
    if (isChecked) {
      combobox.style.display = "block";
      slider.style.display = "block";
    } else {
      combobox.style.display = "none";
      slider.style.display = "none";
    }
  }
  window.setActorVisibility(dataId, isChecked);
}
function handleSliderChange(event) {
  const dataId = event.getAttribute("data-id");
  window.setActorOpacity(dataId, event.value / 100);
}
// Funzione per gestire il cambiamento di stato dei checkbox
function handleComboboxChange(event) {
  const checkboxId = event.target.id; // Ottieni l'ID del checkbox
  const selValue = event.target.value; // Ottieni lo stato (checked o not checked)
  const dataId = event.target.getAttribute("data-id");
  window.setActorMapName(dataId, selValue);
}
// Aggiungi il gestore di eventi solo ai checkbox all'interno del div con ID 'hamburgerMenu'
document.querySelectorAll('#hamburgerMenu input[type="checkbox"]').forEach((checkbox) => {
  checkbox.addEventListener("change", handleCheckboxChange);
});
document.querySelectorAll('#hamburgerMenu input[type="range"]').forEach((slider) => {
  slider.addEventListener(
    "input",
    debounce(() => handleSliderChange(slider), 100)
  );
});
document.querySelectorAll("#hamburgerMenu select").forEach((select) => {
  select.addEventListener("change", handleComboboxChange);
});
