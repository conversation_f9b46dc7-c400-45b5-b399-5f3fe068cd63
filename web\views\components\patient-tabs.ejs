<div class="flex border-t border-gray-700 mb-4 w-full px-4">
    <% if (process.env.SUMMARY_URL) { %>
    <a href="/patients/<%=patient.id%>">
        <button class="tab-link px-7 py-4 text-2xl font-semibold text-gray-300 border-t-2 border-transparent hover:text-white" data-tab="">
            <%= t("patientTabs.summary") %>
        </button>
    </a>
    <%}%>
    <a href="/patients/<%=patient.id%>/resources">
        <button class="tab-link px-7 py-4 text-2xl font-semibold text-gray-300 border-t-2 border-transparent hover:text-white" data-tab="resources">
            <%= t("patientTabs.resources") %>
        </button>
    </a>
    <a href="/patients/<%=patient.id%>/activationMaps">
        <button
            class="tab-link px-7 py-4 text-2xl font-semibold text-gray-300 border-t-2 border-transparent hover:text-white" data-tab="activationMaps">
            <%= t("patientTabs.initialAssessment") %>
        </button>
    </a>
    <a href="/patients/<%=patient.id%>/simulations">
        <button
            class="tab-link px-7 py-4 text-2xl font-semibold text-gray-300 border-t-2 border-transparent hover:text-white" data-tab="simulations">
            <%= t("patientTabs.simulations") %>
        </button>
    </a>
    <a href="/patients/<%=patient.id%>/implantation">
        <button
            class="tab-link px-7 py-4 text-2xl font-semibold text-gray-300 border-t-2 border-transparent hover:text-white" data-tab="implantation">
            <%= t("patientTabs.implantation") %>
        </button>
    </a>
</div>

<script>
    const currentUrl = window.location.pathname;
    const tabLinks = document.querySelectorAll('.tab-link');
    const resourcesTab = document.querySelector('.tab-link[data-tab=""]');
    const isResources = currentUrl == `/patients/<%=patient.id%>`;
    if (isResources) {
        resourcesTab.classList.remove('text-gray-300', 'border-transparent', 'hover:text-white');
        resourcesTab.classList.add('bg-zinc-700', 'text-white', 'border-darkred');
    }
    else {
        tabLinks.forEach((link) => {
            if (!link.dataset.tab) return;
            if (currentUrl.includes(link.dataset.tab) || (link.dataset.tab === '' && currentUrl == '/patients/<%=patient.id%>')) {
                link.classList.remove('text-gray-300', 'border-transparent', 'hover:text-white');
                link.classList.add('bg-zinc-700', 'text-white', 'border-darkred');
                // link.setAttribute('disabled', true);
            }
        });
    }

</script>
