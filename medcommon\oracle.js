"use strict";
const oracledb = require("oracledb");
oracledb.autoCommit = false;
oracledb.fetchAsString = [oracledb.CLOB];

const config = require("./config");
const logger = require("./logger");

const defaultDoCommit = true;

let isInitiated = false;

let connPool = undefined;

let sessionDateSql = "alter session set nls_date_format ='YYYY-MM-DD\"T\"HH24:MI:SS'";

//let sessionTimeZoneSql = "alter session set time_zone='Greenwich'";

var connAttrs = {
  user: config.userOracle,
  password: config.passOracle,
  connectString: config.connectString,
  poolMin: config.oraPoolsMin,
  poolMax: config.oraPoolsMax,
  poolIncrement: config.oraPoolIncrement,
  events: true,
};

let elkData = {
  "log.origin.file.name": "medcommon/oracle.js",
};

/** Ci sono più controindicazioni che benefici nel utilizzare questo meccanismo per verificare lo stato di salute 
 * della connessione al db.  
 * Prende una connessione dal pool di connessioni (l'init se non è già stato fatto viene fatto nel getOracleConnection).
 * Il giusto compromesso tra affidabilità e riduzione di carichi al db è sfruttare il pool di connessioni.
 * Anche se la getOracleConnection restituisce una connessione, non è detto che questa sia veramente utilizzabile, 
 * ma internamente il pool di connessioni fà un ping ogni 60 s e ciò è meno impattante rispetto al ping.
 * Per maggiori informazioni: 
 * https://node-oracledb.readthedocs.io/en/latest/api_manual/connection.html#connection.ping
 * https://node-oracledb.readthedocs.io/en/latest/user_guide/tuning.html#roundtrips
 * @returns true|false
 */
async function pingDB() {
  let toRet = false;
  let pingConn
  try {
    pingConn = await getOracleConnection();
    let pingErr = await pingConn.ping();
    toRet = pingErr ? false : true;
  } catch (error) {
    logger.error("Ping DB Err: %O", error);
  } finally {
    if (pingConn) {
      await pingConn.close();
    }
  }
  return toRet;
}

function createPool() {
  //in Oracle viene fatto sotto nell'init....
}

/**
 * Inizializza l'istantClient e connection Pool di Oracle
 */
async function init() {
  if (!isInitiated) {
    //lo so.. ma questo primo try/catch è voluto/controllato.
    try {
      oracledb.initOracleClient({ libDir: config.instantClient });
      logger.info("Oracle Instant Client inizializzato: %s", config.instantClient);
    } catch (e) {
      logger.info("Oracle Instant Client già inizializzato.");
    }
    try {
      //Ora creo il connection Pool..
      connPool = await oracledb.createPool(connAttrs);
      logger.info("OracleConnection POOL creato.");
      isInitiated = true;
      //pingConn = await connPool.getConnection();
    } catch (error) {
      logger.error("Errore on init(). Create Pool fallito:%O", error, { error });
      isInitiated = false;
      throw error;
    }
  }
  return;
}

/**
 * Recupera una connessione Oracle dal pool di connessioni.
 * @returns Oggetto connessione.
 */
async function getOracleConnection(oraUser, elk = {}) {
  let conn = undefined;
  try {
    if (!isInitiated) {
      await init();
    }
    conn = await connPool.getConnection();
    if (conn) {
      let debugExtra = "";
      if (config.setNlsDateFormat === "S") {
        try {
          await conn.execute(sessionDateSql);
          debugExtra = "NLS_DATE";
        } catch (error) {
          logger.error("Error on setNlsDateFormat. Error:%s", error.message, { error: error });
        }
      }
      let pkgDone = await setPackagesOracle(conn, oraUser);
      if (pkgDone) {
        debugExtra = debugExtra + " PKG_USER";
      }
      logger.verbose("getOracleConnection:[%s]", debugExtra, { ...elk, ...elkData, oraUser });
    } else {
      logger.warn("Recupero connessione dal pool fallito!", { ...elkData });
    }
  } catch (error) {
    logger.error("Error on getOracleConnection:%O", error, { error });
    throw error;
  }
  return conn;
}

/**
 * Esegue la chiamata sul database e restituisce l'oggetto result della connessione,
 * altrimenti genera un errore che DEVE essere intercettato/gestito da fuori..
 * Es. extraOptions = {
 *   connection: LaConnessioneCheVoglio,
 *   doCommit: true | false -> se si verifica un errore fà il rollback altrimenti fà il commit.
 *   oraUser: { id: [idutente], name: [nomeutente] }
 * }
 * Se passo la connessione in extraOptions, NON viene chiusa al termine del execute,
 * inoltre per gestire il commit da esterno passare doCommit:false fino al
 * momento desiderato di commit, oppure il commit può essere gestito da fuori,
 * nemmeno i packages vengono impostati.
 * cosi come il rilascio della connessione..
 * @param {string} statement La query in formato stringa da eseguire.
 * @param {Array} bindParams Parametri da bindare alla query.
 * @param {ExtraOptions} extraOptions  Struttura json con dati aggiuntivi da passare.
 *
 * Passare dntLog:true su extraOptions per NON loggare dati sensibili (tipo password, etc.);
 */
async function oracleExecute(statement, bindParams = [], extraOptions = {}) {
  let conn;
  let result = [];
  // Di default il commit sulla connessione è disattivato ma l'opzione defaultDoCommit è true,
  // quindi per transazioni devo passare una connessione e il doCommit a false fino all'ultima operazione.
  let doCommit = extraOptions.doCommit !== undefined ? extraOptions.doCommit : defaultDoCommit;
  let doNotTrack = extraOptions.dntLog !== undefined ? extraOptions.dntLog : false;
  let elk = extraOptions.elk || {};
  let logMeta = {
    ...elkData,
    doCommit: doCommit,
    ...elk,
  };
  logger.debug("oracleExecute", logMeta);
  let opts = { outFormat: oracledb.OUT_FORMAT_OBJECT, autoCommit: doCommit };
  let closeConnection = true;
  try {
    //Posso passare una connesione che voglio e passare sempre quella, ma non voglio che venga chiusa.
    //verifico che sia 'Healty' altrimenti ne creo una...
    if (extraOptions.connection && extraOptions.connection.isHealthy()) {
      conn = extraOptions.connection;
      closeConnection = false;
    } else {
      conn = await getOracleConnection(extraOptions.oraUser, elk);
    }
    if (conn) {
      if (!doNotTrack) {
        logger.debug("oracleExecute.bindParams:%O", bindParams, logMeta);
      } else {
        logger.debug("oracleExecute.bindParams:[#_Do_Not_Track_#]", logMeta);
      }
      logger.debug("oracleExecute.statement:%O", statement, logMeta);
      result = await conn.execute(statement, bindParams, opts);
      logger.debug("oracleExecute.result:%O", result, logMeta);
    } else {
      logger.warn("Connessione da Pool NON riuscita!");
      throw "Connessione Oracle non riuscita.";
    }
  } catch (error) {
    logger.error("Error on conn.execute. %s", error.message, { ...logMeta, error });
    if (doCommit && conn && conn.isHealthy()) {
      await conn.rollback();
    }
    throw error;
  } finally {
    //Se ho passato una connessione come parametro non entro qui sotto, vedi sopra...
    if (conn && closeConnection) {
      // conn assignment worked, need to close
      try {
        await conn.close();
      } catch (error) {
        logger.error("Error on conn.close(). %s", error.message, { ...logMeta, error });
      }
    }
  }
  return result;
}
/**
 * Effettua una connessione/query di prova per verificare la connessione DB.
 * @param {*} req La richiesta
 * @param {*} res La risposta dal db (data&ora del server)
 */
async function verifyOracleConnection(req, res) {
  // #swagger.tags = ['Oracle']
  // #swagger.summary = 'Verifica la connessione con il DB Oracle'
  // #swagger.description = 'Endpoint per verificare la connessione al db.'
  var respCode = 500;
  var respJson = {};
  try {
    //let result = await oracleExecute("select (select sysdate from dual) as data, (select banner from v$version) as versione", [], {});
    let result = await oracleExecute("select ver.banner, sysdate from v$version ver where rownum=1", [], {});
    if (result) {
      respCode = 200;
      respJson = result.rows[0];
    }
  } catch (error) {
    logger.error("Error on verifyOracleConnection. %s", error.message);
    respJson = { message: error.message, error: error };
  } finally {
    res.status(respCode).send(respJson);
  }
}

/**
 *  Se passo per l'autenticazione utente mi ritrovo impostato sulla req
 *  l'oggetto
 */
async function setPackagesOracle(conn, userObject) {
  let toRet = false;
  if (config.setPackageUtente === "S") {
    logger.debug("setPackagesOracle for user:%O", { ...elkData, userObject });
    try {
      let { id, username, tod, uidStazione } = userObject || { id: "", username: "", tod: "", uidStazione: "" };
      if (id && username) {
        logger.debug("setPackageUtente.oraUser -> [%s, %s]", id, username, elkData);
        try {
          var utenteSql = "SELECT utente.getid(" + id + ",'" + username + "') FROM dual";
          await conn.execute(utenteSql);
          toRet = true;
        } catch (error) {
          logger.error("setPackagesOracle:error on utente.getid(). Error:%O", { error: error, ...elkData });
        }
      }
      if (uidStazione) {
        logger.debug("set utente.uidStazione=%s", uidStazione);
        try {
          await conn.execute(`begin utente.uidstazione:=${uidStazione}; end;`);
          toRet = true;
        } catch (error) {
          logger.error("setPackagesOracle:error on set utente.uidstazione. Error:%O", { error: error, ...elkData });
        }
      }
      if (tod) {
        logger.debug("setTipologia_client[%s]", tod);
        let packageName = "utente";
        try {
          const res = await conn.execute("SELECT txt FROM params WHERE UPPER( id ) = 'PACKAGE_NAME_TIPOLOGIA_CLIENT';");
          packageName = res.rows[0].TXT;
        } catch (error) {
          logger.info('setPackagesOracle: PACKAGE_NAME_TIPOLOGIA_CLIENT on table PARAMS not found, using "utente" as default', { error: error, ...elkData });
        }
        try {
          await conn.execute(`begin ` + packageName + `.tipologia_client:='WEB'; end;`);
          toRet = true;
        } catch (error) {
          logger.error("setPackagesOracle:error on set tipologia_client. Error:%O", { error: error, ...elkData });
        }
      }
    } catch (error) {
      logger.warn("try/catch on /medcommon/oracle/setPackageUtente error:${%s}", error.message);
    }
  }
  return toRet;
}

/**
 * Ogni DB ha il suo modo (e libreria di creazione parametri) qui faccio l'astrazione
 * perchè NON voglio essere obbligato a importare ovunque il require oracleDB (ASTRAZIONE)
 *
 * Es. options = {
 *   dir: direzione (IN | OUT | INOUT)
 *   val: valore del parametro,
 *   type: tipo del parametro stringa minuscola (string | date | number | buffer | clob | blob | cursor | buffer).
 *   maxSize: dimensione del campo (! default == 200)
 * }
 */
const createParameter = (options) => fillOptions(options);

function fillOptions(options) {
  let toRet = options;
  toRet.dir = stringToOracleDirection(options.dir);
  if (options.val !== undefined) {
    toRet.val = options.val;
  }
  if (options.type !== undefined) {
    toRet.type = stringToOracleType(options.type);
  }
  if (options.maxSize !== undefined) {
    toRet.maxSize = options.maxSize;
  }
  return toRet;
}

function stringToOracleDirection(stringDir) {
  let toRet = oracledb.BIND_IN;
  if (stringDir === "OUT") {
    toRet = oracledb.BIND_OUT;
  }
  if (stringDir === "INOUT") {
    toRet = oracledb.BIND_INOUT;
  }
  return toRet;
}

//https://www.zybuluo.com/WrRan/note/570707
function stringToOracleType(stringType) {
  if (stringType === "string") {
    return oracledb.STRING;
  }
  if (stringType === "number") {
    return oracledb.NUMBER;
  }
  if (stringType === "clob") {
    return oracledb.CLOB;
  }
  if (stringType === "blob") {
    return oracledb.BLOB;
  }
  if (stringType === "date") {
    return oracledb.DATE;
  }
  if (stringType === "buffer") {
    return oracledb.BUFFER;
  }
  if (stringType === "cursor") {
    return oracledb.CURSOR;
  }
}
/**
 * @typedef ExtraOptions
 * @property {oracledb.Connection} [connection] - La connessione da utilizzare (es. per transazioni)
 * @property {boolean} [doCommit] - Se fare il commit sulla transazione nel caso autocommit sia disattivato.
 * @property {jso} [oraUser] - Oggetto utente oracle per impostare i packages utente.
 * @property {object} [elk] - Oggetto json che viene passato al logger per tracciare transazioni tramite elk (ElasticSearch)..
 */

const ExtraOptions = class {
  constructor(data) {
    this.connection = data.conn || undefined;
    this.doCommit = data.doCommit || false;
    this.oraUser = data.oraUser || {};
    this.elk = data.elk || {};
  }
};

/** Esegue la query e ritorna la riga letta da db
 * @param {string} La query o stored procedure da richiamare.
 * @param {array} L'array di parametri da associare alla query
 * @param {ExtraOptions} Opzioni aggiuntive da passare al gestore db.
 * @returns false se la query non ritorna 1 riga, altrimenti la riga letta.
 */
async function dbSingleRow(statement, bindParams, extraOptions = {}) {
  let toRet = false;
  let result = await oracleExecute(statement, bindParams || [], { ...extraOptions });
  if (result && result.rows && result.rows.length == 1) {
    toRet = result.rows[0];
  }
  return toRet;
}
/** Quando faccio update / delete il db ritorna rowsAffected.
 *
 * @param {*} statement
 * @param {*} bindParams
 * @param {*} extraOptions
 * @returns (rowsAffected == 1)
 */
async function dbRowsAffected(statement, bindParams, extraOptions) {
  let result = await oracleExecute(statement, bindParams || [], extraOptions || {});
  return result && result.rowsAffected == 1;
}
/** Interroga il db per verificare se esiste l'oggetto con i parametri forniti.
 *
 * @param {String|'%'} objType Tipo oggetto da cercare (TABLE|VIEW|SYNONYM|etc.)
 * @param {String|'%'} owner Il proprietario dell'oggetto (default:%)
 * @param {String|'%'} objectName Il nome dell'oggetto da ricercare (default:%)
 * @param {*} options Opzioni da passare alla connessione db (es. elk, connection, doCommit, etc.)
 * @returns {true|false}
 */
async function existDbObject(objType = "%", owner = "%", objectName = "%", options = {}) {
  const getDbObjectSql = `select owner, object_name, object_type from sys.all_objects where OBJECT_NAME like :1 AND OWNER like :2 AND OBJECT_TYPE like :3`;
  let toRet = false;
  try {
    let data = await oracleExecute(getDbObjectSql, [objectName, owner, objType], options);
    toRet = data && data.rows && data.rows.length ? true : false;
    logger.verbose("DB Check IF [%s:'%s.%s'] EXIST -> %s", objType, owner, objectName, toRet);
  } catch (error) {}
  //logger.debug("DB OBJECT %s.%s (%s) EXIST:%s", owner, objectName, objType, toRet);
  return toRet;
}
/** Esegue un'array di commandi sql con evidenza di eventuali errori.
 *
 * @param {String} objType Tipo oggetto (solo per messaggio di log)
 * @param {String} schema Nome dello schema (solo per messaggio di log)
 * @param {String} objName  Nome della tabella (solo per messaggio di log)
 * @param {Array[String]} queries Array di commandi sql da eseguire.
 * @param {*|{}} options Opzioni da passare al db
 * @returns
 */
async function executeDbScriptsArray(objType, schema, objName, queries = [], options = {}) {
  let toRet = 0;
  logger.info("Esecuzione scripts per oggetto[%s]: %s.%s", objType, schema, objName);
  for (var i = 0; i < queries.length; i++) {
    let element = queries[i];
    try {
      await oracleExecute(element, [], options);
    } catch (error) {
      toRet++;
      logger.error("Errore scripts [%s](%s.%s) su indice %s :%O", objType, schema, objName, i, error, { error });
    }
  }
  return toRet;
}
/*
OWNER                NOT NULL VARCHAR2(128)  
TABLE_NAME           NOT NULL VARCHAR2(128)  
COLUMN_NAME          NOT NULL VARCHAR2(128)  
DATA_TYPE                     VARCHAR2(128)  
DATA_LENGTH          NOT NULL NUMBER         
DATA_PRECISION                NUMBER         
DATA_SCALE                    NUMBER         
NULLABLE                      VARCHAR2(1)
DATA_DEFAULT,
CHAR_LENGTH  
*/
/** Interroga la sys.all_tabs_columns e ritorna un dizionario (con key COLUMN_NAME) in modo da
 * poter eventualmente eseguire degli scripts di allineamento.
 *
 * @param {String} schema Owner della tabella.
 * @param {String} table Nome della tabella da interrogare.
 * @returns
 */
async function getTableSchema(schema, table, dbOptions = {}) {
  const getTableSchemaSql = `SELECT col.COLUMN_NAME, col.DATA_TYPE, col.DATA_LENGTH, col.DATA_PRECISION, col.DATA_SCALE, col.NULLABLE, col.DATA_DEFAULT, col.CHAR_LENGTH FROM sys.all_tab_columns col INNER JOIN sys.all_tables t on col.owner = t.owner and col.TABLE_NAME = t.table_name WHERE col.owner=:1 AND col.TABLE_NAME=:2`;
  let toRet = [];
  try {
    let data = await oracleExecute(getTableSchemaSql, [schema, table], dbOptions);
    if (data && data.rows) {
      data.rows.forEach((element) => {
        let toAdd = { dataType: element.DATA_TYPE, dataLength: element.DATA_LENGTH };
        toRet[element.COLUMN_NAME] = toAdd;
      });
    }
  } catch (error) {
    logger.warn("Errore per la tabella %s.%s", schema, table, error);
  }
  return toRet;
}
/*
async function createIfNotExist(objType, schema, table, queries = []) {
  let toRet = false;
  let objectExist = await existDbObject(objType, schema, table);
  if (!objectExist) {
    logger.warn("Oggetto %s.%s NON trovato, lo creo:", schema, table);
    for (var i = 0; i < queries.length; i++) {
      let element = queries[i];
      await oracle.dbExecute(element, [], {});
      logger.warn("EXECUTED:%O", element);
    }
    toRet = true;
  }
  return toRet;
}*/

module.exports = {
  oracledb,
  createPool,
  getDBConnection: getOracleConnection,
  dbExecute: oracleExecute,
  verifyDBConnection: verifyOracleConnection,
  createParameter: createParameter,
  setPackagesOracle: setPackagesOracle,
  stringToOracleDirection,
  stringToOracleType,
  pingDB,
  init,
  dbSingleRow,
  dbRowsAffected,
  existDbObject,
  executeDbScriptsArray,
  getTableSchema,
};
