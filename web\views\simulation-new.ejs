<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("viewer.heading") %> <% icon = "fa-solid fa-notes-medical" %> <%- include('components/head') %>
  </head>
  <body class="h-screen">
    <%- include('components/header') %>
    <main class="h-full w-full overflow-y-auto">
      <button id="hamburgerBtn" class="bg-darkred text-white px-4 py-2 rounded-lg absolute top-80 right-4 w-12 h-12">
        <i class="fa-solid fa-bars text-xl"></i>
      </button>
      <!-- 3D Viewer -->
      <div class="flex w-full justify-center items-center">
        <canvas id="vtk-3d-canvas" class="w-[1050px] h-[750px] hidden"></canvas>
      </div>
      <!-- 3D Viewer -->
      <div class="flex w-full justify-center items-center"><!-- spacer -->&nbsp;</div>
      <div class="flex w-full justify-center items-center mt-2">
        <form id="leadSettingsForm" class="flex flex-col items-center justify-center w-full rounded-lg bg-darkgray p-4 mx-4">
          <div class="w-full text-white text-3xl font-semibold my-4"><%= t("simulation.lead_settings") %></div>
          <div class="flex justify-between gap-6 rounded-lg text-white w-full p-2 m-2">
            <!-- LV Lead -->
            <div class="flex flex-col gap-2 border border-white rounded-lg w-1/2 p-2 m-2">
              <div class="flex items-center gap-3">
                <input type="checkbox" id="lvTarget" name="lead" data-id="LV_TARGET" class="w-12 h-12 accent-darkred lead-toggle" />
                <label for="lvTarget" class="text-2xl font-semibold text-red-600"><%= t("simulation.lvLead") %></label>
              </div>
              <div class="flex flex-row justify-between p-2">
                <div class="flex flex-col">
                  <p class="text-xl">X:&nbsp;<span id="lvx" name="lvx" class="text-xl">0</span></p>
                </div>
                <div class="flex flex-col">
                  <p class="text-xl">Y:&nbsp;<span id="lvy" name="lvy" class="text-xl">0</span></p>
                </div>
                <div class="flex flex-col">
                  <p class="text-xl">Z:&nbsp;<span id="lvz" name="lvz" class="text-xl">0</span></p>
                </div>
              </div>
              <div class="flex flex-row justify-between p-2">
                <div class="flex flex-col">
                  <p class="text-xl">Vent.Coords:&nbsp;<span id="lVentrCoords" name="lVentrCoords" class="text-xl">- ; - ; - ; -</span></p>
                </div>
              </div>
              <input type="hidden" name="lv_point" id="lv_point" value="" required />
              <input type="hidden" id="lv_ventricular" name="lv_ventricular" value="0;0;0;0" />
            </div>
            <div class="flex flex-col gap-2 border border-white rounded-lg w-1/2 p-2 m-2">
              <div class="flex items-center gap-3">
                <input type="checkbox" id="rvTarget" name="lead" data-id="RV_TARGET" class="w-12 h-12 accent-darkred lead-toggle" />
                <label for="rvTarget" class="text-2xl font-semibold text-blue-600"><%= t("simulation.rvLead") %></label>
              </div>
              <div class="flex flex-row justify-between p-2">
                <div class="flex flex-col">
                  <p class="text-xl">X:&nbsp;<span id="rvx" name="rvx" class="text-xl">0</span></p>
                </div>
                <div class="flex flex-col">
                  <p class="text-xl">Y:&nbsp;<span id="rvy" name="rvy" class="text-xl">0</span></p>
                </div>
                <div class="flex flex-col">
                  <p class="text-xl">Z:&nbsp;<span id="rvz" name="rvz" class="text-xl">0</span></p>
                </div>
              </div>
              <div class="flex flex-row justify-between p-2">
                <div class="flex flex-col">
                  <p class="text-xl">Vent.Coords:&nbsp;<span id="rVentrCoords" name="rVentrCoords" class="text-xl">- ; - ; - ; -</span></p>
                </div>
              </div>
              <input type="hidden" name="rv_point" id="rv_point" value="" required />
              <input type="hidden" id="rv_ventricular" name="rv_ventricular" value="0;0;0;0" />
            </div>
            <script>
              document.querySelectorAll(".lead-toggle").forEach((input) => {
                input.addEventListener("change", function () {
                  if (this.checked) {
                    // Uncheck all others
                    document.querySelectorAll(".lead-toggle").forEach((el) => {
                      if (el !== this) el.checked = false;
                    });
                  }
                });
              });
            </script>
          </div>
          <div class="flex flex-col gap-2 mt-4 w-full">
            <label for="notes" class="text-white text-xl font-semibold">Notes</label>
            <input
              type="text"
              id="notes"
              name="notes"
              class="p-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-darkred"
              placeholder="<%= t('simulation.notesPlaceholder') %>"
              required
            />
          </div>
          <button type="submit" class="px-4 mt-8 py-2 bg-darkred text-white rounded hover:bg-darkred transition w-full">
            <p class="text-2xl font-semibold"><%= t("simulation.start") %></p>
          </button>
        </form>
      </div>
      <!-- show options with checkbox: Ventricles, CoronarySinus, Epicardium, LV Endocardium, RV Endocardium, Suggested Target Points  -->
      <div id="hamburgerMenu" class="absolute top-80 right-4 bg-darkgray p-2 rounded-xl w-[300px] hidden"><%- include('components/hamburger-options') %></div>
    </main>
    <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
      <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
      <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
    </div>
    <div id="blurOverlay" class="fixed inset-0 hidden bg-darkgray bg-opacity-77 backdrop-blur-sm"></div>
    <div id="alertDialog" class="fixed inset-0 flex items-center justify-center text-white bg-opacity-50 hidden">
      <div class="rounded-lg bg-darkred shadow-lg p-6 w-1/3">
        <h2 class="text-xl font-semibold mb-4"><%=t('about')%></h2>
        <p id="alertMessage" class="mb-4">Questo è un messaggio di avviso.</p>
        <div class="flex justify-end">
          <button onclick="closeDialog()" class="px-4 py-2 bg-red-700 hover:bg-red-600 text-white rounded transition"><%=t('back')%></button>
        </div>
      </div>
    </div>
    <script src="/js/hamburger-menu.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const hamburgerBtn = document.getElementById("hamburgerBtn");
        const hamburgerMenu = document.getElementById("hamburgerMenu");
        hamburgerBtn.addEventListener("click", (event) => {
          hamburgerMenu.classList.toggle("hidden");
        });
        document.getElementById("leadSettingsForm").addEventListener("submit", async (event) => {
          event.preventDefault(); // Previeni il comportamento predefinito del form
          if (validateForm()) {
            try {
              const data = {
                notes: document.getElementById("notes").value,
                rv_point: document.getElementById("rv_point").value,
                lv_point: document.getElementById("lv_point").value,
                lv_ventricular: document.getElementById("lv_ventricular").value,
                rv_ventricular: document.getElementById("rv_ventricular").value,
              };
              const response = await fetch("/patients/<%=patientId%>/simulation-new?mapId=<%=mapId%>", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json", // Imposta il tipo di contenuto su JSON
                },
                body: JSON.stringify(data), // Converti l'oggetto in una stringa JSON
              });
              if (response.ok) {
                // Mostra l'errore all'utente
                //document.getElementById("errorMessage").innerText = result.message;
                window.location.href = "/patients/<%=patientId%>/simulations";
              } else {
                // Gestisci il successo (ad esempio, reindirizza o mostra un messaggio di successo)
                hideLoading();
                showDialog("error");
              }
            } catch (error) {
              console.error("Error:", error);
            }
          }
        });
      });
    </script>
    <script>
      let viewer;
      let models = <%- resources %>;

      const loadingScreenDiv = document.getElementById("loadingScreen");
      const vtkContainer = document.getElementById("vtk-3d-canvas");

      let canvas_width = 1050;
      let canvas_height = 750;

      vtkContainer.width = canvas_width;
      vtkContainer.height = canvas_height;
      vtkContainer.style.width = canvas_width + "px";
      vtkContainer.style.height = canvas_height + "px";

      function hideLoading() {
        if (loadingScreenDiv) {
          loadingScreenDiv.classList.add("hidden");
        }
        if (vtkContainer.classList) {
          vtkContainer.classList.remove("hidden");
        }
      }

      window.addEventListener("ModuleLoaded", async () => {
        viewer = new VtkViewer("#vtk-3d-canvas", { models: models });
        await viewer.viewerInit();
        viewer.setSize(canvas_width, canvas_height);
        await viewer.refresh();
        window.viewer = viewer;
        hideLoading();
      });

      function onCellClickEvent(actor, x, y, z, ventricular) {
        console.log("onCellClickEvent received from C++: ", actor, x, y, z, ventricular);
        const rvTarget = document.getElementById("rvTarget");
        const lvTarget = document.getElementById("lvTarget");
        if (actor == "WHOLE_HEART") {
          if (rvTarget.checked) {
            // Logica per RV TARGET
            console.log("RV Target selected");
            viewer.setTargetPosition("RV_TARGET", x, y, z);
            //Valori del post
            document.getElementById("rv_ventricular").value = ventricular.join(",");
            document.getElementById("rv_point").value = x + "," + y + "," + z;
            //Valori a video
            document.getElementById("rVentrCoords").innerText = ventricular.join(" ; ");
            document.getElementById("rvx").innerText = x;
            document.getElementById("rvy").innerText = y;
            document.getElementById("rvz").innerText = z;
            rvTarget.checked = false;
          } else if (lvTarget.checked) {
            // Logica per LV TARGET
            console.log("LV Target selected");
            viewer.setTargetPosition("LV_TARGET", x, y, z);
            //Valori del post
            document.getElementById("lv_ventricular").value = ventricular.join(",");
            document.getElementById("lv_point").value = x + "," + y + "," + z;
            //Valori a video
            document.getElementById("lVentrCoords").innerText = ventricular.join(" ; ");
            document.getElementById("lvx").innerText = x;
            document.getElementById("lvy").innerText = y;
            document.getElementById("lvz").innerText = z;
            lvTarget.checked = false;
          }
        }
      }

      function validateForm() {
        const rvPoint = document.getElementById("rv_point").value;
        const lvPoint = document.getElementById("lv_point").value;
        let message = "";
        if (!lvPoint) {
          message += '<%= t("simulation.lvLead") %>: <%= t("required") %> \n';
        }
        if (!rvPoint) {
          message += '<%= t("simulation.rvLead") %>: <%= t("required") %> \n';
        }
        if (message) {
          event.preventDefault(); // Impedisce l'invio del modulo
          showDialog(message);
          return false;
        }
        loadingScreenDiv.classList.remove("hidden");
        return true;
      }
      function showDialog(message) {
        document.getElementById("alertMessage").innerText = message;
        const dialog = document.getElementById("alertDialog");
        const blurOverlay = document.getElementById("blurOverlay");
        blurOverlay.classList.remove("hidden");
        dialog.classList.remove("hidden");
        dialog.style.pointerEvents = "auto"; // Permetti l'interazione con il dialogo
        document.body.style.pointerEvents = "none";
      }
      function closeDialog() {
        //document.getElementById("alertDialog").classList.add("hidden");
         const blurOverlay = document.getElementById("blurOverlay");
         blurOverlay.classList.add("hidden");
         const dialog = document.getElementById("alertDialog");
         dialog.classList.add("hidden");
         dialog.style.pointerEvents = "none"; // Disabilita l'interazione con il dialogo
         document.body.style.pointerEvents = "auto";
      }
    </script>
    <script src="/js/vtk-viewer.js" type="module"></script>
  </body>
</html>
