<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("viewer.heading") %> <% icon = "fa-solid fa-notes-medical" %> <%- include('components/head') %>
  </head>
  <body>
    <%- include('components/header') %>
    <main>
      <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
        <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
        <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
      </div>

      <div class="flex flex-col items-center justify-center w-full rounded-lg bg-darkgray p-2 mx-4">
        <button id="hamburgerBtn" class="bg-darkred text-white px-4 py-2 rounded-lg absolute top-80 right-4 w-12 h-12">
          <i class="fa-solid fa-bars text-xl"></i>
        </button>

        <!-- 3D Viewer -->
        <canvas id="vtk-3d-canvas" class="w-[1050px] h-[750px] hidden"></canvas>

        <form id="leadSettingsForm" method="post" class="flex flex-col items-center justify-center w-full rounded-lg bg-darkgray p-4 mx-4">
          <div class="w-full text-white text-3xl font-semibold my-4"><%= t("simulation.lead_settings") %></div>

          <div class="flex justify-between gap-6 rounded-lg text-white w-full">
            <!-- LV Lead -->
            <div class="flex flex-col gap-2">
              <input type="hidden" id="rVentricular" name="rVentricular" value="0" />
              <div class="flex items-center gap-3">
                <input type="checkbox" id="lvTarget" name="lead" data-id="LV_TARGET" class="w-12 h-12 accent-darkred lead-toggle" />
                <label for="lvTarget" class="text-2xl font-semibold"><%= t("simulation.lvLead") %></label>
              </div>
              <div class="flex flex-wrap items-center gap-4 pt-2">
                <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                  <label for="lvx" class="text-xl">X</label>
                  <input type="text" id="lvx" name="lvx" class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                </div>
                <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                  <label for="lvy" class="text-xl">Y</label>
                  <input type="text" id="lvy" name="lvy" class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                </div>
                <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                  <label for="lvz" class="text-xl">Z</label>
                  <input type="text" id="lvz" name="lvz" class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                </div>
              </div>
            </div>

            <!-- RV Lead -->
            <div class="flex flex-col gap-2">
              <input type="hidden" id="lVentricular" name="lVentricular" value="0" />
              <div class="flex items-center gap-3">
                <input type="checkbox" id="rvTarget" name="lead" data-id="RV_TARGET" class="w-12 h-12 accent-darkred lead-toggle" />
                <label for="rvTarget" class="text-2xl font-semibold"><%= t("simulation.rvLead") %></label>
              </div>
              <div class="flex flex-wrap items-center gap-4 pt-2">
                <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                  <label for="rvx" class="text-xl">X</label>
                  <input type="text" id="rvx" name="rvx" class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                </div>
                <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                  <label for="rvy" class="text-xl">Y</label>
                  <input type="text" id="rvy" name="rvy" class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                </div>
                <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                  <label for="rvz" class="text-xl">Z</label>
                  <input type="text" id="rvz" name="rvz" class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                </div>
              </div>
            </div>
            <script>
              document.querySelectorAll(".lead-toggle").forEach((input) => {
                input.addEventListener("change", function () {
                  if (this.checked) {
                    // Uncheck all others
                    document.querySelectorAll(".lead-toggle").forEach((el) => {
                      if (el !== this) el.checked = false;
                    });
                  }
                });
              });
            </script>
          </div>
          <div class="flex flex-col gap-2 mt-4 w-full">
            <label for="notes" class="text-white text-xl font-semibold">Notes</label>
            <input
              type="text"
              id="notes"
              name="notes"
              class="p-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-darkred"
              placeholder="<%= t('simulation.notesPlaceholder') %>"
              required
            />
          </div>
          <button type="submit" class="px-4 mt-8 py-2 bg-darkred text-white rounded hover:bg-darkred transition w-full">
            <p class="text-2xl font-semibold"><%= t("simulation.start") %></p>
          </button>
        </form>

        <!-- show options with checkbox: Ventricles, CoronarySinus, Epicardium, LV Endocardium, RV Endocardium, Suggested Target Points  -->
        <div id="hamburgerMenu" class="absolute top-80 right-4 bg-darkgray p-2 rounded-xl w-[300px] hidden">
          <%- include('components/hamburger-options') %>
        </div>
      </div>
    </main>
    <script src="/js/hamburger-menu.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const hamburgerBtn = document.getElementById("hamburgerBtn");
        const hamburgerMenu = document.getElementById("hamburgerMenu");
        hamburgerBtn.addEventListener("click", (event) => {
          hamburgerMenu.classList.toggle("hidden");
        });
      });
    </script>
    <script>
       let viewer;
       let models = <%- resources %>;

       const loadingScreenDiv = document.getElementById("loadingScreen");
       const vtkContainer = document.getElementById("vtk-3d-canvas");

       let canvas_width = 1050;
       let canvas_height = 750;

       vtkContainer.width = canvas_width;
       vtkContainer.height = canvas_height;
       vtkContainer.style.width = canvas_width + "px";
       vtkContainer.style.height = canvas_height + "px";

       function hideLoading() {
        if (loadingScreenDiv) {
          loadingScreenDiv.classList.add("hidden");
        }
        if (vtkContainer.classList) {
          vtkContainer.classList.remove("hidden");
        }
       }

      window.addEventListener("ModuleLoaded", async () => {
        viewer = new VtkViewer("#vtk-3d-canvas", { models: models });
        await viewer.viewerInit();
        viewer.setSize(canvas_width, canvas_height);
        await viewer.refresh();
        window.viewer = viewer;
        hideLoading();
      });

      function onCellClickEvent(actor, x, y, z, ventricular) {
        console.log("onCellClickEvent received from C++: ", actor, x, y, z, ventricular);
        const rvTarget = document.getElementById("rvTarget");
        const lvTarget = document.getElementById("lvTarget");
        if (actor == "WHOLE_HEART") {
         if (rvTarget.checked) {
              // Logica per RV TARGET
              console.log("RV Target selected");
              viewer.setTargetPosition("RV_TARGET", x, y, z);
              document.getElementById("rVentricular").value = ventricular;
              document.getElementById("rvx").value = x;
              document.getElementById("rvy").value = y;
              document.getElementById("rvz").value = z;
          } else if (lvTarget.checked) {
              // Logica per LV TARGET
              console.log("LV Target selected");
              viewer.setTargetPosition("LV_TARGET", x, y, z);
              document.getElementById("lVentricular").value = ventricular;
              document.getElementById("lvx").value = x;
              document.getElementById("lvy").value = y;
              document.getElementById("lvz").value = z;
          }
        }
      }
    </script>
    <script src="/js/vtk-viewer.js" type="module"></script>
  </body>
</html>
<!--{
    "right lead": {
        "5": [
            "1.000;0.266;1.000;0.000;0",
            [
                28.223331451416,
                -178.46334838867,
                210.68743896484
            ]
        ]
    },
    "leads": {
        "1": [
            "0.770;2.890;1.000;3.000",
            [
                60.080642700195,
                -85.754417419434,
                169.98257446289
            ]
        ],
        "2": [
            "0.730;3.016;1.000;3.000",
            [
                58.859062194824,
                -86.523612976074,
                161.80352783203
            ]
        ],
        "3": [
            "0.610;-2.827;1.000;3.000",
            [
                57.836322784424,
                -97.798812866211,
                140.47540283203
            ]
        ],
        "5": [
            "0.930;-2.985;1.000;3.000",
            [
                28.770763397217,
                -94.761901855469,
                165.67330932617
            ]
        ]
    }
}-->