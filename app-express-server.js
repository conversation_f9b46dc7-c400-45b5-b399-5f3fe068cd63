"use strict";
import express from "express";
// import helmet from 'helmet';
// import cors from 'cors';
import compression from "compression";
import bodyParser from "body-parser";
import cookieParser from "cookie-parser";
import session from "express-session";
import path from "path";

import appRoutes from "./endpoints/index.js";

import http from "http";

//Questo perchè se type=module devo fare cosi:
import { fileURLToPath } from "url";
import { dirname } from "path";
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

//Fix risoluzione dns di localhost tramite node-fetch che su ipv6 fallisce.
import { setDefaultResultOrder } from "dns";
setDefaultResultOrder("ipv4first");

import dotenv from "dotenv";
dotenv.config();

//#region import da 'libreria' medcommon
import logger from "medcommon/logger.js";
import oracle from "medcommon/oracle";
import { medElkLogMiddleWare } from "medcommon/logger.js";
import oracleSessionStore from "medcommon/oracleSessionStore";
//const config = require("medcommon/config");
//const promiseMiddleware = require("medcommon/promise");
//#endregion

import { JobSchedulerSocket } from "./job-scheduler-socket.js";

const myPid = `pid.${process.pid}`;

const sessionOptions = {
  store: new (oracleSessionStore(session))({
    schemaName: "NICE",
    tableName: "COOKIE_SESSIONS",
    pruneSessionInterval: 60 * 10,
    elk: { transaction: { id: myPid + ".cookie" } },
  }),
  secret: process.env.COOKIE_SECRET,
  resave: true,
  saveUninitialized: false,
  cookie: {
    maxAge: (process.env.COOKIE_MAX_AGE || 24) * 3600000, //24 * 60 * 60 * 1000, //1 giorno
    sameSite: false, //Attenzione se metto a true il sso non va, ma porta altri problemi...
  },
};
const sessionMiddleware = session(sessionOptions);
//const helmetOptions = {frameguard: false, contentSecurityPolicy: false,};

const jobSchedulerSocket = new JobSchedulerSocket({ port: process.env.WEB_SOCKET_PORT || 8080 });

class AppExpressServer {
  thisServer = undefined;

  exprApp = express();

  ipAddress = process.env.IP_ADDRESS || "0.0.0.0";
  port = process.env.PORT || 3000;
  basePath = process.env.VIRTUALDIRPATH || "/";
  application = process.env.APPLICATION || "nice backend";

  constructor(options) {
    // eslint-disable-next-line no-unused-vars
    options = options || {};

    //#region TUTTI i Vari Middleware utilizzati da Express
    this.exprApp
      .use(compression())
      //.use(cors())
      //.use(helmet(helmetOptions))
      .head("/health-check", (req, res) => res.status(200).end())
      .get(
        "/test-oracle",
        //#swagger.tags = ['System Checks']
        //#swagger.summary = 'Verifica la stato della connessione Oracle'
        //#swagger.description = 'Endpoint per verificare la connessione Oracle.'s
        oracle.verifyDBConnection
      )
      .use(bodyParser.json())
      .use(bodyParser.urlencoded({ extended: false }))
      .use(express.static(path.join(__dirname, "web", "public"))) //Se metto le pagine public, prima di logger si evita il loro log...
      .use("/storage", express.static(path.join(__dirname, "storage")))
      .use(medElkLogMiddleWare) //Middleware di Logger per OGNI richiesta.
      //.set('trust proxy', () => config.proxyList || false)
      .use(cookieParser(process.env.COOKIE_SECRET)) //Metto cookie parser DOPO cartella public x evitare parse dei cookie su pagine statiche
      .use(sessionMiddleware)
      .set("view engine", "ejs")
      // eslint-disable-next-line no-undef
      .set("views", path.join(__dirname, "web", "views"))
      //.use(promiseMiddleware)
      .use((error, req, res, next) => {
        logger.error("ERROR ON %s %s %O", req.method, req.path, error, { error, transaction: req.transaction });
        next(error);
      })
      //All app routes are now in XYZ_routes.js files.
      .use(appRoutes);
    //#endregion
    //Riattivare quando verrà utilizzata la medcommon...
    /*this.exprApp.use((error, req, res, next) => {
      logger.error("ERROR ON %s %s %O", req.method, req.path, error, { error, transaction: req.transaction });
      next();
    });*/

    this.thisServer = http.createServer(this.exprApp);
  }

  start = async function () {
    this.thisServer.listen(this.port, this.ipAddress, () =>
      logger.info(`⚡ ${this.application} Server [ RUNNING ] on http://${this.ipAddress}:${this.port}${this.basePath}`, {
        transaction: { id: `pid.${process.pid}` },
      })
    );
  };

  stop = async function () {
    this.thisServer.close();
    logger.info(`☠️  ${this.application} Server [ STOPPED ] on http://${this.ipAddress}:${this.port}${this.basePath}`);
  };
}
export default AppExpressServer;
