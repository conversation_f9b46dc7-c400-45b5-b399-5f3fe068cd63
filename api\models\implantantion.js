import { AbstractDbCrudModel } from "cruder/abstractCrud";
import <PERSON><PERSON> from "joi";

class ImplantationModel extends AbstractDbCrudModel {
  static schema = {
    schemaName: "NICE",
    tableName: "IMPLANTATIONS",
    keys: ["ID"],
    fields: ["DESCRIPTION", "ID_MAP", "ID_MAP_RESULT", "ID_PATIENT", "STATUS", "CREATED_AT", "DATA"],
    mapping: {
      ID: "id",
      DESCRIPTION: "description",
      ID_MAP: "idMap",
      ID_MAP_RESULT: "idMapResult",
      ID_PATIENT: "idPatient",
      STATUS: "status",
      CREATED_AT: "createdAt",
      DATA: 'data',
    },
    joi: Joi.object({}),
  };
  constructor(data = {}, dbType = "oracle") {
    super(data, dbType);
  }
}
export default ImplantationModel;
