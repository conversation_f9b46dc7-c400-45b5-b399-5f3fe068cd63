CREATE SEQUENCE  "NICE"."S_IMPLANTATIONS_ID"  MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 NOCACHE  NOORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL ;

CREATE TABLE "NICE"."IMPLANTATIONS"  (	"ID" NUMBER, 
	"DESCRIPTION" VARCHAR2(100 BYTE), 
	"ID_MAP" NUMBER, 
	"ID_MAP_RESULT" NUMBER, 
	"STATUS" NUMBER DEFAULT -1, 
	"DATA" VARCHAR2(1000 BYTE), 
	"CREATED_AT" DATE DEFAULT sysdate, 
	"ID_PATIENT" NUMBER
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

SET DEFINE OFF;
--Insert into NICE.IMPLANTATIONS (ID,DESCRIPTION,ID_MAP,ID_MAP_RESULT,STATUS,DATA,CREATED_AT,ID_PATIENT) values ('1','test123','1','5','-1','{}',to_date('28-AGO-25','DD-MON-RR'),'1');
ALTER TABLE "NICE"."IMPLANTATIONS" MODIFY ("ID" NOT NULL ENABLE);
ALTER TABLE "NICE"."IMPLANTATIONS" MODIFY ("DESCRIPTION" NOT NULL ENABLE);
ALTER TABLE "NICE"."IMPLANTATIONS" MODIFY ("ID_MAP" NOT NULL ENABLE);
ALTER TABLE "NICE"."IMPLANTATIONS" MODIFY ("ID_MAP_RESULT" NOT NULL ENABLE);
ALTER TABLE "NICE"."IMPLANTATIONS" MODIFY ("STATUS" NOT NULL ENABLE);
ALTER TABLE "NICE"."IMPLANTATIONS" MODIFY ("CREATED_AT" NOT NULL ENABLE);
ALTER TABLE "NICE"."IMPLANTATIONS" MODIFY ("ID_PATIENT" NOT NULL ENABLE);
