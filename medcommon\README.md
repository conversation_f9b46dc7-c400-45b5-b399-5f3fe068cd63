<!-- Improved compatibility of back to top link: See: https://github.com/othneildrew/Best-README-Template/pull/73 -->
<a name="readme-top"></a>
<!-- PROJECT LOGO -->
<div align="center">
<h2 align="center">medcommon (JavaScript)</h2>
<p align="center">Libreria node.js di classi comuni, per incoraggiare l'abbandono della pratica del [Copy&Paste](https://en.wikipedia.org/wiki/Copy-and-paste_programming) nei progetti node.js 
    <br />
    <br />
    <a href="https://git.medarchiver.com/developers/medcommon/-/wikis/home"><strong>Leggi la documentazione »</strong></a>
    <br />
    <br />
<!-- <a href="http://*********:3008/">Demo Web</a> · -->
    <a href="https://git.medarchiver.com/developers/medcommon/issues"><PERSON><PERSON><PERSON></a>
    ·
    <a href="https://git.medarchiver.com/developers/medcommon/issues">Richiedi Funzionalità</a>
  </p>
</div>
<!-- TABLE OF CONTENTS -->

  # Sommario

  <ol>
    <li>
      <a href="#about-the-project">Informazioni sul progetto</a>
      <ul>
        <li><a href="#built-with">Tecnologie</a></li>
      </ul>
    </li>
    <li><a href="#getting-started">Integrazione libreria nei propri progetti</a>
    </li>
    <li><a href="#verify-install">Verifica Installazione</a>
    <li><a href="#example-repos">Repository di riferimento</a>
  </ol>


<!-- ABOUT THE PROJECT --><a name="about-the-project"></a>
# Informazioni sul progetto  

Il repository nasce dalla necessità di mettere ordine ed unificare, nel limite del possibile, i vari progetti node.js esistenti.

Infatti in passato, ad ogni nuovo progetto si clonava ([Copy&Paste](https://en.wikipedia.org/wiki/Copy-and-paste_programming)) il codice di un altro progetto esistente, con il risultato di avere copie degli stessi files riportati in più progetti, situazione che genera una manutenzione difficoltosa.

Il progetto by-design è stato strutturato come modulo npm/yarn, cosi da eliminare la necessità di dover referenziare la libreria con i percorsi assoluti (es. '../../../medcommon') che rende il codice poco gestibile.  

<!-- Here's a blank template to get started: To avoid retyping too much info. Do a search and replace with your text editor for the following: -->

<p align="right">(<a href="#readme-top">torna in cima</a>)</p>

<!-- TECHNOLOGIES --><a name="built-with"></a>

## Tecnologie

* ![NodeJS](https://img.shields.io/badge/node.js-6DA55F?style=for-the-badge&logo=node.js&logoColor=white) ![Express.js](https://img.shields.io/badge/express.js-%23404d59.svg?style=for-the-badge&logo=express&logoColor=%2361DAFB) ![JavaScript](https://img.shields.io/badge/javascript-%23323330.svg?style=for-the-badge&logo=javascript&logoColor=%23F7DF1E) ![Oracle](https://img.shields.io/badge/Oracle-F80000?style=for-the-badge&logo=oracle&logoColor=white)


<p align="right">(<a href="#readme-top">torna in cima</a>)</p>

<!-- GETTING STARTED --><a name="getting-started"></a>
# Integrazione libreria nei propri progetti

Per utilizzare questa libreria nel proprio progetto, si raccomanda di integrarlo come un [submodule git](https://git-scm.com/book/en/v2/Git-Tools-Submodules), cosi da mantenere un legame del repository sul proprio progetto.
Questo porta il vantaggio che nel proprio progetto verrà mantenuto un riferimento al commit esatto della versione utilizzata, che **NON** sarà obbligatoriamente sempre all'ultima versione della libreria.

Il submodule va aggiunto, solo _dopo_ aver creato il proprio repository su git ed aggiunto il remote origin.  
Per aggiungerlo, lanciare dalla riga commandi della cartella principale del progetto:

```bash
git submodule add ../../developers/medcommon.git
```

In questo modo verrà creato un file **.gitmodules** e verrà anche fatto il clone del repository (all'ultima versione) nella cartella **/medcommon**.
```js
// Il file .gitmodules in fase di commit/push 
// dovrà essere inserito tra i files del proprio repository
```

>**ATTENZIONE**:  
Il comando `git submodule add` con path relativo  
(../../developers/medcommon.git) 
serve perché il clone del repository si possa fare sia via HTTP sia via SSH. Se usassimo il path assoluto si dovrebbe per forza specificare uno o l'altro e questo renderebbe impossibile fare i deploy automatici che usano chiavi SSH. Bisogna però, in base al progetto git a cui si aggiungono i submodules, calcolare il path relativo in modo corretto.

A questo punto per inserirlo come dependencies (e poterlo cosi utilizzare) all'interno del proprio progetto, lanciare il commando:

```bash
npm i file:./medcommon --save
```

In questo modo viene aggiunta la dipendenza al file **package.json** come se fosse un qualsiasi altro package installato via npm.

```json
"dependencies": {
    "medcommon": "file:medcommon"
}
```

> In questa fase npm installa nella cartella node_modules anche tutte le dipendenze definite nel package.json presente nella cartella /medcommon.

Per verificare il funzionamento, consultare <a href="#verify-install">verifica funzionamento</a>.

<p align="right">(<a href="#readme-top">torna in cima</a>)</p>

<a name="verify-install"></a>

### Verifica funzionamento

Se si vuole effettuare una rapida verifica del corretto funzionamento, creare un file js a piacere e fare [Copy&Paste](https://en.wikipedia.org/wiki/Copy-and-paste_programming) delle seguenti righe:

```js
const config = require("medcommon/config");
const logger = require("medcommon/logger");

var configHasErrors = config.checkEnvVariables(false);
```
Assicurarsi che sia presente un file .env nel progetto e avviare l'app node. (es. index.js)

```bash
node index.js 
```
Con l'esempio sopra riportato, vengono generati dei messaggi di check del valore delle variabili contenute nel file .env. Tali messaggi vengono visualizzati sia sulla riga commando sia salvati in un file log.

<p align="right">(<a href="#readme-top">torna in cima</a>)</p>

<a name="example-repos"></a>

## Repositories che utilizzano la libreria

- Progetto [Gestione Utenti](https://git.medarchiver.com/developers/usermanager_nodejs.git) MEDarchiver.
- Progetto d'integrazione MEDarchiver con Sinteco Athena, [Backend](https://git.medarchiver.com/developers/medathena_nodejs.git).

<p align="right">(<a href="#readme-top">torna in cima</a>)</p>


<!-- CONTRIBUTING 
## Contributing

Contributions are what make the open source community such an amazing place to learn, inspire, and create. Any contributions you make are **greatly appreciated**.

If you have a suggestion that would make this better, please fork the repo and create a pull request. You can also simply open an issue with the tag "enhancement".
Don't forget to give the project a star! Thanks again!

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

<p align="right">(<a href="#readme-top">back to top</a>)</p>
-->

<!-- LICENSE 
## License

Distributed under the MIT License. See `LICENSE.txt` for more information.

<p align="right">(<a href="#readme-top">back to top</a>)</p>
-->
<!-- CONTACT 
## Contact

Your Name - [@twitter_handle](https://twitter.com/twitter_handle) - email@email_client.com

Project Link: [https://git.medarchiver.com/flutter/repo_name](https://github.com/github_username/repo_name)

<p align="right">(<a href="#readme-top">back to top</a>)</p>
-->

<!-- ACKNOWLEDGMENTS 
## Acknowledgments

* []()
* []()
* []()

<p align="right">(<a href="#readme-top">back to top</a>)</p>
-->

<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->
[Flutter.dev]: https://img.shields.io/badge/Flutter-%2302569B.svg?style=for-the-badge&logo=Flutter&logoColor=white
[contributors-shield]: https://img.shields.io/github/contributors/github_username/repo_name.svg?style=for-the-badge
[contributors-url]: https://git.medarchiver.com/flutter/repo_name/graphs/contributors
[forks-shield]: https://img.shields.io/github/forks/flutter/repo_name.svg?style=for-the-badge
[forks-url]: https://github.com/flutter/repo_name/network/members
[stars-shield]: https://img.shields.io/github/stars/github_username/repo_name.svg?style=for-the-badge
[stars-url]: https://github.com/flutter/repo_name/stargazers
[issues-shield]: https://img.shields.io/github/issues/github_username/repo_name.svg?style=for-the-badge
[issues-url]: https://github.com/flutter/repo_name/issues
[license-shield]: https://img.shields.io/github/license/github_username/repo_name.svg?style=for-the-badge
[license-url]: https://github.com/flutter/repo_name/blob/master/LICENSE.txt
[linkedin-shield]: https://img.shields.io/badge/-LinkedIn-black.svg?style=for-the-badge&logo=linkedin&colorB=555
[linkedin-url]: https://linkedin.com/in/linkedin_username
[product-screenshot]: images/screenshot.png
[Next.js]: https://img.shields.io/badge/next.js-000000?style=for-the-badge&logo=nextdotjs&logoColor=white
[Next-url]: https://nextjs.org/
[React.js]: https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB
[React-url]: https://reactjs.org/
[Vue.js]: https://img.shields.io/badge/Vue.js-35495E?style=for-the-badge&logo=vuedotjs&logoColor=4FC08D
[Vue-url]: https://vuejs.org/
[Angular.io]: https://img.shields.io/badge/Angular-DD0031?style=for-the-badge&logo=angular&logoColor=white
[Angular-url]: https://angular.io/
[Svelte.dev]: https://img.shields.io/badge/Svelte-4A4A55?style=for-the-badge&logo=svelte&logoColor=FF3E00
[Svelte-url]: https://svelte.dev/
[Laravel.com]: https://img.shields.io/badge/Laravel-FF2D20?style=for-the-badge&logo=laravel&logoColor=white
[Laravel-url]: https://laravel.com
[Bootstrap.com]: https://img.shields.io/badge/Bootstrap-563D7C?style=for-the-badge&logo=bootstrap&logoColor=white
[Bootstrap-url]: https://getbootstrap.com
[JQuery.com]: https://img.shields.io/badge/jQuery-0769AD?style=for-the-badge&logo=jquery&logoColor=white
[JQuery-url]: https://jquery.com
