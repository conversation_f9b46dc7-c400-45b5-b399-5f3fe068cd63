<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("patientList.heading") %>
    <% icon = "fa-solid fa-users" %>
    <%- include('components/head') %>
    <style>
      mark {
        background-color: rgb(170, 132, 62);
        color: black;
        padding: 0 2px;
        border-radius: 2px;
      }
      .my-scroll::-webkit-scrollbar {
        width: 24px;
      }
      .my-scroll::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 3px;
      }
      .my-scroll::-webkit-scrollbar-track {
        background-color: #eee;
      }
      .selected-row {
        background-color: #3F899C !important; 
      }
      .patient-row {
        cursor: pointer;
      }
      .sortable-header {
        cursor: pointer;
        user-select: none;
        position: relative;
      }
      .sortable-header:hover {
        background-color: #374151;
      }
      .sort-icon {
        margin-left: 8px;
        opacity: 0.5;
      }
      .sort-icon.active {
        opacity: 1;
      }
    </style>
  </head>
  <body class="h-screen overflow-hidden">
    <%- include('components/header') %>
    <main class="h-full flex flex-col">
      <div class="pt-4 flex-shrink-0">
        <div class="flex items-center gap-3 mb-4">
          <div><i class="fa-solid fa-magnifying-glass text-3xl ml-2"></i></div>
          <input
            id="searchInput"
            name="searchInput"
            type="text"
            placeholder="<%= t('search') %>..."
            class="bg-[#3a3a3a] text-white placeholder-gray-500 focus:outline-none flex-1 text-xl tracking-wide px-4 py-2 rounded-md uppercase"
            oninput="this.value = this.value.toUpperCase()"
            value="<%=lastSearchTerm%>"
          />
          <select id="statusFilter" tabindex="0" class="bg-[#3a3a3a] text-white border-none focus:outline-none text-xl px-2 py-2 rounded-md focus:ring-2 focus:ring-white">
            <option value="" disabled selected hidden><%= t('badge.filterStatus') %></option>
            <option value="all"><%= t('badge.all') %></option>
            <option value="before"><%= t('badge.before') %></option>
            <option value="after"><%= t('badge.after') %></option>
            <option value="follow_up"><%= t('badge.follow_up') %></option>
          </select>
          <a
            href="/patients/new"
            class="text-white p-2 hover:text-green-600 transition"
            aria-label="<%=t('addNewPatient')%>"
            title="<%=t('addNewPatient')%>">
            <i class="fa-solid fa-user-plus text-3xl ml-2"></i>
          </a>
        </div>
      </div>
      <div class="flex-1 p-4 pt-0  overflow-hidden">
        <div id="resultTable" class="h-full bg-[#2c2c2c] rounded-lg shadow-lg overflow-y-auto relative">
          <table class="min-w-full table-fixed">
            <thead class="sticky top-0 z-10 uppercase p-4 bg-darkred">
              <tr>
                <th class="p-4 text-left sortable-header" data-sort="lastName">
                  <%= tup("lastName") %>
                  <i class="fa-solid fa-sort sort-icon"></i>
                </th>
                <th class="p-4 text-left sortable-header" data-sort="firstName">
                  <%= tup("firstName") %>
                  <i class="fa-solid fa-sort sort-icon"></i>
                </th>
                <th class="p-4 text-center" data-sort="birthDate">
                  <%= t("birthDate") %>
                </th>
                <th class="p-4 text-center" data-sort="age">
                  <%= t("age") %>
                </th>
                <th class="p-4 text-center" data-sort="gender">
                  <%= t("gender") %>
                </th>
                <th class="p-4 text-center sortable-header" data-sort="statusTag">
                  <%= t("statusTag") %>
                  <i class="fa-solid fa-sort sort-icon"></i>
                </th>
              </tr>
            </thead>
            <tbody id="patientListBody" class="text-white text-lg">
            </tbody>
          </table>
          <div id="overlay" class="absolute inset-0 bg-gray-800 bg-opacity-70 flex flex-col items-center justify-center z-10">
            <div class="w-24 h-24 border-4 border-gray-300 border-t-darkred rounded-full animate-spin"></div>
            <p id="loadingText" class="text-white text-lg font-sans mt-4"><%= t('patientList.searchInProgress') %></p>
          </div>
        </div>
      </div>
    </main>
    <template id="row-template">
      <tr class="border-b border-gray-300 patient-row">
        <td class="p-4 text-left lastName"></td>
        <td class="p-4 text-left firstName"></td>
        <td class="p-4 text-center birthdate"></td>
        <td class="p-4 text-center age"></td>
        <td class="p-4 text-center gender"></td>
        <td class="p-4 text-center statusTag"></td>
      </tr>
    </template>

    <%- include('components/keybinds') %>


    
    <script>
      let currentPatientId = null;
      let allPatients = []; // Store all search results for filtering
      let currentSort = { field: null, direction: 'asc' };

      const input = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      const tbody = document.getElementById('patientListBody');
      const template = document.getElementById('row-template').content;

      const resultTable = document.getElementById("resultTable");
      const overlay = document.getElementById('overlay');

      // Add sort event listeners
      document.querySelectorAll('.sortable-header').forEach(header => {
        header.addEventListener('click', () => {
          const sortField = header.dataset.sort;
          sortTable(sortField);
        });
      });

      input.addEventListener('input',
        debounce(async function (e) {
          const query = e.target.value.trim();
          await performSearchQuery(query);
        }, 500)
      );

      statusFilter.addEventListener('change', function() {
        filterAndDisplayResults();
      });

      function sortTable(field) {
        // Toggle direction if same field, otherwise default to asc
        if (currentSort.field === field) {
          currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
          currentSort.field = field;
          currentSort.direction = 'asc';
        }

        // Update sort icons
        document.querySelectorAll('.sort-icon').forEach(icon => {
          icon.className = 'fa-solid fa-sort sort-icon';
        });

        const activeHeader = document.querySelector(`[data-sort="${field}"] .sort-icon`);
        activeHeader.className = `fa-solid fa-sort-${currentSort.direction === 'asc' ? 'up' : 'down'} sort-icon active`;

        // Sort and redisplay
        filterAndDisplayResults();
      }

      function getSortValue(patient, field) {
        switch (field) {
          case 'lastName':
            return patient.lastName.toLowerCase();
          case 'firstName':
            return patient.firstName.toLowerCase();
          case 'birthDate':
            return new Date(patient.birthDate);
          case 'age':
            return Math.floor((new Date() - new Date(patient.birthDate)) / 365 / 24 / 60 / 60 / 1000);
          case 'gender':
            return patient.gender.toLowerCase();
          case 'statusTag':
            return patient.statusTag || '';
          default:
            return '';
        }
      }

      function handleRowClick(row, patientId) {
        let selectedRow = document.querySelector('.selected-row');
        if (selectedRow === row) {
          window.location.href = `/patients/${patientId}`;
          return;
        }
        if (selectedRow) {
          selectedRow.classList.remove('selected-row');
        }
        row.classList.add('selected-row');
      }

      async function performSearchQuery(query) {
        tbody.innerHTML = '';
        console.log('performSearchQuery', query);
        document.querySelector('.selected-row')?.classList.remove('selected-row');
        try {
          overlay.style.display = 'flex';
          const response = await fetch(`/patients/search?term=${encodeURIComponent(query)}`, { method: 'POST' });
          if (!response.ok) throw new Error('Search failed');
          const data = await response.json();
          allPatients = data; // Store all results
          filterAndDisplayResults();
        } catch (error) {
          console.error('Search error:', error);
          tbody.innerHTML = `<tr><td colspan="6" class="text-center py-4 text-error"><%= t('searchError') %></td></tr>`;
        }
        finally {
          overlay.style.display = 'none';
        }
      }

      function filterAndDisplayResults() {
        tbody.innerHTML = '';
        document.querySelector('.selected-row')?.classList.remove('selected-row');
        
        const statusFilterValue = statusFilter.value;
        let filteredPatients = allPatients;
        if (statusFilterValue == 'before' || statusFilterValue == 'after' || statusFilterValue == 'follow_up') {
          filteredPatients = allPatients.filter(patient => patient.statusTag === statusFilterValue);
        }

        // Sort the filtered patients
        if (currentSort.field) {
          filteredPatients.sort((a, b) => {
            const aVal = getSortValue(a, currentSort.field);
            const bVal = getSortValue(b, currentSort.field);
            
            let comparison = 0;
            if (aVal > bVal) comparison = 1;
            if (aVal < bVal) comparison = -1;
            
            return currentSort.direction === 'asc' ? comparison : -comparison;
          });
        }

        if (filteredPatients.length === 0) {
          const tr = document.createElement('tr');
          tr.innerHTML = `<td colspan="6" class="text-center py-4 text-gray-400"><%= t('noResultsFound') %></td>`;
          tbody.appendChild(tr);
        } else {
          filteredPatients.forEach((row) => addNewRow(row));
        }
      }

      const badgeTemplates = {
        before: `<%- include('./components/badge-text', {badgeColor: 'bg-green-500', badgeText: 'badge.before'}) %>`,
        after: `<%- include('./components/badge-text', {badgeColor: 'bg-yellow-500', badgeText: 'badge.after'}) %>`,
        followUp: `<%- include('./components/badge-text', {badgeColor: 'bg-red-500', badgeText: 'badge.follow_up'}) %>`
      };

      function addNewRow(dataRow){
        const tableBody = document.getElementById('patientListBody');
        const tr = template.cloneNode(true).querySelector('tr');
        const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();
        tr.dataset.id = dataRow.id;
        tr.querySelector('.firstName').innerHTML = highlightMatch(dataRow.firstName.toUpperCase(), searchTerm);
        tr.querySelector('.lastName').innerHTML = highlightMatch(dataRow.lastName.toUpperCase(), searchTerm);
        const data = new Date(dataRow.birthDate);
        tr.querySelector('.birthdate').textContent = data.toLocaleDateString('it-IT');
        tr.querySelector('.age').textContent = Math.floor((new Date() - data) / 365 / 24 / 60 / 60 / 1000);
        tr.querySelector('.gender').textContent = dataRow.gender;
        
        switch (dataRow.statusTag) {
          case 'before': tr.querySelector('.statusTag').innerHTML = badgeTemplates.before; break;
          case 'after': tr.querySelector('.statusTag').innerHTML = badgeTemplates.after; break;
          case 'follow_up': tr.querySelector('.statusTag').innerHTML = badgeTemplates.followUp; break;
          default: tr.querySelector('.statusTag').innerHTML = 'Unknown';
        }
        
        tr.addEventListener('click', () => handleRowClick(tr, dataRow.id));
        tableBody.appendChild(tr);
      }

      document.addEventListener('DOMContentLoaded', function () {
        performSearchQuery(input.value.trim());

        
        // if arrow up or down, select next or previous row, mind lastClikedRow
        document.addEventListener('keydown', (event) => {
          const selectedRow = document.querySelector('.selected-row');
          if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
            if (!selectedRow) {
              const firstRow = tbody.querySelector('.patient-row');
              handleRowClick(firstRow, firstRow.dataset.id);
              return;
            }
            const rows = tbody.querySelectorAll('.patient-row');
            let index = Array.from(rows).indexOf(selectedRow);
            if (index <= 0 && event.key === 'ArrowUp') return;
            if (index >= rows.length - 1 && event.key === 'ArrowDown') return;
            if (event.key === 'ArrowUp') {
              index = index > 0 ? index - 1 : 0;
            } else {
              index = index < rows.length - 1 ? index + 1 : rows.length - 1;
            }
            handleRowClick(rows[index], rows[index].dataset.id);
            // move scroll to selected row
            rows[index].scrollIntoView({ behavior: 'instant', block: 'center' });
            // console.log('arrow', selectedRow);
          }
          if (event.key === 'Enter') {
            event.preventDefault();
            // console.log(selectedRow?.dataset.id, 'enter', selectedRow);
            if (selectedRow) {
              handleRowClick(selectedRow, selectedRow.dataset.id);
            }
          }
        });
      });

      function debounce(fn, delay) {
        let timer = null;
        return (...args) => {
          clearTimeout(timer);
          timer = setTimeout(() => fn.apply(this, args), delay);
        };
      }

      function highlightMatch(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
      }
    </script>
  </body>
</html>
