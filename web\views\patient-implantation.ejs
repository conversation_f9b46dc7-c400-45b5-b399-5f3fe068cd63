<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">

<head>
    <%- include('components/head') %>
</head>

<body>
    <% title=t("patient.heading") %>
        <% icon="fa-solid fa-user" %> <%- include('components/head') %>
                <%- include('components/header') %>
                    <%- include('components/patient-btns-editdelete') %>
                        <%- include('components/patient-tabs') %>
                            <main>
                                <!-- dropdown menu, centered 50% width -->
                                <div class="w-1/2 mx-auto p-4 rounded-lg shadow-lg">
                                    <select id="assessmentSelect" class="w-full h-10 rounded-lg">
                                        <option value="">Select Assessment</option>
                                        <% initialAssessments.forEach(function(map) { %>
                                            <option value="<%= map.id %>">
                                                <%= map.description %>
                                            </option>
                                            <% }); %>
                                    </select>
                                </div>
                                <canvas id="vtk-3d-canvas" class="hidden "></canvas>
                                <!-- loading screen -->
                                 <div id="loadingScreen" class="flex bg-darkgray flex-col justify-center items-center" style="height: 900px">
                                    <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
                                    <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
                                </div>
                                <div class="flex flex-row justify-between w-full rounded-lg bg-darkgray p-4 text-white">
                                    <!-- LV Lead (Static) -->
                                    <div class="flex flex-col gap-2 mb-8">
                                        <input type="hidden" id="rVentricular" name="rVentricular" value="0" />
                                        <div class="flex items-center gap-3">
                                            <input type="checkbox" id="lvTarget" name="lead" data-id="LV_TARGET"
                                                class="w-12 h-12 accent-darkred lead-toggle" />
                                            <label for="lvTarget" class="text-2xl font-semibold">
                                                <%= t("simulation.lvLead") %>
                                            </label>
                                        </div>
                                        <div class="flex flex-wrap items-center gap-4 pt-2">
                                            <% ["x", "y" , "z" ].forEach(axis=> { %>
                                                <div
                                                    class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                                                    <label for="lv<%= axis %>" class="text-xl">
                                                        <%= axis.toUpperCase() %>
                                                    </label>
                                                    <input type="text" id="lv<%= axis %>" name="lv<%= axis %>"
                                                        class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                                                </div>
                                                <% }); %>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-2">
                                        <!-- Add/Remove Buttons -->
                                        <div class="flex gap-4 mt-4">
                                            <button type="button" onclick="addRVLead()"
                                                class="bg-green-600 px-4 py-2 rounded text-white">
                                                <i class="fa-solid fa-plus"></i>
                                                Add RV Lead
                                            </button>
                                            <button type="button" onclick="removeRVLead()"
                                                class="bg-red-600 px-4 py-2 rounded text-white">
                                                <i class="fa-solid fa-minus"></i>
                                                Remove RV Lead
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </main>

                            <script src="/js/vtk-viewer.js" type="module"></script>
                            <script>
                                const loadingScreenDiv = document.getElementById("loadingScreen");
                                const vtkContainer = document.getElementById("vtk-3d-canvas");

                                function onCellClickEvent(a, b, c, d, e) { 
                                    
                                }

                                window.addEventListener("ModuleLoaded", async () => {
                                    viewer = new VtkViewer("#vtk-3d-canvas", { models: <%- resources %>  });
                                    await viewer.viewerInit();
                                    viewer.setSize(window.innerWidth, 900);
                                    await viewer.refresh();
                                    if (loadingScreenDiv) {
                                        loadingScreenDiv.classList.add("hidden");
                                    }
                                    if (vtkContainer.classList) {
                                        vtkContainer.classList.remove("hidden");
                                    }
                                });
                            </script>
                            <script>
                                function addRVLead() {
                                    const container = document.getElementById("rvLeadsContainer");
                                    const leadCount = container.
                                    if (leadCount >= 4) {
                                        alert("Maximum of 4 RV leads allowed.");
                                        return;
                                    }
                                    const leadId = `RV_LEAD_${leadCount + 1}`;
                                    const leadDiv = document.createElement("div");
                                    leadDiv.className = "flex flex-col gap-2 mb-8";
                                    leadDiv.innerHTML = `
                                        <div class="flex items-center gap-3">
                                            <input type="checkbox" id="${leadId}" name="lead" data-id="${leadId}"
                                                class="w-12 h-12 accent-darkred lead-toggle" />
                                            <label for="${leadId}" class="text-2xl font-semibold">
                                                RV Lead ${leadCount + 1}
                                            </label>
                                        </div>
                                        <div class="flex flex-wrap items-center gap-4 pt-2">
                                            ${["x", "y", "z"].map(axis => `
                                                <div
                                                    class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                                                    <label for="${leadId}${axis.toUpperCase()}" class="text-xl">
                                                        ${axis.toUpperCase()}
                                                    </label>
                                                    <input type="text" id="${leadId}${axis.toUpperCase()}" name
                                                        class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                                                </div>
                                            `).join('')}
                                        </div>
                                    `;
                                    container.appendChild(leadDiv);
                                    document.getElementById("rVentricular").value = leadCount + 1;
                                }
                            </script>
</body>

</html>