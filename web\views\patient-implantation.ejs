<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">

<head>
    <%- include('components/head') %>
</head>

<body>
    <% title=t("patient.heading") %>
        <% icon="fa-solid fa-user" %> <%- include('components/head') %>
                <%- include('components/header') %>
                    <%- include('components/patient-btns-editdelete') %>
                        <%- include('components/patient-tabs') %>
                            <main>
                                <!-- dropdown menu, centered 50% width -->
<div class="w-3/4 max-w-2xl mx-auto p-6 rounded-lg shadow-lg">
    <select id="assessmentSelect"
        class="w-full h-14 rounded-lg text-2xl text-white px-4 py-2 bg-gray-700 focus:outline-none focus:ring-2">
        
        <!-- Placeholder option -->
        <option disabled <%= !implantation.idMapResult ? 'selected' : '' %>>
            <%= t("Select Initial Assessment") %>
        </option>

        <!-- Dynamic options -->
        <% initialAssessments.forEach(function(map) { %>
            <option value="<%= map.id %>" <%= map.id === implantation.idMapResult ? 'selected' : '' %>>
                <%= map.description %>
            </option>
        <% }); %>
    </select>
</div>

                                <canvas id="vtk-3d-canvas" class="hidden"></canvas>
                                <!-- loading screen -->
                                 <div id="loadingScreen" class="flex bg-darkgray flex-col justify-center items-center" style="height: 900px">
                                    <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
                                    <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
                                </div>

                                        
                                <div class="flex flex-row justify-between w-full rounded-lg p-4 text-white">
                                    <div id="lvLeadsContainer" class="flex flex-col gap-4 w-1/2">
                                        <!-- Dynamic RV leads will be added here -->
                                    </div>
                                    <div class="flex flex-col gap-2 mb-8">
                                        <input type="hidden" id="rVentricular" name="rVentricular" value="0" />
                                        <div class="flex items-center gap-3">
                                            <input type="checkbox" id="rvTarget" name="lead" data-id="RV_TARGET"
                                                class="w-12 h-12 accent-darkred lead-toggle" />
                                            <label for="rvTarget" class="text-2xl font-semibold">
                                                <%= t("simulation.rvLead") %>
                                            </label>
                                        </div>
                                        <div class="flex flex-wrap items-center gap-4 pt-2">
                                            <% ["x", "y" , "z" ].forEach(axis=> { %>
                                                <div
                                                    class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
                                                    <label for="rvTarget<%= axis %>" class="text-xl">
                                                        <%= axis.toUpperCase() %>
                                                    </label>
                                                    <input type="text" id="rvTarget<%= axis %>" name="rv<%= axis %>"
                                                        class="bg-gray-700 text-white w-32 text-center text-xl p-2 mb-4 rounded" />
                                                </div>
                                                <% }); %>
                                        </div>
                                                                        
                                        <!-- Add/Remove Buttons -->
                                        <div class="flex justify-center gap-4 mt-16">
                                            <button type="button" onclick="addRVLead()"
                                                class="bg-green-600 p-4 mr-16 rounded text-white text-xl">
                                                <i class="fa-solid fa-plus"></i>
                                                Add LV Lead
                                            </button>
                                            <!-- <button type="button" onclick="removeRVLead()"
                                                class="bg-red-600 px-4 py-2 rounded text-white">
                                                <i class="fa-solid fa-minus"></i>
                                                Remove RV Lead
                                            </button> -->
                                        </div>
                                    </div>
                                </div>
                            </main>

                            <script src="/js/vtk-viewer.js" type="module"></script>
                            <script>
                                const loadingScreenDiv = document.getElementById("loadingScreen");
                                const vtkContainer = document.getElementById("vtk-3d-canvas");
                                let idx = 0;
                                function onCellClickEvent(actor, x, y, z, ventricular) { 
                                    console.log("onCellClickEvent received from C++: ", actor, x, y, z, ventricular);
                                    
                                    if (actor == "WHOLE_HEART") {
                                        const checkedLead = document.querySelector('.lead-toggle:checked');
                                        if (checkedLead) {
                                            const leadId = checkedLead.id;
                                            const idNumber = leadId.split('_').pop(); // Get the number part
                                            console.log("Setting position for lead:", leadId);
                                            document.getElementById(leadId + "x").value = x;
                                            document.getElementById(leadId + "y").value = y;
                                            document.getElementById(leadId + "z").value = z;
                                            if (leadId === "rvTarget") {
                                                viewer.setTargetPosition("RV_TARGET", x, y, z);
                                            } else {
                                                viewer.setTargetPosition("LV_TARGET_" + idNumber, x, y, z);
                                            }
                                        }
                                    }
                                }

                                window.addEventListener("ModuleLoaded", async () => {
                                    viewer = new VtkViewer("#vtk-3d-canvas", { models: <%- resources %>  });
                                    await viewer.viewerInit();
                                    viewer.setSize(window.innerWidth, 900);
                                    await viewer.refresh();
                                    if (loadingScreenDiv) {
                                        loadingScreenDiv.classList.add("hidden");
                                    }
                                    if (vtkContainer.classList) {
                                        vtkContainer.classList.remove("hidden");
                                    }
                                });
                            </script>
                            <script>
                                function addRVLead() {
                                    const container = document.getElementById("lvLeadsContainer");
                                    const leadCount = container.childElementCount;
                                    if (leadCount >= 4) {
                                        alert("Maximum of 4 RV leads allowed.");
                                        return;
                                    }
                                    const leadId = `LV_LEAD_${leadCount + 1}`;
                                    const leadDiv = document.createElement("div");
                                    leadDiv.className = "flex flex-col gap-2 mb-8";
                                    leadDiv.setAttribute("data-lead-id", leadId);
                                    leadDiv.innerHTML = `
<div class="flex items-center justify-around gap-3 bg-transparent text-white py-1 px-2">


    <!-- Checkbox and Label -->
    <input type="checkbox" id="${leadId}" name="lead" data-id="${leadId}"
        class="w-10 h-10 accent-darkred lead-toggle" />
    <label for="${leadId}" class="text-3xl font-bold mr-2">
        LV Lead
    </label>

    <!-- Coordinate Inputs -->
    ${["x", "y", "z"].map(axis => `
        <input type="text" id="${leadId}${axis}" name="${leadId}${axis.toUpperCase()}"
            placeholder="${axis.toUpperCase()}"
            class="bg-gray-700 text-white w-20 text-center text-base p-1 rounded" />
    `).join('')}
    <!-- Delete Button -->
    <button class="flex text-red-400 hover:text-red-600 text-2xl leading-none"
        onclick="deleteLead('${leadId}')">
        <i class="fa-solid fa-xmark"></i>
    </button>
</div>

                                    `;
                                    container.appendChild(leadDiv);
                                    document.getElementById("rVentricular").value = leadCount + 1;

                                    // Add event listener for the new checkbox
                                    const newCheckbox = document.getElementById(leadId);
                                    newCheckbox.addEventListener('change', function () {
                                        if (this.checked) {
                                            // Uncheck all other lead toggles
                                            document.querySelectorAll('.lead-toggle').forEach(el => {
                                                if (el !== this) el.checked = false;
                                            });
                                        }
                                    });
                                }

                                function deleteLead(leadId) {
                                    const leadDiv = document.querySelector(`[data-lead-id="${leadId}"]`);
                                    leadDiv.remove();
                                    const container = document.getElementById("lvLeadsContainer");
                                    const leadCount = container.childElementCount;
                                    document.getElementById("rVentricular").value = leadCount;
                                }

                                // Add event listener for existing LV lead toggle
                                document.addEventListener('DOMContentLoaded', function() {
                                    document.querySelectorAll('.lead-toggle').forEach(input => {
                                        input.addEventListener('change', function () {
                                            if (this.checked) {
                                                // Uncheck all other lead toggles
                                                document.querySelectorAll('.lead-toggle').forEach(el => {
                                                    if (el !== this) el.checked = false;
                                                });
                                            }
                                        });
                                    });
                                });
                            </script>
</body>

</html>