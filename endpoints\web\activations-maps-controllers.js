import logger from "medcommon/logger";
import PatientResourceModel from "../../api/models/patient-resource.js";
import VMapResourceModel from "../../api/models/view-map-resource.js";
import JobSchedulerSocketModel from "../../api/models/job-scheduler.js";
import MapModel from "../../api/models/map.js";
import CommonUtils from "../../api/utils.js";

const utils = new CommonUtils();

export default {
  getActivactionMaps: async (req, res) => {
    const patientId = req.params.id;
    logger.debug("GET /%s/activationMaps", patientId);
    const maps = await new MapModel().findFree([`${patientId}`], "ID_PATIENT = :1 AND TYPE=0", "", true);
    let activationMaps = {};
    for (let i = 0; i < maps.length; i++) {
      const map = maps[i];
      const newData = { ...map };
      newData.idMap = map.id;
      const ecgInfo = await new VMapResourceModel().findFirst([`${map.id}`], "ID_MAP=:1 AND ID_RESOURCE_TYPE=1", "", true);
      newData.ecgInfo = ecgInfo;
      const tacInfo = await new VMapResourceModel().findFirst([`${map.id}`], "ID_MAP=:1 AND ID_RESOURCE_TYPE=8", "", true);
      newData.tcInfo = tacInfo;
      activationMaps[map.id] = newData;
    }
    const ecgList = await new PatientResourceModel().findFree([patientId], "ID_PATIENT = :1 AND ID_RESOURCE_TYPE = 1", "", true);
    const ctList = await new PatientResourceModel().findFree([patientId], "ID_PATIENT = :1 AND ID_RESOURCE_TYPE = 8", "", true);
    let activationMapsList = Object.values(activationMaps);
    return res.render("patient-maps", {
      list: activationMapsList,
      resources: { ecgList: ecgList, ctList: ctList },
    });
  },
  postCreateActivationMap: async (req, res) => {
    const patientId = req.params.id;
    const ecgSelezionato = req.body.ecg;
    const tacSelezionata = req.body.ctScan;
    const newMapId = await utils.getSequenceNextVal("S_MAPS_ID");
    logger.debug("Nuova mappa: ID:%s [ECG ID: %s - TAC ID: %s ]", newMapId, ecgSelezionato, tacSelezionata);
    const newMapData = {
      id: newMapId,
      idPatient: patientId,
      idUserCreate: 1,
      mapTag: res.locals.selectedPatient.statusTag,
      description: req.body.notes || "",
      status: -1,
      creationDate: new Date(),
      type: 0, //activaction_map..
    };
    const idJob1 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const idJob2 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const idJob3 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const idJob4 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const jobData1 = {
      id: idJob1,
      idPatient: patientId,
      idMap: newMapId,
      createdAt: new Date(),
      stage: "create_segmentation",
      idEcg: "default",
      jobStatus: 0,
      jobData: "{}",
    };
    await delay(1500); // Aspetta 2 secondi
    const jobData2 = {
      id: idJob2,
      idPatient: patientId,
      idMap: newMapId,
      createdAt: new Date(),
      stage: "segmentation",
      idEcg: "default",
      jobStatus: 0,
      jobData: "{}",
    };
    await delay(1500); // Aspetta 2 secondi
    const jobData3 = {
      id: idJob3,
      idPatient: patientId,
      idMap: newMapId,
      createdAt: new Date(),
      stage: "create_actmap_stage",
      idEcg: "default",
      jobStatus: 0,
      jobData: "{}",
    };
    await delay(1500); // Aspetta 2 secondi
    const jobData4 = {
      id: idJob4,
      idPatient: patientId,
      idMap: newMapId,
      createdAt: new Date(),
      stage: "actmap_stage",
      idEcg: "default",
      jobStatus: 0,
      jobData: "{}",
    };
    const newMap = new MapModel(newMapData);
    const newMapResEcg = new MapResourceModel({ idMap: newMapId, idResource: ecgSelezionato });
    const newMapResCtScan = new MapResourceModel({ idMap: newMapId, idResource: tacSelezionata });
    const jobSched1 = new JobSchedulerSocketModel(jobData1);
    const jobSched2 = new JobSchedulerSocketModel(jobData2);
    const jobSched3 = new JobSchedulerSocketModel(jobData3);
    const jobSched4 = new JobSchedulerSocketModel(jobData4);

    let success = false;
    //La transazione la prendo da uno dei modelli, è indifferente.
    const transaction = await newMap.createNewTransaction();
    try {
      success = (await newMap.save(transaction)).success;
      if (!success) throw Error("Error step save newMap");
      success = (await newMapResEcg.save(transaction)).success;
      if (!success) throw Error("Error step save newMapResEcg");
      success = (await newMapResCtScan.save(transaction)).success;
      if (!success) throw Error("Error step save newMapResCtScan");
      success = (await jobSched1.save(transaction)).success;
      if (!success) throw Error("Error step save jobSched1");
      success = (await jobSched2.save(transaction)).success;
      if (!success) throw Error("Error step save jobSched2");
      success = (await jobSched3.save(transaction)).success;
      if (!success) throw Error("Error step save jobSched3");
      success = (await jobSched4.save(transaction)).success;
      if (!success) throw Error("Error step save jobSched4");
      await newMap.commitTransaction(transaction);
    } catch (error) {
      logger.error("Error on POST new activation map: %O", error);
      //Anche qua è indifferente il model che fà rollback o commit.
      await newMap.rollbackTransaction(transaction);
    } finally {
      await newMap.disposeTransaction(transaction);
    }
    return res.redirect("/patients/" + patientId + "/activationMaps");
  },
};
