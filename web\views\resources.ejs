<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <%- include('components/head') %>
  </head>
  <body>
    <main>
      <div class="flex flex-wrap gap-6 p-4 overflow-y-auto">
        <div class="card w-64 rounded-lg bg-[#3a3a3a] font-sans h-36 flex flex-col justify-between cursor-pointer overflow-hidden"
          data-id="">
          <div class="bg-[#2c2c2c] rounded-t-lg text-center">
            <div class="text-base font-semibold uppercase tracking-wide p-2 text-gray-200"><p class="text-center"><i class="fa-solid fa-plus"></i></p></div>
          </div>
          <div class="text-sm leading-tight flex flex-col justify-between text-gray-300">
            <p class="text-center p-4 ">Acquire new data</p>
          </div>
          <div class="flex justify-end m-2"></div>
        </div>
      <% if (list && list.length > 0) { %> <% list.forEach(resource => { %>
        <%if (resource.type == "ecg") {%>
          <a href="javascript:top.location.href='ecg?resid=<%=resource.id%>'">
        <%} else {%>
          <a href="javascript:top.location.href='resources/<%=resource.id%>'">
        <%}%>
        <div class="card w-64 rounded-lg bg-[#3a3a3a] font-sans h-36 flex flex-col justify-between cursor-pointer overflow-hidden"
          data-id="<%= resource.id %>">
          <div class="bg-[#2c2c2c] rounded-t-lg text-center">
            <div class="text-base font-semibold uppercase tracking-wide p-2 text-gray-200"><%= resource.type %></div>
          </div>
          <div class="text-sm leading-tight flex flex-col justify-between">
            <p class="text-center p-4"><%= resource.text %></p>
          </div>
          <div class="flex justify-end m-2">
            <% let currText = resource.status === 0 ? 'Before' : resource.status === 1 ? 'After' : 'Follow up'; %>
            <% let currColor = resource.status === 0 ? 'bg-green-500' : resource.status === 1 ? 'bg-yellow-500' : 'bg-red-500';%>
            <%- include('./components/badge-text', {badgeColor:currColor, badgeText:currText})  %>
          </div>
        </div>
        </a>
      <% }); } %> 
      </div>
    </main>

    <!--<script type="module">
      document.addEventListener('DOMContentLoaded', () => {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card) => {
          card.addEventListener('click', () => {
            top.location.href = 'resources/' + card.dataset.id;
          });
        });
      });
    </script>-->
  </body>
</html>
