const OAuth2Server = require('@node-oauth/oauth2-server');
const AbstractGrantType = OAuth2Server.AbstractGrantType;
const InvalidArgumentError = OAuth2Server.InvalidArgumentError;
const InvalidRequestError = OAuth2Server.InvalidRequestError;

const jwt = require("jsonwebtoken");
const logger = require("medcommon/logger");

class TokenExchangeGrantType extends AbstractGrantType {
    constructor(opts) {
        super(opts);
    }

    async handle(request, client) {
        if (!request) throw new InvalidArgumentError('Missing `request`');
        if (!client) throw new InvalidArgumentError('Missing `client`');

        let scope = this.getScope(request);
        let jwtData = await this.getUserFromJwtToken(request);
        let user = jwtData.user
        logger.debug("User from jwtData: %O", user);
        return this.saveToken(user, client, scope);
    }

    async saveToken(user, client, scope) {
        this.validateScope(user, client, scope);

        let token = {
            accessToken: await this.generateAccessToken(client, user, scope),
            accessTokenExpiresAt: this.getAccessTokenExpiresAt(),
            refreshToken: await this.generateRefreshToken(client, user, scope),
            refreshTokenExpiresAt: this.getRefreshTokenExpiresAt(),
            scope: scope
        };

        return this.model.saveToken(token, client, user);
    }

    async getUserFromJwtToken(request) {
        let toRet = false;
        let { subject_token } = request.body;
        if (subject_token) {
            let jwtSecret = process.env.JWT_SECRET_KEY ||  process.env.OAUTH2_JWT_SECRET || "01234567890";
            let decoded = jwt.verify(subject_token, jwtSecret);
            if (decoded) {
                toRet = decoded;
            }
        }
        return toRet;
    }
}

module.exports = TokenExchangeGrantType;