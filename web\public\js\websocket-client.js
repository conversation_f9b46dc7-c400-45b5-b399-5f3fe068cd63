// Crea una connessione WebSocket
const socket = new WebSocket(webSocketUrl);
// Gestisci l'apertura della connessione
socket.addEventListener("open", () => {
  console.log("Connected to the WebSocket server ->", webSocketUrl);
});
// Gestisci i messaggi ricevuti dal server
socket.addEventListener("message", (event) => {
  const data = JSON.parse(event.data);
  if (data && data["event-type"]) {
    // Dispatcha eventi personalizzati in base al tipo di evento
    const customEvent = new CustomEvent(data["event-type"], { detail: data });
    window.dispatchEvent(customEvent);
  } else {
    console.log("Message from server:", event.data);
  }
});
/*socket.addEventListener("server-status", (event) => {
  console.log("Server-status:", event);
});*/
