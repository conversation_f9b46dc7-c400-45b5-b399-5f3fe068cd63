import logger from "medcommon/logger";
import oracle from "medcommon/oracle";
import path from "path";
import fs from "fs";
import FormData from "form-data";
import axios from "axios";

const projRootDir = process.cwd();

export default {
  getSequenceNextVal: async (sequenceName) => {
    let toRet = -1;
    try {
      const seqSql = `SELECT ${sequenceName}.nextval id from dual`;
      const respId = await oracle.dbSingleRow(seqSql);
      toRet = respId["ID"] || -1;
    } catch (error) {
      logger.error("Error on getSequenceNextVal: %O", error);
    }
    return toRet;
  },
  getStorageFullPath: (idPatient, resType, idMap, ensureExist = false) => {
    let resourceURL = "";
    switch (resType) {
      case 1:
      case 8:
        resourceURL = `/storage/${idPatient}/`;
        break;
      default:
        resourceURL = `/storage/${idPatient}/map-${idMap}/`;
        break;
    }
    if (ensureExist && !fs.existsSync(resourceURL)) {
      try {
      } catch (error) {
        logger.error(error);
      }
    }
    return resourceURL;
  },
  uploadFile: async (url, fileName, fullFilePath) => {
    let toRet = false;
    try {
      const fileBuffer = fs.createReadStream(fullFilePath);
      const form = new FormData();
      form.append(fileName, fileBuffer);
      let headers = {
        "Content-Type": `multipart/form-data; boundary=${form.getBoundary()}`,
      };
      // Invia la richiesta POST
      const response = await axios.post(url, form, { headers });
      logger.verbose("File uploaded successfully:%O", response.data);
      toRet = response.status == 202;
    } catch (error) {
      logger.error("Error uploading file:%O", error.response ? error.response.data : error.message);
      toRet = false;
    }
    return toRet;
  },
};
