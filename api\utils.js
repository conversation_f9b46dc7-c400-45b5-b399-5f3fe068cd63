import logger from "medcommon/logger";
import oracle from "medcommon/oracle";
import path from "path";
import fs from "fs";
import FormData from "form-data";
import axios from "axios";
import MapModel from "../api/models/map.js";
import MapResourceModel from "../api/models/map-resource.js";
import PatientResourceModel from "../api/models/patient-resource.js";
import SimulationModel from "../api/models/simulation.js";

const projectRootDir = process.cwd();

export default class CommonUtils {
  constructor() {}
  async getSequenceNextVal(sequenceName) {
    let toRet = -1;
    try {
      const seqSql = `SELECT ${sequenceName}.nextval id from dual`;
      const respId = await oracle.dbSingleRow(seqSql);
      toRet = respId["ID"] || -1;
    } catch (error) {
      logger.error("Error on getSequenceNextVal: %O", error);
    }
    return toRet;
  }
  getStorageFullPath(idPatient, resType, idMap, ensureExist = false) {
    let resourceURL = "";
    switch (resType) {
      case 1:
      case 8:
        resourceURL = path.join(projectRootDir, `storage`, `${idPatient}`);
        break;
      default:
        resourceURL = path.join(projectRootDir, `storage`, `${idPatient}`, `map-${idMap}`);
        break;
    }
    if (ensureExist && !fs.existsSync(resourceURL)) {
      try {
        fs.mkdirSync(resourceURL, { recursive: true });
      } catch (error) {
        logger.error(error);
      }
    }
    return resourceURL;
  }
  async uploadFile(url, fileName, fullFilePath) {
    let toRet = false;
    try {
      const fileBuffer = fs.createReadStream(fullFilePath);
      const form = new FormData();
      form.append(fileName, fileBuffer);
      let headers = {
        "Content-Type": `multipart/form-data; boundary=${form.getBoundary()}`,
      };
      // Invia la richiesta POST
      const response = await axios.post(url, form, { headers });
      logger.verbose("File uploaded successfully:%O", response.data);
      toRet = response.status == 202;
    } catch (error) {
      logger.error("Error uploading file:%O", error.response ? error.response.data : error.message);
      toRet = false;
    }
    return toRet;
  }
  async downloadFile(url) {
    let toRet = false; // Variabile di ritorno inizializzata a false
    try {
      const response = await axios({
        method: "get",
        url: url,
        responseType: "arraybuffer", // Imposta il tipo di risposta su arraybuffer per ottenere i dati binari
      });
      const disposition = response.headers["content-disposition"];
      let fileName = "downloaded_file"; // Nome di default
      if (disposition && disposition.indexOf("attachment") !== -1) {
        const matches = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (matches != null && matches[1]) {
          fileName = matches[1].replace(/['"]/g, ""); // Rimuovi eventuali virgolette
        }
      }
      // Imposta toRet con l'oggetto contenente il nome del file e il contenuto come buffer
      toRet = {
        fileName,
        content: Buffer.from(response.data), // Converti i dati in un buffer
      };
    } catch (error) {
      logger.error("Error downloading file:", error);
      // toRet rimane false in caso di errore
    }
    return toRet; // Restituisci toRet
  }

  /** Aggiunge alla risorse il file indicato e lo associa alla mappa indicata. */
  async createDbResource(idPatient, idMap, resIdType, resNotes, fileName) {
    try {
      //Verifico se per il paziente esiste già la stessa risorsa (tipo e nome file identici)
      const resourceExist = await new PatientResourceModel().findFirst(
        [idPatient, resIdType, fileName],
        "ID_PATIENT = :1 AND ID_RESOURCE_TYPE = :2 AND UNIQUE_ID = :3",
        "",
        true
      );
      if (!resourceExist.id) {
        const newResId = await this.getSequenceNextVal("S_RESOURCES_ID");
        const newResData = {
          id: newResId,
          idPatient: idPatient,
          idUserCreate: 1,
          idUserUpdate: 1,
          idResourceType: resIdType,
          resourceNotes: resNotes,
          resourceTag: "before",
          resourceOrder: newResId,
          resourceDate: new Date(),
          uuid: fileName,
        };
        const newResource = new PatientResourceModel(newResData);
        const newMapResData = {
          idMap: idMap,
          idResource: newResId,
        };
        const newMapResource = new MapResourceModel(newMapResData);
        //Save storage transactioned:
        let success = false;
        //La transazione la prendo da uno dei modelli, è indifferente.
        const transaction = await newResource.createNewTransaction();
        try {
          success = (await newResource.save(transaction)).success;
          if (!success) throw Error("Error step save newResource");
          success = (await newMapResource.save(transaction)).success;
          if (!success) throw Error("Error step save newMapResource");
          await newResource.commitTransaction(transaction);
        } catch (error) {
          logger.error("Error on POST new activation map: %O", error);
          //Anche qua è indifferente il model che fà rollback o commit.
          await newResource.rollbackTransaction(transaction);
        } finally {
          await newResource.disposeTransaction(transaction);
        }
      } else {
        logger.debug("Resource of type %s with uniqueId '%s' already exists with id: %s", resIdType, fileName, resourceExist.id);
      }
    } catch (error) {
      logger.error("Error on createDbResource:%O", error);
    }
  }
  async setMapReady(mapId) {
    let toRet = false;
    const myMap = await new MapModel().findByKeys([mapId]);
    if (myMap) {
      myMap.status = 1;
      toRet = (await myMap.update()).success;
    }
    return toRet;
  }
  async setSimulationReady(simId) {
    let toRet = false;
    const mySim = await new SimulationModel().findByKeys([simId]);
    if (mySim) {
      mySim.status = 1;
      toRet = (await mySim.update()).success;
    }
    return toRet;
  }
}
