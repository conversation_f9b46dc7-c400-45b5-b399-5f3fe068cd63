<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("patient.heading") %> <% icon = "fa-solid fa-user" %> <%- include('components/head') %>
    <style></style>
  </head>
  <body class="h-screen">
    <%- include('components/header') %> <%- include('components/patient-btns-editdelete') %> <%- include('components/patient-tabs') %>
    <!-- main content fill entire height -->
    <main class="h-full w-full overflow-y-auto">
      <div class="flex flex-wrap gap-6 p-4 overflow-y-auto">
        <% if (list && list.length > 0) { %> <% list.forEach(resource => { %>
        <a href="/patients/<%=patientId%>/simulation?simId=<%=resource.id%>" <% if (resource.status == -2){ %>
           onclick="event.preventDefault();"
          <%}%>
          class="cursor-pointer">
          <div
            class="card w-64 rounded-lg bg-[#3a3a3a] font-sans h-36 flex flex-col justify-between cursor-pointer overflow-hidden"
            data-id="<%= resource.id %>"
          >
            <div class="bg-[#2c2c2c] rounded-t-lg text-center">
              <div class="text-base font-semibold uppercase tracking-wide p-2 text-gray-200"><%= resource.description %></div>
            </div>
            <div class="text-sm leading-tight flex flex-col justify-between">
              <p class="text-center p-4"><%= locDateTime(resource.createdAt) %></p>
            </div>
            <% if (resource.status == -1) { %>
            <div class="flex justify-center align-center py-2 border-t border-darkgray gap-2">
              <div class="loading-spinner-small animate-spin rounded-full border-t-darkred border-solid w-8 h-8"></div>
              <span class="ml-2 text-gray-300"><%= t('simulations.processing') %></span>
            </div>
            <% } else { %>
            <div class="flex justify-end m-2">
              <% let currText = 'simulation.ready'; %> <% let currColor = 'bg-green-500'; %> <%- include('./components/badge-text', {badgeColor:currColor,
              badgeText:currText}) %>
            </div>
            <% } %>
          </div>
        </a>
        <% }); } else { %>
        <div class="bg-[#2c2c2c] rounded-t-lg text-center">
          <div class="text-base font-semibold uppercase tracking-wide p-2 text-gray-200"><%= t('simulations.emptylist') %></div>
        </div>
        <% } %>
      </div>
    </main>
    <script>
      // Aggiungi un listener per l'evento job-completed
      window.addEventListener("job-completed", (event) => {
          const data = event.detail; // Ottieni i dati dall'evento
          // Controlla se il stage è 'simulation'
          if (data.stage === 'simulation') {
            window.location.reload(true);
          }
      });
    </script>
  </body>
</html>
