<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("patient.heading") %> <% icon = "fa-solid fa-user" %> <%- include('components/head') %>
    <style></style>
  </head>
  <body class="h-screen">
    <%- include('components/header') %> <%- include('components/patient-btns-editdelete') %> <%- include('components/patient-tabs') %>
    <!-- main content fill entire height -->
    <main class="h-full w-full overflow-y-auto">
      <div class="flex flex-wrap gap-6 p-4 overflow-y-auto">
        <% if (list && list.length > 0) { %> <% list.forEach(resource => { %>
          <div class="card w-[480px] h-[150px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between overflow-hidden" data-id="<%= resource.id %>">
            <div class="bg-[#2c2c2c] rounded-t-lg flex items-center justify-between p-1">
              <div class="font-semibold uppercase tracking-wide p-2 text-gray-200 text-xl">
                <span class="mr-2"><%= resource.description %></span>
              </div>
              <div class="px-3">
                <button onclick="" class="text-white hover:text-gray-300">
                  <i class="fa-solid fa-ellipsis-vertical text-2xl"></i>
                </button>
              </div>
            </div>
            <a href="/patients/<%=patientId%>/simulation?simId=<%=resource.id%>" 
                <% if (resource.status == -1){ %>
                onclick="event.preventDefault();"
                <%} else {%>
                  class="cursor-pointer"
                <%}%>>
            <div class="flex flex-col flex-grow text-white p-3">
              <div class="text-sm leading-tight flex flex-col justify-between">
                <p class="text-center p-4"><%= locDateTime(resource.createdAt) %></p>
              </div>
            <% if (resource.status == -1) { %>
            <div class="flex justify-center align-center py-2 border-t border-darkgray gap-2">
              <div class="loading-spinner-small animate-spin rounded-full border-t-darkred border-solid w-8 h-8"></div>
              <span class="ml-2 text-gray-300"><%= t('simulation.processing') %></span>
            </div>
            <% } else { %>
            <div class="flex justify-end m-2">
              <% let currText = 'simulation.ready'; %> <% let currColor = 'bg-green-500'; %>
              <%- include('./components/badge-text', {badgeColor:currColor, badgeText:currText}) %>
            </div>
            <% } %>
            </div>
          </a>
         </div>
        <% }); } else { %>
        <div class="h-full w-full bg-[#2c2c2c] rounded-t-lg text-center items-center">
          <div class="text-base font-semibold uppercase tracking-wide p-2 text-gray-200"><%= t('simulation.emptylist') %></div>
        </div>
        <% } %>
      </div>
    </main>
    <script>
      // Aggiungi un listener per l'evento job-completed
      window.addEventListener("job-completed", (event) => {
          const data = event.detail; // Ottieni i dati dall'evento
          // Controlla se il stage è 'simulation'
          if (data.stage === 'simulation') {
            window.location.reload(true);
          }
      });
      function toggleMenu(event) {
          const menu = document.getElementById('dropdownMenu');
          const rect = event.target.getBoundingClientRect();
          
          // Posiziona il menu accanto all'icona
          menu.style.top = `${rect.bottom + window.scrollY}px`;
          menu.style.left = `${rect.left}px`;
          
          // Mostra o nascondi il menu
          menu.classList.toggle('hidden');
      }

      // Chiudi il menu se si clicca al di fuori
      window.onclick = function(event) {
          const menu = document.getElementById('dropdownMenu');
          if (!event.target.closest('.px-3')) {
              menu.classList.add('hidden');
          }
      };
    </script>
    <div id="dropdownMenu" class="hidden absolute bg-gray-800 text-white rounded-lg shadow-lg p-2">
        <ul>
            <li class="p-2 hover:bg-gray-700 cursor-pointer">Opzione 1</li>
            <li class="p-2 hover:bg-gray-700 cursor-pointer">Opzione 2</li>
            <li class="p-2 hover:bg-gray-700 cursor-pointer">Opzione 3</li>
        </ul>
    </div>
  </body>
</html>
