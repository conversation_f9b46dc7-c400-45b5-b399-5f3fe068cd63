/**
 * Gestisce automaticamente la risposta positiva (data.status) con i dati passati su data.
 * @param {*} res
 * @param {*} data
 * @returns
 */
module.exports.handleResponse = handleResponse = (res, data) => {
  if (data.status) {
    let status = data.status;
    delete data.status;
    let cleanData = data;
    return res.status(status).send(cleanData);
  }
  return res.status(200).send(data);
};

/**
 * Gestisce automaticamente l'invio della risposta di errore
 * @param {*} res
 * @param {*} err
 * @returns
 */
module.exports.handleError = handleError = (res, err = {}) => res.status(err.status || 500).send({ error: err.message });