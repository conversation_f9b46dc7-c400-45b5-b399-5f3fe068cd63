<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("patient.addNew") %>
    <% icon = "fa-solid fa-user-plus" %>
  <%- include('components/head') %>
  <link rel="stylesheet" href="/css/flatpickr.min.css">
  <script src="/js/flatpickr.js"></script>
  </head>
  <body>
    <%- include('components/header') %>
      <form
        id="patientForm" 
        method="POST"
        action="<%= patient && patient.ID ? `/patients/${patient.ID}/edit` : '/patients/new' %>"
        class="bg-darkgray-light w-full max-w-2xl rounded-lg shadow-lg p-8 space-y-6 select-none mt-8"
      >
        <!-- Title -->
        <!--<h1 class="text-3xl font-semibold mb-6 text-on-darkgray text-center"><%= patient && patient.ID ? t("patient.edit") : t("patient.addNew") %></h1>-->

        <!-- Name -->
        <div class="flex items-center space-x-4">
          <div class="flex-1">
            <label for="FIRST_NAME" class="block mb-1 font-semibold text-on-darkgray"><%= t("firstName") %></label>
            <input
              class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
              type="text"
              autofocus
              id="FIRST_NAME"
              name="FIRST_NAME"
              value="<%= patient?.FIRST_NAME || '' %>"
              maxlength="25"
              aria-required="true"
            />
          </div>

          <div class="flex-1">
            <label for="LAST_NAME" class="block mb-1 font-semibold text-on-darkgray"><%= t("lastName") %></label>
            <input
              class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
              type="text"
              id="LAST_NAME"
              name="LAST_NAME"
              value="<%= patient?.LAST_NAME || '' %>"
              maxlength="25"
              aria-required="true"
            />
          </div>
        </div>

        <!-- Dob and Gender -->
        <div class="flex items-center space-x-4">
          <div class="flex-1">
            <label for="BIRTH_DATE" class="block mb-1 font-semibold text-on-darkgray"><%= t("birthDate") %></label>
            <input
              type="text"
              id="BIRTH_DATE"
              name="BIRTH_DATE"
              value="<%= patient?.BIRTH_DATE ? locDateTime(patient.BIRTH_DATE) : t('dateFormat') %>"
              required
              class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
              aria-required="true"
            />
          </div>

          <div class="flex-1">
            <label for="GENDER" class="block mb-1 font-semibold text-on-darkgray"><%= t("gender") %></label>
            <select
              id="GENDER"
              name="GENDER"
              required
              class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
              style="height: 50px;"
              aria-required="true"
            >
            <option value="" disabled <%= !patient?.gender ? "selected" : "" %>>-- <%= t("dropDownSelect") %> --</option>
            <option value="M" <%= patient?.GENDER === "M" ? "selected" : "" %>><%= t("male") %> (M)</option>
            <option value="F" <%= patient?.GENDER === "F" ? "selected" : "" %>><%= t("female") %> (F)</option>
            </select>
          </div>
        </div>

        <div class="flex-1">
            <label for="Status" class="block mb-1 font-semibold text-on-darkgray"><%= t("Status") %></label>
            <select
              id="Status"
              name="Status"
              required
              class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
              style="height: 50px;"
              aria-required="true"
            >
            <option value="" disabled <%= !patient?.STATUS_TAG ? "selected" : "" %>>-- <%= t("dropDownSelect") %> --</option>
            <option value="before" <%= patient?.STATUS_TAG === "before" ? "selected" : "" %>>Pre-procedure</option>
            <option value="after" <%= patient?.STATUS_TAG === "after" ? "selected" : "" %>>Post-procedure</option>
            <option value="follow_up" <%= patient?.STATUS_TAG === "follow_up" ? "selected" : "" %>>Follow-up</option>
            </select>
          </div>

        <!-- EF -->
        <div class="flex items-center">
          <div class="flex-1">
            <label for="EF" class="block font-semibold text-on-darkgray"><%= t("ef") %></label>
            <p class="text-sm text-gray-400 my-2"><%= t("efRangeHint") %></p>
            <div class="flex items-center">
              <span class="text-sm text-gray-400 mr-2">10</span>
              <input
                type="number"
                id="EF"
                name="EF"
                min="10"
                max="80"
                step="1"
                value="<%= patient?.EF || '' %>"
                required
                class="flex-1 p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
                aria-required="true"
              />
              <span class="text-sm text-gray-400 ml-2">80</span>
            </div>
          </div>
        </div>

        <!-- ESV -->
        <div class="flex items-center">
          <div class="flex-1">
            <label for="esv" class="block font-semibold text-on-darkgray"><%= t("esv") %></label>
            <p class="text-sm text-gray-400 my-2"><%= t("esvRangeHint") %></p>
            <div class="flex items-center">
              <span class="text-sm text-gray-400 mr-2">10</span>
              <input
                type="number"
                id="ESV"
                name="ESV"
                min="10"
                max="1000"
                step="1"
                value="<%= patient?.ESV || '' %>"
                required
                class="flex-1 p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
                aria-required="true"
              />
              <span class="text-sm text-gray-400 ml-2">1000</span>
            </div>
          </div>
        </div>

        <!-- Submit and Cancel Buttons -->
        <div class="pt-4 flex flex-row-reverse justify-between items-center space-x-reverse space-x-4">
          <button
            tabindex="0"
            type="submit"
            class="w-full bg-darkred text-on-darkred px-6 py-3 rounded hover:bg-darkred-light transition focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-darkred"
          >
            <%= patient && patient.ID ? t("saveChanges") : t("add") %>
          </button>
          <button
            type="button"
            onclick="window.history.back()"
            class="w-full bg-gray-600 text-on-darkgray px-6 py-3 rounded hover:bg-gray-700 transition focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            <%= t("cancel") %>
          </button>
        </div>
      </form>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        flatpickr("#BIRTH_DATE", {
          altInput: true,
          altFormat: "<%= t('pickDateFormat') %>",
          dateFormat: "Z",
          minDate: "01/01/1920",
          maxDate: "today",
          allowInput: true,
          theme: "dark"
        });
      });

      document.getElementById('patientForm').addEventListener('submit', function(event) {
        event.preventDefault();

        // Get the form element
        const form = document.getElementById('patientForm');

        // Get form data using the actual field names
        const formData = {
          FIRST_NAME: form.FIRST_NAME.value,
          LAST_NAME: form.LAST_NAME.value,
          BIRTH_DATE: form.BIRTH_DATE.value,
          GENDER: form.GENDER.value,
          EF: form.EF.value,
          ESV: form.ESV.value,
          STATUS_TAG: form.Status.value
        };

        // Get the form method and action URL
        const method = form.getAttribute('method');
        const url = form.getAttribute('action');

        // Send the data as form-encoded (matching backend expectation)
        const formBody = new URLSearchParams(formData);

        fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formBody,
        })
          .then((response) => {
            if (response.redirected) {
              window.location.href = response.url;
            } else if (response.ok) {
              // Handle success case
              window.location.href = '/patients/search';
            } else {
              throw new Error('Network response was not ok');
            }
          })
          .catch((error) => {
            console.error('Error:', error);
            // Handle errors here (e.g., show error message)
          });
      });
    </script>
  </body>
</html>
