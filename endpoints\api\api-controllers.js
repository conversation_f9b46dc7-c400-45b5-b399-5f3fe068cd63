import fs from 'fs';

//const mockDataFile = './web/public/json/50patients.json';
//const patients = JSON.parse(fs.readFileSync(mockDataFile, 'utf-8'));

export default {
    /*patientGet: async (req, res) => {
        let { query } = req.query;
        const normalizedQuery = query.trim().toLowerCase();
        const result = patients.filter(p =>
            p.firstName.toLowerCase().includes(normalizedQuery) ||
            p.lastName.toLowerCase().includes(normalizedQuery)
        );
        res.json(result).end();
    },
    patientPost: async (req, res) => {
        let patient = req.body;
        patient.lastAccess = new Date().toISOString().split('T')[0];
        try {
            // add patient to json file
            patients.push(patient);
            fs.writeFileSync(mockDataFile, JSON.stringify(patients));
            res.json(patient);
        } catch (error) {
            res.status(500).json({ success: false, message: error.message });
        }
    },
    patientPut: async (req, res) => {
        try {
            const patientId = req.params.id;
            let patient = req.body;
            patient.code = patientId;
            patient.lastAccess = new Date().toISOString().split('T')[0];
            // update patient in json file
            const index = patients.findIndex((p) => p.code === patient.code);
            patients[index] = patient;
            fs.writeFileSync(mockDataFile, JSON.stringify(patients));
            res.json(patient);
        } catch (error) {
            res.status(500).json({ success: false, message: error.message });
        }
    },
    patientDelete: async (req, res) => {
        const patientId = req.params.id;
        try {
            if (patientId == undefined) {
                res.status(400).json({ success: false, message: 'Patient ID is required' });
                return;
            }
            if (patientId === 'P001' || patientId === 'P002' || patientId === 'P003') {
                res.status(400).json({ success: false, message: 'Cannot delete default patient' });
                return;
            }
            // delete patient from json file
            const index = patients.findIndex((p) => p.code === patientId);
            patients.splice(index, 1);
            fs.writeFileSync(mockDataFile, JSON.stringify(patients));
            res.json({ success: true, message: `Patient ${patientId} deleted successfully` });
        } catch (error) {
            res.status(500).json({ success: false, message: error.message });
        }
    }*/
};