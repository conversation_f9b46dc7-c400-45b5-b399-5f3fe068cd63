import { AbstractDbViewModel } from "cruder/abstractView";
class VMapResourceModel extends AbstractDbViewModel {
  static schema = {
    schemaName: "NICE",
    tableName: "V_MAP_RESOURCES",
    keys: ["ID_MAP"],
    fields: [
        "DESCRIPTION",
        "MAP_TAG",
        "ID_PATIENT",
        "ID_RESOURCE",
        "RESOURCE_DATE",
        "RESOURCE_NOTES",
        "RESOURCE_ORDER",
        "RESOURCE_TAG",
        "ID_RESOURCE_TYPE",
        "ID_RESOURCE_TYPE_TEXT",
        "UNIQUE_ID",
        "STATUS",
    ],
    mapping: {
        ID_MAP: "idMap",
        DESCRIPTION: "mapDescription",
        MAP_TAG: "mapTag",
        ID_PATIENT: "idPatient",
        ID_RESOURCE: "idResource",
        RESOURCE_DATE: "resourceDate",
        RESOURCE_NOTES: "resourceNotes",
        RESOURCE_ORDER: "resourceOrder",
        RESOURCE_TAG: "resourceTag",
        ID_RESOURCE_TYPE: "idResourceType",
        ID_RESOURCE_TYPE_TEXT: "idResourceTypeText",
        UNIQUE_ID: "uniqueId",
        STATUS: "status",
    },
  };
  constructor(data = {}, dbType = "oracle") {
    super(data, dbType);
  }
}
export default VMapResourceModel;
