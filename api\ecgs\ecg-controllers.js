export default {
  acquireNewEcg: async (req, res) => {
    const deviceId = '00A037009B036470';
    const sessionId = 'TEMPSTEF';
    const url = `http://10.1.2.11:8889/acquire-ecg-10s/${deviceId}/${sessionId}`;
    let response;
    try {
      response = await axios.request({
        method: 'GET',
        url: url,
      });
    } catch (error) {
      logger.error('error on acquireNewEcg: %O', error);
      throw error.response.data;
    }
    res.send(response.data);
  },
  saveEcg: async (req, res) => {},
  deleteEcg: async (req, res) => {},
};
