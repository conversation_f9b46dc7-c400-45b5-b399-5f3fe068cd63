const config = require("medcommon/config");
const logger = require("medcommon/logger");
const DbAdapter = require("./db-adapter");
const postgres = require("pg");

class DbPostgres extends DbAdapter {
  #connPool;

  constructor() {
    super();
  }

  async init() {
    try {
      this.#connPool = new postgres.Pool(config.postgresConfig);
    }
    catch (error) {
      logger.error("ERROR on DbPostgres.init(): %O", error);
    }
  }

  /**
 * Recupera una connessione Oracle dal pool di connessioni.
 * @returns Oggetto connessione.
 */
  async getDBConnection() {
    let conn = await this.#connPool.connect();
    logger.debug("DbPostgres.getDbConnection -> from connPool: %O", conn.processID);
    return conn;
  }

  async dbExecute(query, params = [], options = {}) {
    logger.debug("DbPostgres.dbExecute", query, params, options);
    let toRet = this.defDbExecuteResponse();
    toRet.dbOperation = this.detectQueryOperation(query);
    let conn;
    try {
      conn = await this.getDBConnection();
      const result = await conn.query(query, params);
      toRet = this.fillResponseFromResult(result);
    } catch (error) {
      logger.error("ERROR on DbPostgres.dbExecute:%O", error);
      toRet.success = false;
      toRet.error = error.message;
    } finally {
      try {
        await conn.end();
      } catch (err) {
        logger.error("ERROR on DbPostgres.conn.end(). %s", err.message);
      }
    }
    return toRet;
  }

  fillResponseFromResult(result) {
    let toRet = this.defDbExecuteResponse();
    logger.info("DbMaria.dbExecute result:%O", result);
    if (result) {
      const resJson = {};
      if (result.rows) {
        resJson.rows = result.rows;
        resJson.rowsAffected = result.length;
      }
      if (result.rowCount && result.rowCount > 0) {
        resJson.rowsAffected = result.rowCount;
      }
      resJson.success = true;
      toRet = { ...toRet, ...resJson };
    }
    return toRet;
  }

  async getNewTransaction() {
    const conn = await this.getDBConnection();
    // Inizia la transazione
    await conn.query('BEGIN');
    return conn;
  }
  async transactExecute(conn, query, params) {
    logger.debug("DbPostgres.transactExecute", query, params);
    let toRet = this.defDbExecuteResponse();
    toRet.dbOperation = this.detectQueryOperation(query);
    if (conn) {
      try {
        const result = await conn.query(query, params);
        //logger.info("DbMaria.transactExecute result:%O", result);
        toRet = this.fillResponseFromResult(result);
      } catch (error) {
        logger.error("ERROR on DbPostgres.transactExecute:%O", error);
        toRet.success = false;
        toRet.error = error.message;
      }
    }
    return toRet;
  }

  async commit(conn) {
    if (conn) await conn.query('COMMIT');
  }

  async rollback(conn) {
    if (conn) await conn.query('ROLLBACK');
  }

  /** Rilascia nel modo nativo la connessione.
   * @param {*} conn 
   */
  async release(conn) {
    if (conn) conn.release();
  }

  //#region Formattazione specifica queries del client db
  formatTableName(schema = {}) {
    return `"${schema.schemaName}"."${schema.tableName}"`;
  }
  formatAllFieldsNames(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    return allFields.map((key) => `"${key}"`).join(',')
  }
  //Caso insert into table (....) values(§,§,§,§)
  formatAllFieldsParams(schema = {}) {
    const allFields = [...schema.keys || [], ...schema.fields || []];
    let i = 0;
    const allStr = allFields.map((key) => {
      i++;
      return `$${i}`;
    }).join(',');
    return allStr;
  }
  //Caso update table set (A=§, B=§) where .... 
  formatUpdateParams(schema = {}) {
    let i = 0;
    const updStr = schema.fields.map((key) => {
      i++;
      return `"${key}"=$${i}`;
    }).join(',');
    return updStr;
  }
  //Caso della clausola WHERE => A=§ AND B=§ AND C=§
  formatWhereParams(schema = {}, startIndex = 0) {
    const keys = schema.keys || [];
    let i = startIndex;
    const keysStr = schema.keys.map((key) => {
      i++;
      return `"${key}"=$${i}`;
    }).join(" AND ");
    return keysStr;
  }
  formatDbParam = (name, index) => `$${index}`;
  formatDbField = (name) => `"${name}"`;
  dbParamRegex = () => /\$\d+/g;
  //#endregion
}
module.exports = DbPostgres;
