canvas {
  display: block;
  width: 100wv;
  height: 100hv;
  background-color: #fff0f0;
}

#config-bar {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 10px;
  border-radius: 6px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

#config-bar button {
  font-size: 18px;
  margin: 0 4px;
  background: none;
  border: none;
  cursor: pointer;
}
#overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgb(100, 93, 93); /* sfondo semitrasparente */
  display: flex; /* Usa flexbox per allineare gli elementi */
  flex-direction: column; /* Posiziona lo spinner sopra e il testo sotto */
  justify-content: center; /* Centra verticalmente */
  align-items: center; /* Centra orizzontalmente */
  text-align: center;
  z-index: 1000; /* Lo mette sopra tutto */
}

#loader {
  width: 40px;
  height: 40px;
  border: 4px solid #ccc;
  border-top: 4px solid #cc0000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

#loadingText {
  color: #fff;
  font-size: 18px;
  font-family: Arial, sans-serif;
  /*margin-top: 20px;  Spazio tra spinner e testo */
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
