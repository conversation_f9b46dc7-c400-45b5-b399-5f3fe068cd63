{"name": "oauth2", "version": "1.0.0", "description": "Features OAUTH2 come modulo indipendente.", "author": "<PERSON>", "homepage": "", "license": "ISC", "private": true, "files": ["/oauth2.token.exchange.grant.js", "/oauth2.autopruner.js", "/oauth2.models.js", "/oauth2.server.js", "/oauth2.apis.js", "/oauth2.queries.js", "/oauth2.socketio.js", "/oauth2.routes.js", "/package.json"], "exports": {"./oauth2.models": "./oauth2.models.js", "./oauth2.models.js": "./oauth2.models.js", "./oauth2.routes": "./oauth2.routes.js", "./oauth2.routes.js": "./oauth2.routes.js", "./oauth2.apis": "./oauth2.apis.js", "./oauth2.apis.js": "./oauth2.apis.js", "./oauth2.queries": "./oauth2.queries.js", "./oauth2.queries.js": "./oauth2.queries.js", "./oauth2.server": "./oauth2.server.js", "./oauth2.server.js": "./oauth2.server.js", "./package.json": "./package.json"}, "dependencies": {"@elastic/ecs-winston-format": "^1.5.2", "@node-oauth/oauth2-server": "^5.1.0", "dateformat": "^4.5.1", "dotenv": "^14.3.2", "fs": "0.0.1-security", "jsonwebtoken": "^9.0.2", "ldap-authentication": "^3.0.3", "medcommon": "file:../medcommon", "moment-timezone": "^0.5.33", "node-fetch": "^3.3.2", "oracledb": "^5.4.0", "path": "^0.12.7", "winston": "^3.8.1", "winston-daily-rotate-file": "^4.7.1"}}