<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ECG list</title>
    <link href="/css/tailwind.css" rel="stylesheet" />
  </head>
  <body class="bg-darkgray text-white h-screen m-0 overflow-hidden">
    <div class="flex h-screen">
      <!-- Sidebar -->
      <div class="w-[350px] bg-darkgray-light p-4 flex flex-col justify-between overflow-y-auto">
        <div id="scanList">
          <table class="text-left w-full">
            <thead class="text-white border-b border-gray-700">
              <tr>
                <th class="pb-2"><%= t("dateTime") %></th>
                <th class="pb-2"><%= t("description") %></th>
              </tr>
            </thead>
            <tbody>
              <tr class="hover:bg-darkgray transition cursor-pointer">
                <td class="py-2 pl-2">28/03/2025 - 16:30</td>
                <td class="py-2">ECG before implant</td>
              </tr>
              <tr class="hover:bg-darkgray transition cursor-pointer">
                <td class="py-2 pl-2">18/05/2025 - 08:35</td>
                <td class="py-2">ECG after implant</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="flex flex-col items-center gap-4">
          <div class="flex flex-col gap-4 w-full px-4">
            <!-- Acquire 10s Button -->
            <button
              id="acquire-10s"
              class="w-full bg-darkgray hover:bg-gray-700 rounded-md flex items-center justify-start gap-4 p-4"
            >
              <div class="w-16 h-16 bg-darkred-light flex items-center justify-center text-white font-bold">10s</div>
              <span class="text-lg font-semibold text-white">Acquire 10 Seconds</span>
            </button>

            <!-- Acquire Continuous Button -->
            <button
              id="acquire-cont"
              class="w-full bg-darkgray hover:bg-gray-700 rounded-md flex items-center justify-start gap-4 p-4"
            >
              <div class="w-16 h-16 bg-darkred-light flex items-center justify-center text-white font-bold"></div>
              <span id="label" class="text-lg font-semibold text-white">Acquire Continuously</span>
            </button>
          </div>
        </div>

        <script>
          const acquire10sButton = document.getElementById('acquire-10s');
          const acquireContButton = document.getElementById('acquire-cont');
          let isRecordingCont = false;

          acquire10sButton.addEventListener('click', () => {
            // Save and disable
            const originalDiv = acquire10sButton.querySelector('div');
            const originalSpan = acquire10sButton.querySelector('span');
            acquire10sButton.disabled = true;

            // Remove red circle div
            originalDiv.remove();

            // Countdown
            let count = 0;
            for (let i = 0; i < 10; i++) {
              setTimeout(() => {
                count++;
                originalSpan.innerText = 'Recording... ' + count + 's';
              }, i * 1000);
            }

            // Restore after 10s
            setTimeout(() => {
              // Restore div
              acquire10sButton.insertBefore(originalDiv, originalSpan);
              // Restore label
              originalSpan.innerText = 'Acquire 10 Seconds';
              acquire10sButton.disabled = false;
            }, 10000);
          });

          acquireContButton.addEventListener('click', () => {
            const div = acquireContButton.querySelector('div');
            const span = acquireContButton.querySelector('#label');

            if (!isRecordingCont) {
              // Switch to "Recording" state
              div.classList.remove('rounded-full', 'bg-darkred-light');
              div.classList.add('rounded-sm', 'bg-black');
              div.innerHTML = '<span class="text-white font-bold">■</span>';
              span.innerText = 'Stop';

              // Start your acquisition logic here...
              isRecordingCont = true;
            } else {
              // Switch back to idle state
              div.classList.remove('rounded-sm', 'bg-black');
              div.classList.add('rounded-full', 'bg-darkred-light');
              div.innerHTML = '';
              span.innerText = 'Acquire Continuously';

              // Stop your acquisition logic here...
              isRecordingCont = false;
            }
          });
        </script>
      </div>
      <!-- Main Content -->
      <div class="flex-1 overflow-y-auto bg-gray-700'">
        <canvas id="ecgCanvas"></canvas>
      </div>
    </div>

    <script type="module">
      import { EcgData, EcgDrawer } from '/js/ecg-lib.js';

      document.addEventListener('DOMContentLoaded', () => {
        const rows = document.querySelectorAll('#scanList tr');
        rows.forEach((row, index) => {
          row.addEventListener('click', () => {
            selectedIndex = index;
            updateSelectedRow();
          });
        });
        let selectedIndex = -1;

        function updateSelectedRow() {
          rows.forEach((row, index) => {
            if (index == 0) return;
            if (index == selectedIndex) {
              row.classList.add('bg-darkred-light');
              row.classList.remove('hover:bg-darkgray');
            } else {
              row.classList.remove('bg-darkred-light');
              row.classList.add('hover:bg-darkgray');
            }
          });
        }
        updateSelectedRow();

        const canvas = document.getElementById('ecgCanvas');
        const drawer = new EcgDrawer(canvas);

        window.onload = async () => {
          await new Promise((resolve) => requestAnimationFrame(resolve));
          drawer.setCanvasDimensions(1000, 750);
        };
      });
    </script>
  </body>
</html>
