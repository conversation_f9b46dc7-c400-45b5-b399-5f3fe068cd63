---
# file: docker-compose.yaml
version: '3.8'
name: testing-xe
services:
  db:
    container_name: testing-oracle-xe-11
    image: gvenzl/oracle-xe:********-slim-faststart
    environment:
      ORACLE_PASSWORD: test
      APP_USER: ${USERORACLE}
      APP_USER_PASSWORD: ${PASSORACLE}
      TZ: Europe/Rome
    networks:
      - db_net
    ports: #per il debug esterno espongo la 1533 <- qui fare attenzione quindi..
      - 1533:1521
    volumes: 
      #Qui mappo gli scripts che verranno eseguiti alla CREAZIONE del container 
      - ./database/container-entrypoint-initdb.d:/container-entrypoint-initdb.d
    healthcheck:
      test: healthcheck.sh
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped
networks:
  db_net:
    driver: bridge
...
