class DbAdapter {
  constructor() {
    if (new.target === DbAdapter) {
      throw new TypeError("Cannot instantiate abstract class DbAdapter directly");
    }
  }

  /** Inizializza il pool di connessioni */
  async init() {
    throw new Error("init() must be implemented");
  }

  /**
   * @param {*} query 
   * @param {*} params 
   * @param {*} options 
   * @returns { "success": boolean, "rows": [], "rowsAffected": number, "error": string | null, "dbOperation": string }
   */
  async dbExecute(query, params, options) {
    throw new Error("dbExecute() must be implemented");
  }

  /** Crea una connessione ed inizia una nuova transazione */
  async getNewTransaction() {
    throw new Error("getNewTransaction() must be implemented");
  }

  async transactExecute(conn, sql, params) {
    throw new Error("transactExecute() must be implemented");
  }

  async commit(conn) {
    throw new Error("commit() must be implemented");
  }

  async rollback(conn) {
    throw new Error("rollback() must be implemented");
  }

  /** Rilascia nel modo nativo la connessione.
   * @param {*} conn 
   */
  async release(conn) {
    throw new Error("release() must be implemented");
  }

  /*async runInTransaction(callback) {
    const conn = await this.getNewTransaction();
    try {
      const result = await callback(conn);
      await this.commit(conn);
      return result;
    } catch (err) {
      await this.rollback(conn);
      throw err;
    } finally {
      await this.release(conn);
    }
  }*/

  defDbExecuteResponse = () => { return { rows: [], dbOperation: "UNKNOWN", success: false, rowsAffected: 0, error: null } };

  detectQueryOperation = (query) => {
    let toRet = "UNKNOWN";
    // Ricava l'operazione dalla query (prima parola, ignorando eventuali spazi o commenti)
    const match = query.trim().match(/^(INSERT|SELECT|UPDATE|DELETE)/i);
    if (match) {
      const operation = match[1].toUpperCase();
      switch (operation) {
        case 'INSERT':
        case 'SELECT':
        case 'UPDATE':
        case 'DELETE':
          toRet = operation;
          break;
        default:
          // opzionale, nel caso in cui voglia gestire altri casi
          break;
      }
    }
    return toRet;
  }
  formatDbParam = (name, index) => { throw new Error("formatDbParam() must be implemented"); }
  formatDbField = (name) => { throw new Error("formatDbField() must be implemented"); }
}
module.exports = DbAdapter;
/*function placeHolder(name, index) {
  switch (dbType) {
    case "maria":
      return "?";
    case "postgres":
      return `$${index}`;
    case "oracle":
    default:
      return `:${name}`;
  }
}
*/
/*
function getDbRegex() {
  switch (dbType) {
    case "maria":
      return /\?/g;
    case "postgres":
      return /\$\d+/g;
    case "oracle":
    default:
      return /:\w+/g;
  }
}
*/
/*
function getTableByDb(schema) {
  let tableName;
  switch (dbType) {
    case "postgres":
      tableName = `"${schema.schemaName}"."${schema.tableName}"`;
      break;
    case "maria":
      tableName = `${schema.tableName}`;
      break;
    case "oracle":
      tableName = `${schema.schemaName}.${schema.tableName}`;
      break;
  }
  return tableName;
}
*/
/*
function getAllFieldsByDb(schema) {
  const allFields = [...schema.keys, ...schema.fields];
  const fieldsStr = dbType == "postgres" ? allFields.join('","') : allFields.join(",");
  return dbType == "postgres" ? `"${fieldsStr}"` : fieldsStr;
}
*/
/*
function getFieldsByDb(schema) {
  const allFields = [...schema.fields];
  const fieldsStr = dbType == "postgres" ? allFields.join('","') : allFields.join(",");
  return dbType == "postgres" ? `"${fieldsStr}"` : fieldsStr;
}
*/
/*
function getInsertParamsByDb(schema) {
  const allFields = [...schema.keys, ...schema.fields];
  let i = 0;
  const paramsStr = allFields
    .map((field) => {
      i++;
      return `${placeHolder(field, i)}`;
    })
    .join(",");
  return paramsStr;
}
*/
/*
function getUpdateParamsByDb(schema) {
  let i = 0;
  const fields = schema.fields
    .map((field) => {
      i++;
      let dbField = dbType == "postgres" ? `"${field}"` : `${field}`;
      let param = placeHolder(field, i);
      return `${dbField}=${param}`;
    })
    .join(", ");
}
*/
/*
function getKeysByDb(schema) {
  let i = 0;
  const keysStr = schema.keys
    .map((key) => {
      i++;
      let dbKey = dbType == "postgres" ? `"${key}"` : `${key}`;
      let param = placeHolder(key, i);
      return `${dbKey}=${param}`;
    })
    .join(" AND ");
  return keysStr;
}
*/