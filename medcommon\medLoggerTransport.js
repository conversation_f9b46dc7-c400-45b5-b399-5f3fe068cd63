const Transport = require("winston-transport");
const util = require("util");
const http = require("http");

// Inherit from `winston-transport` so you can take advantage
// of the base feature and `.exceptions.handle()`.
module.exports = class MedLoggerTransport extends Transport {
  constructor(opts) {
    super(opts);
    this.options = opts.medlogger || {};
  }
  /*
  options = {
    host: "*************",
    port: 3210,
    path: "/logs",
    method: "PUT",
    headers: {},
  };
  */

  log(info, callback) {
    setImmediate(() => {
      this.emit("logged", info);
    });
    info.medType = this.levelToMedType(info.level);
    info.medLevel = this.typeToMedNumLevel(info.level);
    // Perform the writing to the remote service
    if (this.options && this.options.active && this.options.host) {
      this.httpPut(info)
        .then((response) => null)
        .catch((error) => null);
    }
    callback();
  }
  levelToMedType(level) {
    switch (level) {
      default:
        return level;
    }
  }
  typeToMedNumLevel(type) {
    switch (type) {
      case "error":
        return "100";
      case "warn":
        return "200";
      case "info":
        return "300";
      case "http":
        return "400"
      case "verbose":
        return "500";
      case "debug":
        return "600";
      case "silly":
        return "700";
      default:
        return 500;
    }
  }
  httpPut = (logItem) => {
    let options = this.options;
    options.headers["Content-Type"] = "application/x-www-form-urlencoded";
    const bodyLevel = `level=${encodeURI(logItem.medLevel)}`;
    const bodyType = `type=${encodeURI(logItem.medType)}`;
    const bodyMessage = `message=${encodeURI(logItem.message)}`;
    const bodyTime = `time=${encodeURI(logItem.timestamp)}`;
    const bodyAppName = `app_name=${encodeURI(options.application)}`;
    const xFormBody = `${bodyLevel}&${bodyType}&${bodyTime}&${bodyMessage}&${bodyAppName}`;
    options.headers["Content-Length"] = Buffer.byteLength(xFormBody);
    return new Promise((resolve, reject) => {
      var req = http
        .request(options, (res) => {
          res.setEncoding("utf8");
          let body = "";
          res.on("data", (chunk) => (body += chunk));
          res.on("end", () => resolve(body));
        })
        .on("error", reject);
      req.write(xFormBody);
      req.end();
    });
  };
};
