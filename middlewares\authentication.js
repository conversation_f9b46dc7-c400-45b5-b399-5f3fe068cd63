import axios from 'axios';
import logger from 'medcommon/logger';
import dotenv from 'dotenv';
import crypto from 'crypto';
dotenv.config();

const excludedPaths = ['login', 'logout', 'callback'];

export default {
  authenticationMiddleware: (req, res, next) => {
    if (!excludedPaths.some((path) => req.url.includes(path)) && !req.session.userid) {
      req.session.backUrl = req.url;
      return res.redirect('/login');
    }
    next();
  },
  //# region Nuovo sistema come da specifiche
  encryptPassword: (password, passphrase, salt) => {
    // hash password with sha512
    return crypto.createHash('sha512').update(salt).update(passphrase).update(password).digest('base64');
  },
  //#endregion
  axiosLoginROPC: async (username, password) => {
    let url = `${process.env.APP_OAUTH_URL}${process.env.APP_OAUTH_TOKEN_ENDPOINT}`;
    const data = new URLSearchParams({
      grant_type: 'password',
      username,
      password,
      client_id: process.env.APP_CLIENT_ID,
      client_secret: process.env.APP_CLIENT_SECRET,
      scope: process.env.APP_OAUTH_SCOPE,
    });
    const res = await axios.request({
      method: 'POST',
      url: url,
      data: data,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    return res.data; // { access_token, id_token, refresh_token, ... }
  },
  axiosAuthorize: async (username, password) => {
    let url = `${process.env.APP_OAUTH_URL}${process.env.APP_OAUTH_AUTHORIZE_ENDPOINT}`;
    logger.debug('url:%s', url);
    logger.debug('redirectUrl:%s', process.env.APP_OAUTH_REDIRECT_URL);
    // Invia la richiesta POST al nuovo URL
    let response;
    let data = new URLSearchParams({
      username: username,
      password: password,
      client_id: process.env.APP_CLIENT_ID,
      response_type: 'code',
      scope: process.env.APP_OAUTH_SCOPE,
      redirect_uri: process.env.APP_OAUTH_REDIRECT_URL,
    });
    try {
      response = await axios.request({
        method: 'POST',
        url: url,
        data: data,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
    } catch (error) {
      throw error.response.data;
    }
    return response.data;
  },
  axiosToken: async (authCode) => {
    let url = `${process.env.APP_OAUTH_URL}${process.env.APP_OAUTH_TOKEN_ENDPOINT}`;
    let redirectUrl = process.env.APP_OAUTH_REDIRECT_URL;
    let tokenResponse;
    let data = new URLSearchParams({
      code: authCode,
      client_id: process.env.APP_CLIENT_ID,
      client_secret: process.env.APP_CLIENT_SECRET,
      grant_type: 'authorization_code',
      scope: process.env.APP_OAUTH_SCOPE,
      redirect_uri: process.env.APP_OAUTH_REDIRECT_URL,
    });
    try {
      tokenResponse = await axios.request({
        method: 'POST',
        url: url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: data,
      });
    } catch (error) {
      throw error.response.data;
    }
    return tokenResponse.data;
  },
  axiosUserInfo: async (access_token) => {
    // Invia la richiesta POST al nuovo URL
    let response;
    try {
      response = await axios.request({
        method: 'GET',
        url: `${process.env.APP_OAUTH_URL}${process.env.APP_OAUTH_USERINFO_ENDPOINT}`,
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });
    } catch (error) {
      throw error.response.data;
    }
    return response.data;
  },
};
