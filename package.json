{"name": "backend", "version": "2.5.20", "type": "module", "main": "app.js", "scripts": {"start": "node app.js", "watch": "nodemon app.js", "css:build": "npx @tailwindcss/cli -i ./tailwind.css -o ./web/public/css/tailwind.css", "css:watch": "npx @tailwindcss/cli -i ./tailwind.css -o ./web/public/css/tailwind.css -w", "dev": "nodemon --exec \"npm run css:build && npm start\""}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "body-parser": "^1.19.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cruder": "file:./cruder", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.0.1", "joi": "^17.13.3", "medcommon": "file:./medcommon", "multer": "^2.0.2", "oauth2": "file:./oauth2", "socket.io": "^4.7.5", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@tailwindcss/cli": "^4.1.7", "autoprefixer": "^10.4.21", "nodemon": "^3.1.10", "postcss": "^8.5.3", "tailwindcss": "^4.1.7"}}