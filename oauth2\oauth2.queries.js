
module.exports.getUserSql = `SELECT vu.* FROM OAUTH2.v_oauth_users vu, utenti ut WHERE vu."id" = ut.id AND ut.username = :1 AND ut.userpassword = :2`;
module.exports.getUserFromTokenSql = `SELECT ut.* FROM V_OAUTH_USERS ut, access_tokens at WHERE ut."id" = at.user_id AND at.access_token = :1 and at.expires > current_timestamp`;
module.exports.getUserFromClientSql = `SELECT ut.* FROM V_OAUTH_USERS ut, clients cl WHERE ut."id" = cl.user_id AND cl.client_id = :1`;
module.exports.getUsersSql = `SELECT * FROM V_OAUTH_USERS ORDER BY UPPER(NOMINATIVO) ASC`;

module.exports.getAccessTokenSql = `SELECT * FROM access_tokens WHERE access_token=:1 and expires > current_timestamp`;
module.exports.insertAccessTokenSql = `INSERT INTO access_tokens (access_token, client_id, user_id, expires, scope) VALUES(:1,:2,:3,:4,:5)`;
module.exports.revokeAccessTokenSql = `DELETE FROM access_tokens WHERE access_token = :1`;

module.exports.getRefreshTokenSql = `SELECT * FROM refresh_tokens WHERE refresh_token=:1 and expires > current_timestamp`;
module.exports.revokeRefreshTokenSql = `DELETE FROM refresh_tokens WHERE refresh_token = :1`;
module.exports.insertRefreshTokenSql = `INSERT INTO refresh_tokens (refresh_token, client_id, user_id, expires, scope) VALUES(:1,:2,:3,:4,:5)`;

module.exports.getClientSql = `SELECT * FROM clients WHERE client_id=:1 AND (client_secret is null OR client_secret like :2) AND API_VER='2'`;
module.exports.getClientsSql = `SELECT * FROM V_OAUTH_CLIENTS ORDER BY UPPER(CLIENT_NAME)`;
module.exports.insertClientSql = `INSERT INTO CLIENTS (CLIENT_ID,CLIENT_NAME,CLIENT_SECRET,REDIRECT_URI,GRANT_TYPES,SCOPE,USER_ID,CLIENT_DESCRIPTION, API_VER) VALUES (:1,:2,:3,:4,:5,:6,:7,:8,'2')`;
module.exports.updateClientSql = `UPDATE CLIENTS SET CLIENT_NAME=:1, CLIENT_SECRET=:2, REDIRECT_URI=:3, GRANT_TYPES=:4, SCOPE=:5, USER_ID=:6, CLIENT_DESCRIPTION=:7 WHERE CLIENT_ID = :8 AND API_VER='2'`;
module.exports.deleleClientSql = `DELETE FROM clients WHERE client_id=:1`;
module.exports.getClientApiSql = `SELECT * FROM CLIENTS WHERE CLIENT_ID = :1 AND API_VER='2'`;

module.exports.getAuthorizationCodeSql = `SELECT * FROM authorization_codes WHERE authorization_code = :1`;
module.exports.saveAuthorizationCodeSql = `INSERT INTO authorization_codes (authorization_code, client_id, user_id, expires, scope, redirect_uri, challenge, challenge_method) VALUES(:1, :2, :3, :4, :5, :6, :7, :8)`;
module.exports.revokeAuthorizationCodeSql = `DELETE FROM authorization_codes WHERE authorization_code = :1`;

module.exports.getDeviceCodeSql = `SELECT * FROM device_codes WHERE device_code = :1 and expires > current_timestamp`;
module.exports.saveDeviceCodeSql = `INSERT INTO device_codes (device_code, user_id, expires) VALUES(:1, :2, :3)`;
module.exports.revokeDeviceCodeSql = `DELETE FROM device_codes WHERE device_code = :1`;

module.exports.isTokenValidSql = `SELECT COUNT(*) as isvalid FROM access_tokens WHERE access_token = :token AND expires > current_timestamp`;

module.exports.getUserSessionsSql = `SELECT cl.client_name, cl.client_description,at.expires FROM oauth2.access_tokens at, oauth2.clients cl, utenti ut where ut.id=:1 AND at.expires > current_timestamp and cl.client_id = at.client_id and ut.id = at.user_id`;

module.exports.insertAccessLogSql = `insert into ACCESS_LOGS(ENDPOINT, GRANT_TYPE, SOURCE_IP, SOURCE_HOST, SOURCE_AGENT, ACCESS_TOKEN, REFRESH_TOKEN) values (:1,:2,:3,:4,:5,:6,:7)`;

module.exports.getClientConfigsSql = `SELECT * FROM CLIENTS_CONFIGS WHERE CLIENT_ID=:1`;

module.exports.getClientsConfigsSql = `SELECT cc.*, cl.client_name, cl.client_description FROM CLIENTS_CONFIGS cc, CLIENTS cl WHERE cl.api_ver is not null and cl.client_id = cc.client_id ORDER BY client_name, config_name asc`;
module.exports.getClientSingleConfigsSql = `SELECT cc.*, cl.client_name, cl.client_description FROM CLIENTS_CONFIGS cc, CLIENTS cl WHERE cc.client_id=:1 and cl.api_ver is not null and cl.client_id = cc.client_id ORDER BY client_name, config_name asc`;

module.exports.getClientConfigApiSql = `SELECT cc.*, cl.client_name, cl.client_description FROM CLIENTS_CONFIGS cc, CLIENTS cl WHERE cl.api_ver is not null and cl.client_id = cc.client_id AND cc.CLIENT_ID=:1 AND cc.CONFIG_NAME=:2`;
module.exports.updateClientConfigApiSql = `UPDATE CLIENTS_CONFIGS SET CONFIG_NAME=:1, CONFIG_VALUE=:2, CONFIG_TYPE=:3 WHERE CLIENT_ID = :4 AND CONFIG_NAME = :5`;
module.exports.insertClientConfigApiSql = `INSERT INTO CLIENTS_CONFIGS (CLIENT_ID, CONFIG_NAME, CONFIG_VALUE, CONFIG_TYPE) VALUES(:1, :2, :3, :4)`;
module.exports.deleteClientConfigApiSql = `DELETE FROM CLIENTS_CONFIGS WHERE CLIENT_ID = :1 AND CONFIG_NAME = :2`;