<style>
  ::backdrop {
    background-color: rgba(0, 0, 0, 0.7);
  }
</style>

<dialog id="activationMapDialog"
  class="bg-[#1e1e1e] text-white rounded-lg shadow-lg w-[1000px] fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">

  <!-- Close Button -->
  <div class="flex flex-row justify-center p-4 pt-4">
    <div class="flex w-full text-5xl">
      <%=t('activationMaps.selectInitialData')%>
    </div>
    <button onclick="closeDialog()" class="text-lg text-white hover:text-gray-300">
      <i class="fa fa-times text-6xl"></i>
    </button>
  </div>

  <!-- Padded Content -->
  <div class="p-4 pt-2">
    <form method="post">
      <div class="grid grid-cols-2 gap-4">
        <!-- ECGs Panel -->
        <div class="bg-[#2e2e2e] p-4 rounded-md flex flex-col gap-2">
          <h3 class="text-center font-bold text-3xl">
            <%=t('ecg')%>
          </h3>
          <div class="overflow-y-auto h-[500px] flex flex-col gap-2">
            <% let index=0; %>
            <% resources.ecgList.forEach(function(ecg) { %>
              <% let currColor=ecg.resourceTag==='before' ? 'bg-green-500' : ecg.resourceTag==='after'
                ? 'bg-yellow-500' : 'bg-red-500' ; %>
                <label
                  class="flex items-center justify-between gap-2 w-full bg-[#3a3a3a] hover:bg-[#505050] text-white px-4 py-2 rounded-md transition-colors cursor-pointer text-2xl">
                  <div class="flex items-center gap-2">
                    <input type="radio" name="ecg" class="accent-black" value="<%= ecg.id %>" <%=index===0
                      ? 'required' : '' %>/>
                    <span>
                      <%= locDateTime(ecg.resourceDate) %>
                    </span>
                  </div>
                  <%- include('./badge-text', { badgeColor: currColor, badgeText: t('badge.' + ecg.resourceTag) }) %>
                </label>
            <% index++;}); %>
            </div>
            </div>

            <!-- TACs Panel -->
            <div class="bg-[#2e2e2e] p-4 rounded-md flex flex-col gap-2">
              <h3 class="text-center font-bold text-3xl">
                <%=t('ctScan')%>
              </h3>

              <div class="overflow-y-auto h-[500px] flex flex-col gap-2">
                <% index=0; %>
                <% resources.ctList.forEach(function(tac) { %>
                  <% let currColor=tac.resourceTag==='before' ? 'bg-green-500' : tac.resourceTag==='after'
                    ? 'bg-yellow-500' : 'bg-red-500' ; %>
                    <label
                      class="flex items-center justify-between gap-2 w-full bg-[#3a3a3a] hover:bg-[#505050] text-white px-4 py-2 rounded-md transition-colors cursor-pointer text-2xl">
                      <div class="flex items-center gap-2">
                        <input type="radio" name="ctScan" class="accent-black" value="<%= tac.id %>" <%=index===0
                          ? 'required' : '' %>/>
                        <span>
                          <%= locDateTime(tac.resourceDate) %>
                        </span>
                      </div>
                      <%- include('./badge-text', { badgeColor: currColor, badgeText: t('badge.' + tac.resourceTag) }) %>
                    </label>
                <% index++;}); %>
              </div>
        </div>
      </div>
      <!-- Notes input text -->
      <div class="flex flex-col">
        <label for="notes" class="block font-semibold text-on-darkgray pt-4 pb-2">
          <%= t("activationMaps.notes") %>
        </label>
        <input type="text" id="notes" name="notes" placeholder="<%= t("activationMaps.addNotes") %>" required
          class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light" />
      </div>
      <div class="flex justify-center">
        <button type="submit"
          class="mt-8 bg-darkred text-white p-4 rounded-md font-semibold hover:bg-darkred w-[50%] text-3xl">
          <%=t('activationMaps.confirmAdd')%>
        </button>
      </div>
    </form>
  </div>
</dialog>