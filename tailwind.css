@import "tailwindcss";

@font-face {
  font-family: 'Montserrat';
  src: url('/webfonts/Montserrat-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-display: block;
}

@theme {
  --color-darkgray: #333;
  --color-darkred: #326D7C;
  --color-darkgray-light: #2a2a2a;
  --color-darkgray-surface: #555;
  --color-darkred-light: #4698AD;
  --color-on-darkgray: #f5f5f5;
  --color-on-darkred: #ffffff;
  --color-error: #ef4444;
  --font-body: "Montserrat";
}

@layer utilities {
    .loading-spinner {
    width: 6rem;
    height: 6rem;
    border-width: 0.6rem;
    }
}

@layer base {
  * {
    font-family: "Montserrat";
  }
  
  button:not([disabled]),
  [role="button"]:not([disabled]) {
    cursor: pointer;
  }
  body {
    background-color: var(--color-zinc-800);
    color: var(--color-on-darkgray);
    height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: "Montserrat";
  }
  main {
    width: 100%;
  }
}
