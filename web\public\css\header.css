  /* Div principale */
  #header {
    width: 100%;
    height: 120px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    border: 1px solid #000; /* opzionale, per visualizzare il bordo */
    background-color:#252424fe;
  }

  /* Primo div interno alto 40px */
  #header-top {
    height: 40px;
    /*background-color: #f0f0f0;  esempio di colore */
    width: 100%;
    padding-top: 2px;
    padding-bottom: 2px;
    padding-left: 12px;
    padding-bottom: 12px;
  }

  /* Secondo div interno alto 80px */
  #header-bottom {
    height: 80px;
    /*background-color: #ccc;  esempio di colore */
    display: flex;
    flex-direction: row;
  }
  #header-bottom .box {
    height: 80px; /* altezza fissa */
  }
  
  #header-left {
    width: 120px;
    /*background-color: rgb(40, 43, 44);  colore di esempio */
  }
  
  #header-right {
    width: 140px;
    /*background-color: lightgreen;  colore di esempio */
    display: flex;
    justify-content: center; /* Centra gli elementi */
    gap: 8px; /* Opzionale, spazio tra gli span */
    align-items: center;
  }
  
  #header-center {
    flex: 1; /* prende lo spazio rimanente */
    padding-top: 10px;
    /*background-color: lightcoral;  colore di esempio */
  }