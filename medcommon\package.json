{"name": "medcommon", "version": "1.0.10", "description": "Features comuni come modulo indipendente.", "author": "<PERSON>", "homepage": "", "license": "ISC", "repository": "git+https://git.medarchiver.com/developers/medcommon.git", "private": true, "files": ["/config.js", "/medLoggerTransport.js", "/logger.js", "/oracle.js", "/medCrypto.js", "/medOAuth.js", "/dbManager.js", "/promise.js", "/promiseHandlers.js", "/crudServices.js", "/oracleSessionStore.js", "/utils.js", "/package.json", "README.md"], "exports": {"./config": "./config.js", "./config.js": "./config.js", "./logger": "./logger.js", "./logger.js": "./logger.js", "./oracle": "./oracle.js", "./oracle.js": "./oracle.js", "./medCrypto": "./medCrypto.js", "./medCrypto.js": "./medCrypto.js", "./medOAuth": "./medOAuth.js", "./medOAuth.js": "./medOAuth.js", "./dbManager": "./dbManager.js", "./dbManager.js": "./dbManager.js", "./promise": "./promise.js", "./promise.js": "./promise.js", "./promiseHandlers": "./promiseHandlers.js", "./promiseHandlers.js": "./promiseHandlers.js", "./crudServices": "./crudServices.js", "./crudServices.js": "./crudServices.js", "./oracleSessionStore": "./oracleSessionStore.js", "./oracleSessionStore.js": "./oracleSessionStore.js", "./utils.js": "./utils.js", "./utils": "./utils.js", "./package.json": "./package.json"}, "dependencies": {"@elastic/ecs-winston-format": "^1.5.2", "winston": "^3.8.1", "fs": "0.0.1-security", "dotenv": "^14.3.2", "dateformat": "^4.5.1", "moment-timezone": "^0.5.33", "path": "^0.12.7", "oracledb": "^5.4.0", "winston-daily-rotate-file": "^4.7.1"}}