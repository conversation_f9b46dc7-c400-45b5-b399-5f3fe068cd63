import logger from "medcommon/logger";
import SimulationModel from "../../api/models/simulation.js";
import VMapResourceModel from "../../api/models/view-map-resource.js";
import CommonUtils from "../../api/utils.js";

const utils = new CommonUtils();

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export default {
  getSimulations: async (req, res) => {
    const patientId = req.params.id;
    logger.debug("GET /%s/simulations", patientId);
    const resources = await new SimulationModel().findFree([`${patientId}`], "ID_PATIENT = :1", "", true);
    return res.render("simulations", {
      patient: res.locals.selectedPatient,
      patientId: patientId,
      list: resources,
    });
  },
  getSimulation: async (req, res) => {
    const patientId = req.params.id;
    const simId = req.query.simId;
    logger.debug("GET /%s/simulations", patientId);
    const simulation = await new SimulationModel().findFirst([simId], "ID=:1", "", true);
    const resultMapId = simulation.idMapResult;
    const mapResources = await new VMapResourceModel().findFree([`${resultMapId}`], "ID_MAP = :1", "", true);
    let resources = [];
    mapResources.forEach((item) => {
      const url = res.locals.getStorageUrl(item.idPatient, resultMapId, item.idResourceType, item.uniqueId);
      let toAdd = {
        type: item.idResourceTypeText,
        url: url,
      };
      resources.push(toAdd);
    });
    simulation.leads = JSON.parse(simulation.data);
    return res.render("simulation", {
      patientId: patientId,
      simulation: simulation,
      resources: JSON.stringify(resources),
    });
  },
  postSimulation: async (req, res) => {
    try {
      const simId = req.query.simId;
      const simulation = await new SimulationModel().findFirst([simId], "ID=:1", "", true);
      let currData = JSON.parse(simulation.data);
      if (!currData.STATS) {
        currData.STATS = req.body.STATS;
        simulation.data = JSON.stringify(currData);
        const simUpdate = await new SimulationModel(simulation).update();
      }
    } catch (error) {
      logger.error("Error on POST sim STATS:%O", error);
    }
    return res.status(204).end();
  },
  getNewSimulation: async (req, res) => {
    const patientId = req.params.id;
    const mapId = req.query.mapId;
    //TODO:PER MAGGIORE SICUREZZA PASSARE ANCHE ID PAZIENTE.
    const mapResources = await new VMapResourceModel().findFree([`${mapId}`], "ID_MAP = :1", "", true);
    let resources = [];
    mapResources.forEach((item) => {
      const url = res.locals.getStorageUrl(patientId, mapId, item.idResourceType, item.uniqueId);
      let toAdd = {
        type: item.idResourceTypeText,
        url: url,
      };
      resources.push(toAdd);
    });
    if (mapResources) {
      return res.render("simulation-new", {
        patientId: patientId,
        simData: {},
        mapId: mapId,
        resources: JSON.stringify(resources),
      });
    } else {
      return res.redirect("/404");
    }
  },
  postNewSimulation: async (req, res) => {
    const patientId = req.params.id;
    //Questo è l'id della mappa 'assessments' da cui prendere ecg.
    const mapId = req.query.mapId;
    const { notes, rv_ventricular, rv_point, lv_ventricular, lv_point } = req.body;
    const ventriculars = `${rv_ventricular},${lv_ventricular}`;
    //questo è il parametro querystring che verra appeso al post di richiesta nuova simulazione.
    const ppos = ventriculars.replaceAll(",", ";");
    const leadData = {
      RV: {
        axis: `${rv_point}`,
        ventricular: `${rv_ventricular}`,
      },
      LV: {
        axis: `${lv_point}`,
        ventricular: `${lv_ventricular}`,
      },
    };
    //#region creo i nuovi oggetti..
    //Stacco il nuovo id della nuova simulazione
    const newIdSim = await utils.getSequenceNextVal("S_SIMULATIONS_ID");
    //Stacco il nuovo id della mappa risultato che verrà generata e legata alla simulazione.
    const mapIdResult = await utils.getSequenceNextVal("S_MAPS_ID");
    //Creo i dati della nuova simulazione
    const simData = {
      id: newIdSim, //Il nuovo id della simulazione.
      idMap: mapId, //La mappa di origine
      idPatient: patientId,
      idMapResult: mapIdResult, //La mappa del risultato.
      status: -1,
      description: notes,
      createdAt: new Date(),
      data: JSON.stringify(leadData),
    };
    const simulationModel = new SimulationModel(simData);
    //Creao i nuovi dati della nuova mappa risultato.
    const resultMapData = {
      id: mapIdResult,
      description: notes,
      idPatient: patientId,
      status: -1,
      creationDate: new Date(),
      mapTag: "before",
      idUserCreate: 1,
      type: 1, // 1 <- tipo mappa risultato simulazione.
    };
    const resultMapModel = new MapModel(resultMapData);

    const newJobId1 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const jobData1 = {
      id: newJobId1,
      idPatient: patientId,
      idMap: mapId,
      jobStatus: 0,
      stage: "create_simulation",
      idEcg: newIdSim,
      createdAt: new Date(),
      jobData: JSON.stringify({
        idSim: newIdSim,
        ppol: ppos,
      }),
    };
    const job1Model = new JobSchedulerSocketModel(jobData1);

    await delay(2000); // Aspetta un tot.

    const newJobId2 = await utils.getSequenceNextVal("S_PROCESSING_JOBS_ID");
    const jobData2 = {
      id: newJobId2,
      idPatient: patientId,
      idMap: mapIdResult, // <- la mappa di destinazione.
      jobStatus: 0,
      stage: "simulation",
      idEcg: newIdSim,
      createdAt: new Date(),
      jobData: JSON.stringify({
        idSim: newIdSim,
        ppol: ppos,
      }),
    };
    const job2Model = new JobSchedulerSocketModel(jobData2);
    //#endregion
    let success = false;
    //La transazione la prendo da uno dei modelli, è indifferente.
    const transaction = await resultMapModel.createNewTransaction();
    try {
      success = (await simulationModel.save(transaction)).success;
      if (!success) throw Error("Error step save simulation");
      success = (await resultMapModel.save(transaction)).success;
      if (!success) throw Error("Error step save simulation");
      success = (await job1Model.save(transaction)).success;
      if (!success) throw Error("Error step save simulation");
      success = (await job2Model.save(transaction)).success;
      if (!success) throw Error("Error step save simulation");
      await resultMapModel.commitTransaction(transaction);
    } catch (error) {
      logger.error("Error on POST new simulation: %O", error);
      //Anche qua è indifferente il model che fà rollback o commit.
      await resultMapModel.rollbackTransaction(transaction);
    } finally {
      await resultMapModel.disposeTransaction(transaction);
    }
    return success ? res.status(204).end() : res.status(500).send({ error: "internal server error." }).end();
  },
};
