export class EcgData {
  constructor() {
    this.data = {};
    this.order = ["I", "V1", "II", "V2", "III", "V3", "AVR", "V4", "AVL", "V5", "AVF", "V6"];
    this.leads = ["I", "II", "III", "AVR", "AVL", "AVF", "V1", "V2", "V3", "V4", "V5", "V6"];
    this.leads.forEach((lead) => {
      this.data[lead] = { label: lead, values: [] };
    });
  }

  clear() {
    this.leads.forEach((lead) => {
      this.data[lead].values = [];
    });
  }

  addValue(lead, value) {
    if (this.data[lead]) {
      this.data[lead].values.push(value);
    } else {
      console.warn(`Lead "${lead}" non riconosciuto.`);
    }
  }

  setValues(lead, valuesArray) {
    if (this.data[lead]) {
      this.data[lead].values = valuesArray;
    }
  }

  getValues(lead) {
    return this.data[lead]?.values || [];
  }

  getAllLeads() {
    return this.leads.map((lead) => ({
      label: this.data[lead].label,
      values: this.data[lead].values,
    }));
  }

  fromCsvData(csvContent) {
    const lines = csvContent.trim().split("\n");
    console.log("Numero di righe:", lines.length);

    const headers = lines[0].split(",").map((h) => h.trim());
    console.log("Numero di derivazioni:", headers.length);

    const dataLines = lines.slice(1);
    //Pre-alloco gli array, per velocizzare il tutto.
    for (const key of Object.keys(this.data)) {
      this.data[key].values = new Array(dataLines.length);
    }
    for (let i = 0; i < dataLines.length; i++) {
      const values = dataLines[i].split(",");
      const index = parseInt(values[0], 10);
      for (let j = 1; j < headers.length; j++) {
        const header = headers[j];
        const lead = this.data[header];
        if (lead) {
          lead.values[index] = parseFloat(values[j]);
        }
      }
    }
  }

  toCsv() {
    // Creazione dell'intestazione
    const headers = ["Index", ...this.leads];
    const csvRows = [headers.join(",")];

    // Trova la lunghezza massima dei valori
    const maxLength = Math.max(...this.leads.map((lead) => this.data[lead].values.length));

    // Aggiunta dei dati
    for (let i = 0; i < maxLength; i++) {
      const row = [i]; // Inizia con l'indice
      this.leads.forEach((lead) => {
        const value = this.data[lead].values[i] !== undefined ? this.data[lead].values[i] : "";
        row.push(value);
      });
      csvRows.push(row.join(","));
    }
    // Restituisce il CSV come stringa
    return csvRows.join("\n");
  }

  fromEcgData(ecgContent) {
    let lines = ecgContent.trim().split("\n");
    console.log("Numero di righe:", lines.length);
    let numSamples = 0;
    if (lines[3].startsWith("SamplesCount")) {
      numSamples = Number(lines[3].split(" ")[1]);
      console.log("Numero di campioni:", numSamples);
      lines = lines.slice(4);
    }
    for (const key of Object.keys(this.data)) {
      this.data[key].values = new Array(numSamples);
    }
    for (let i = 0; i < 12; i++) {
      const subRows = lines.slice((numSamples + 1) * i);
      console.log("SottoRighe:", subRows[0]);
      const nomeLead = subRows[0].split(" ")[1];
      const subData = new Array(numSamples);
      for (var j = 0; j < numSamples; j++) {
        subData[j] = parseInt(subRows[j + 1], 10) / 1000;
      }
      this.setValues(nomeLead, subData);
    }
  }
}
export class EcgDrawer {
  constructor(canvas, options = {}) {
    if (!(canvas instanceof HTMLCanvasElement)) {
      throw new Error("Il parametro deve essere un elemento canvas valido.");
    }
    // Proprietà costanti
    this.canvas = canvas;
    this.ctx = canvas.getContext("2d");
    this.scalingFactor = 1; //0.955;
    this.pxPerMM = 1;
    this.dataOffsetIndex = 0;

    this.cols = options.cols || 2;
    this.rows = options.rows || 6;

    this.leadBoxHeightMM = options.leadHeightMm || 25; // Altezza per ogni traccia (in mm)
    this.leadBoxWidthMM = options.leadWidthMm || 125; // Larghezza per ogni traccia (in mm)
    this.paddingMM = options.leadsPaddingMm || 10; // Spazio tra tracce
    this.showSmallGrid = options.showMm || false;

    this.fineLineColor = options.gridFineColor || "#ffcccc";
    this.boldLineColor = options.gridBoldColor || "#cc0000";

    this.lineWidth = options.lineWidth || 1;
    this.strokeStyle = options.strokeStyle || "#cc0000";
    this.gridStrokeStyle = options.gridStrokeStyle || "#fff0f0";

    this.mmPerSecond = 25;
    this.mVPerMM = 10;

    this.samplesPerSecond = options.samplesPerSecond || 500;

    this.canvasWidth = options.canvasWidth || 1080;
    this.canvasHeight = options.canvasHeight || 900;

    this.ecgData = new EcgData();

    this.rulerStrokeStyle = options.rulerStrokeColor || "white";
    this.rulerFillStyle = options.rulerFillColor || "white";

    this.isDrawing = false;
    this.startX = 0;
    this.startY = 0;
    this.canvas.addEventListener("mousedown", (e) => this.startDrawing(e));
    this.canvas.addEventListener("mousemove", (e) => this.draw(e));
    this.canvas.addEventListener("mouseup", () => this.stopDrawing());
    this.canvas.addEventListener("mouseleave", () => this.stopDrawing());
    this.canvas.addEventListener("contextmenu", (e) => {
      e.preventDefault();
    });
  }

  startDrawing(e) {
    if (e.button === 0) {
      // Controlla se il tasto sinisto del mouse è premuto
      this.isDrawing = true;
      const rect = this.canvas.getBoundingClientRect();
      this.startX = e.clientX - rect.left;
      this.startY = e.clientY - rect.top;
    } else if (e.button === 2) {
      // Controlla se il tasto destro del mouse è premuto
      if (!this.subCanvas) {
        const newCanvas = document.createElement("canvas");
        newCanvas.className = "subCanvas";
        newCanvas.width = this.canvasWidth;
        newCanvas.height = 230;
        newCanvas.style.pointerEvents = "none"; // Per evitare interferenze con il canvas sottostante
        newCanvas.style.position = "absolute"; // Usa 'absolute' per sovrapporre
        newCanvas.style.zIndex = "1"; // Imposta lo z-index
        newCanvas.style.backgroundColor = this.gridStrokeStyle;
        document.body.appendChild(newCanvas);

        this.ctx2 = newCanvas.getContext("2d");
        this.subCanvas = newCanvas;
      }
    }
  }

  draw(e) {
    if (this.subCanvas) {
      //this.subCanvas.width = Math.abs(currentX - this.startX);
      //this.subCanvas.height = Math.abs(currentY - this.startY);
      const order = this.cols == 1 ? this.ecgData.leads : this.ecgData.order;
      const leadData = this.ecgData.data[order[10]].values;
      const zoom = 2;
      const leadBoxWidth = this.leadBoxWidthMM * this.pxPerMM * zoom;
      const leadBoxHeight = this.leadBoxHeightMM * this.pxPerMM * zoom;
      //const baseY = (this.paddingMM + (leadBoxHeight / 2)) * zoom;
      const baseY = leadBoxHeight;
      this.ctx2.lineCap = "butt";
      this.ctx2.fillStyle = this.gridStrokeStyle;
      this.ctx2.fillRect(0, 0, this.canvasWidth, this.subCanvas.height);
      this.drawGridX(this.ctx2, this.canvasWidth, this.subCanvas.height, 1, zoom);
      this.drawGridY(this.ctx2, this.canvasWidth, this.subCanvas.height, 1, zoom);
      this.drawSingleLead(this.ctx2, leadData, 0, baseY, leadBoxWidth * zoom, this.pixelsPerMV() * zoom, this.scalingFactor * zoom);
    }

    if (!this.isDrawing) return;

    const rect = this.canvas.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;

    // Pulisci il canvas
    //this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.redraw();

    // Disegna la prima linea verticale
    this.ctx.beginPath();
    this.ctx.moveTo(this.startX, 0); // Punto di partenza in alto
    this.ctx.lineTo(this.startX, this.canvas.height); // Punto in basso
    //this.ctx.strokeStyle = '#FFFF00'; // Colore della linea
    this.ctx.strokeStyle = this.rulerStrokeStyle;
    this.ctx.lineWidth = 0.75; // Spessore della linea
    this.ctx.stroke();

    // Disegna la seconda linea verticale
    this.ctx.beginPath();
    this.ctx.moveTo(currentX, 0); // Punto di partenza in alto
    this.ctx.lineTo(currentX, this.canvas.height); // Punto in basso
    this.ctx.stroke();

    // Disegna la prima linea orizzontale
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.startY); // Punto di partenza a sinistra
    this.ctx.lineTo(this.canvas.width, this.startY); // Punto a destra
    this.ctx.stroke();

    // Disegna la seconda linea orizzontale
    this.ctx.beginPath();
    this.ctx.moveTo(0, currentY); // Punto di partenza a sinistra
    this.ctx.lineTo(this.canvas.width, currentY); // Punto a destra
    this.ctx.stroke();
    this.ctx.strokeStyle = this.rulerStrokeStyle;

    // Calcola la distanza in pixel tra le due linee
    const distanceX = currentX - this.startX;
    const distanceY = currentY - this.startY;

    this.ctx.font = "20px Arial"; // Font del testo
    const seconds = Math.abs(distanceX / this.pxPerMM / (this.mmPerSecond * this.scalingFactor));
    const milliVolts = Math.abs(distanceY / this.pxPerMM / 10);
    this.ctx.textAlign = "left"; // Può essere "left", "center", "right"
    this.ctx.textBaseline = "top"
    const txtRow1 = (seconds * 1000).toFixed(1) + "ms";
    const txtRow2 = milliVolts.toFixed(2) + "mV";
    const txtRow3 = (60 / seconds).toFixed(0) + " BPM";
    this.ctx.fillText(txtRow1, 5, currentY);
    this.ctx.fillText(txtRow2, 5, currentY + 20);
    this.ctx.fillText(txtRow3, 5, currentY + 40);
  }

  stopDrawing() {
    this.isDrawing = false;
    if (this.subCanvas) {
      document.body.removeChild(this.subCanvas);
      this.subCanvas = undefined;
    }
    this.redraw();
  }

  pixelsPerMV() {
    return this.mVPerMM * this.pxPerMM;
  }

  /** Imposta nuove dimensioni e fà il redraw. */
  setCanvasDimensions(newWidth, newHeight) {
    this.canvasWidth = newWidth;
    this.canvasHeight = newHeight;
    console.log("New size: ", newWidth, newHeight);
    this.redraw();
  }

  setScalingFactor(newValue) {
    this.scalingFactor = newValue;
    this.redraw();
  }

  setOffsetIndex(newValue) {
    this.dataOffsetIndex = newValue;
    this.redraw();
  }

  setSmallGridVisible(newValue) {
    this.showSmallGrid = newValue;
    this.redraw();
  }

  setSamplesPerSecond(newValue) {
    this.samplesPerSecond = newValue;
  }

  setBoldColor(newValue) {
    this.boldLineColor = newValue;
    this.redraw();
  }
  setFineColor(newValue) {
    this.fineLineColor = newValue;
    this.redraw();
  }

  setGridStrokeStyle(newValue) {
    this.gridStrokeStyle = newValue;
    this.redraw();
  }

  setStrokeStyle(newValue) {
    this.strokeStyle = newValue;
    this.redraw();
  }

  setPaddingMM(newValue) {
    this.paddingMM = newValue;
    this.redraw();
  }

  getMaxDataOffset() {
    try {
      const numSamples = this.ecgData.data[this.ecgData.leads[0]].values.length;
      const numSeconds = numSamples / this.samplesPerSecond;
      this.computePxPerMM();
      const visibleSeconds = this.leadBoxWidthMM / this.mmPerSecond;
      const deltaSeconds = Math.round((Math.round(numSeconds) - visibleSeconds) * this.samplesPerSecond);
      console.log("numSamples & numSeconds:", numSamples, numSeconds, visibleSeconds, deltaSeconds);
      return deltaSeconds;
    } catch (error) {
      console.error("Error on getMaxDataOffset", error);
    }
    return 0;
  }

  computePxPerMM() {
    // Ottieni DPI fisico approssimato usando 1mm CSS
    const mmDiv = document.createElement("div");
    mmDiv.style.width = "1mm";
    mmDiv.style.position = "absolute";
    mmDiv.style.visibility = "hidden";
    document.body.appendChild(mmDiv);
    const mmSize = mmDiv.getBoundingClientRect().width;
    document.body.removeChild(mmDiv);
    this.pxPerMM = mmSize * this.scalingFactor;
  }

  redraw() {
    this.computePxPerMM();
    this.drawEcg();
  }

  setEcgData(newValue) {
    if (!(newValue instanceof EcgData)) {
      throw new Error("I dati devono essere classe EcgData.");
    }
    this.ecgData = newValue;
  }

  drawEcg() {
    // Imposta dimensioni canvas in pixel reali
    const dpi = window.devicePixelRatio || 1;
    this.canvas.width = this.canvasWidth * dpi;
    this.canvas.height = this.canvasHeight * dpi;
    this.ctx.scale(dpi, dpi);

    this.ctx.lineCap = "butt";
    this.ctx.fillStyle = this.gridStrokeStyle;
    this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

    // === Griglie === Se voglio vedere i millimetri 1 mm di default
    let step = this.showSmallGrid ? 1 : 5;
    this.drawGridX(this.ctx, this.canvasWidth, this.canvasHeight, step);
    this.drawGridY(this.ctx, this.canvasWidth, this.canvasHeight, step);

    this.drawSecondsLabels();

    this.drawAllLeads();

    this.ctx.fillText(`SR ${this.samplesPerSecond} Hz - ${this.mmPerSecond} mm/s - ${this.mVPerMM} mm/mV`, this.canvasWidth * 0.85, this.canvasHeight - 15);
  }

  drawSecondsLabels(zoom = 1) {
    let startSecond = Math.round(this.dataOffsetIndex / this.samplesPerSecond);

    this.ctx.lineWidth = 1.5;
    this.ctx.strokeStyle = this.strokeStyle; // || '#cc0000';
    this.ctx.fillStyle = this.strokeStyle; //'black';
    this.ctx.font = `${Math.round(4 * this.pxPerMM)}px Arial`;
    this.ctx.textAlign = "center";

    let yPosition = 4 * this.pxPerMM;

    //const mmPerSample = this.mmPerSecond / this.samplesPerSecond;
    //const pxPerSample = this.pxPerMM * mmPerSample * scaling;

    for (let x = this.paddingMM; x <= this.leadBoxWidthMM + this.paddingMM; x += this.mmPerSecond * this.scalingFactor) {
      this.ctx.fillText(`${startSecond}s`, x * this.pxPerMM, yPosition);
      if (this.cols == 2) {
        let secondX = x + this.paddingMM + this.leadBoxWidthMM;
        this.ctx.fillText(`${startSecond}s`, secondX * this.pxPerMM, yPosition);
      }
      startSecond++;
    }
  }

  drawGridX(ctx, width, height, mmStep, zoom = 1) {
    for (let x = 0; x <= width; x += this.pxPerMM * mmStep * zoom) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.strokeStyle = (Math.round(x / this.pxPerMM) * zoom) % 5 === 0 ? this.boldLineColor : this.fineLineColor;
      ctx.lineWidth = (Math.round(x / this.pxPerMM) * zoom) % 5 === 0 ? 1 : 0.5;
      ctx.stroke();
    }
  }

  drawGridY(ctx, width, height, mmStep, zoom = 1) {
    for (let y = 0; y <= height; y += this.pxPerMM * mmStep * zoom) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.strokeStyle = (Math.round(y / this.pxPerMM) * zoom) % 5 === 0 ? this.boldLineColor : this.fineLineColor;
      ctx.lineWidth = (Math.round(y / this.pxPerMM) * zoom) % 5 === 0 ? 1 : 0.5;
      ctx.stroke();
    }
  }
  drawAllLeads() {
    const leadBoxHeight = this.leadBoxHeightMM * this.pxPerMM;
    const leadBoxWidth = this.leadBoxWidthMM * this.pxPerMM;
    const paddingX = this.paddingMM * this.pxPerMM;
    const paddingY = this.paddingMM * this.pxPerMM;

    for (let i = 0; i < 12; i++) {
      const col = i % this.cols;
      const row = Math.floor(i / this.cols);

      const x = col * (leadBoxWidth + paddingX) + paddingX;
      const y = row * (leadBoxHeight + paddingY) + paddingY;

      const order = this.cols == 1 ? this.ecgData.leads : this.ecgData.order;

      const leadData = this.ecgData.data[order[i]].values;
      const leadLabel = this.ecgData.data[order[i]].label;

      const baseY = y + leadBoxHeight / 2;

      this.drawSingleLead(this.ctx, leadData, x, baseY, leadBoxWidth, this.pixelsPerMV(), this.scalingFactor);

      // Etichetta del lead
      this.ctx.font = `${Math.round(4 * this.pxPerMM)}px Arial`;
      //this.ctx.fillStyle = 'black';
      this.ctx.strokeStyle = this.strokeStyle; // || '#cc0000';
      this.ctx.fillStyle = this.strokeStyle; //'black';
      this.ctx.fillText(leadLabel, x - 4, baseY - 10);
    }
  }
  drawSingleLead(ctx, data, startX, startY, widthLimit, amplitude, scaling) {
    ctx.lineWidth = this.lineWidth;
    ctx.strokeStyle = this.strokeStyle;
    const mmPerSample = this.mmPerSecond / this.samplesPerSecond;
    const pxPerSample = this.pxPerMM * mmPerSample * scaling;
    ctx.beginPath();
    ctx.moveTo(startX, startY - data[0] * scaling);
    let x = startX;
    for (let i = 1 + this.dataOffsetIndex; i < data.length && x < startX + widthLimit; i++) {
      x += pxPerSample;
      const y = startY - data[i] * amplitude * scaling;
      ctx.lineTo(x, y);
    }
    ctx.stroke();
  }
}
