export class EcgData {
  constructor() {
    this.data = {};
    this.order = ["I", "V1", "II", "V2", "III", "V3", "AVR", "V4", "AVL", "V5", "AVF", "V6"];
    this.leads = ["I", "II", "III", "AVR", "AVL", "AVF", "V1", "V2", "V3", "V4", "V5", "V6"];
    this.leads.forEach((lead) => {
      this.data[lead] = { label: lead, values: [] };
    });
  }

  clear() {
    this.leads.forEach((lead) => {
      this.data[lead].values = [];
    });
  }

  addValue(lead, value) {
    if (this.data[lead]) {
      this.data[lead].values.push(value);
    } else {
      console.warn(`Lead "${lead}" non riconosciuto.`);
    }
  }

  setValues(lead, valuesArray) {
    if (this.data[lead]) {
      this.data[lead].values = valuesArray;
    }
  }

  getValues(lead) {
    return this.data[lead]?.values || [];
  }

  getAllLeads() {
    return this.leads.map((lead) => ({
      label: this.data[lead].label,
      values: this.data[lead].values,
    }));
  }

  fromCsvData(csvContent) {
    const lines = csvContent.trim().split("\n");
    console.log("Numero di righe:", lines.length);

    const headers = lines[0].split(",").map((h) => h.trim());
    console.log("Numero di derivazioni:", headers.length);

    const dataLines = lines.slice(1);
    //Pre-alloco gli array, per velocizzare il tutto.
    for (const key of Object.keys(this.data)) {
      this.data[key].values = new Array(dataLines.length);
    }
    for (let i = 0; i < dataLines.length; i++) {
      const values = dataLines[i].split(",");
      const index = parseInt(values[0], 10);
      for (let j = 1; j < headers.length; j++) {
        const header = headers[j];
        const lead = this.data[header];
        if (lead) {
          lead.values[index] = parseFloat(values[j]);
        }
      }
    }
  }
  fromEcgData(ecgContent) {
    let lines = ecgContent.trim().split("\n");
    console.log("Numero di righe:", lines.length);
    let numSamples = 0;
    if (lines[3].startsWith("SamplesCount")) {
      numSamples = Number(lines[3].split(" ")[1]);
      console.log("Numero di campioni:", numSamples);
      lines = lines.slice(4);
    }
    for (const key of Object.keys(this.data)) {
      this.data[key].values = new Array(numSamples);
    }
    for (let i = 0; i < 12; i++) {
      const subRows = lines.slice((numSamples + 1) * i);
      console.log("SottoRighe:", subRows[0]);
      const nomeLead = subRows[0].split(" ")[1];
      const subData = new Array(numSamples);
      for (var j = 0; j < numSamples; j++) {
        subData[j] = parseInt(subRows[j], 10) / 1000;
      }
      this.setValues(nomeLead, subData);
    }

    /*// Trova l'indice della riga che contiene il nome del lead
    const leadIndex = lines.findIndex(line => line.startsWith('*ECG'));
    if (leadIndex === -1) {
      console.error('Lead non trovato nel contenuto ECG.');
      return;
    }

    // Estrai i dati ECG a partire dalla riga successiva
    const dataLines = lines.slice(leadIndex + 1);
    const samplesCount = dataLines.length;
    console.log('Numero di campioni:', samplesCount);

    // Popola i valori per ogni lead
    for (let i = 0; i < samplesCount; i++) {
      const values = dataLines[i].split(','); // Supponendo che i valori siano separati da virgole
      this.leads.forEach((lead, index) => {
        const value = parseFloat(values[index]);
        if (!isNaN(value)) {
          this.data[lead].values[i] = value;
        }
      });
    }*/
  }
}
export class EcgDrawer {
  constructor(canvas, options = {}) {
    if (!(canvas instanceof HTMLCanvasElement)) {
      throw new Error("Il parametro deve essere un elemento canvas valido.");
    }
    // Proprietà costanti
    this.canvas = canvas;
    this.ctx = canvas.getContext("2d");
    this.scalingFactor = 0.955;
    this.pxPerMM = 1;
    this.dataOffsetIndex = 0;

    this.cols = options.cols || 2;
    this.rows = options.rows || 6;

    this.leadBoxHeightMM = options.leadHeightMm || 25; // Altezza per ogni traccia (in mm)
    this.leadBoxWidthMM = options.leadWidthMm || 125; // Larghezza per ogni traccia (in mm)
    this.paddingMM = options.leadsPaddingMm || 10; // Spazio tra tracce
    this.showSmallGrid = options.showMm || false;

    this.fineLineColor = options.gridFineColor || "#ffcccc";
    this.boldLineColor = options.gridBoldColor || "#cc0000";

    this.lineWidth = options.lineWidth || 1;
    this.strokeStyle = options.strokeStyle || "#cc0000";
    this.gridStrokeStyle = options.gridStrokeStyle || "#fff0f0";

    this.mmPerSecond = 25;
    this.samplesPerSecond = options.samplesPerSecond || 500;

    this.canvasWidth = options.canvasWidth || 1080;
    this.canvasHeight = options.canvasHeight || 900;

    this.ecgData = new EcgData();
  }

  /** Imposta nuove dimensioni e fà il redraw. */
  setCanvasDimensions(newWidth, newHeight) {
    this.canvasWidth = newWidth;
    this.canvasHeight = newHeight;
    console.log("New size: ", newWidth, newHeight);
    this.redraw();
  }

  setScalingFactor(newValue) {
    this.scalingFactor = newValue;
  }

  setOffsetIndex(newValue) {
    this.dataOffsetIndex = newValue;
    this.redraw();
  }

  setSmallGridVisible(newValue) {
    this.showSmallGrid = newValue;
  }

  setSamplesPerSecond(newValue) {
    this.samplesPerSecond = newValue;
  }

  setBoldColor(newValue) {
    this.boldLineColor = newValue;
    this.redraw();
  }
  setFineColor(newValue) {
    this.fineLineColor = newValue;
    this.redraw();
  }

  setGridStrokeStyle(newValue) {
    this.gridStrokeStyle = newValue;
    this.redraw();
  }

  setStrokeStyle(newValue) {
    this.strokeStyle = newValue;
    this.redraw();
  }
  getMaxDataOffset() {
    //this.leadBoxWidthMM -> dimensione in mm disegno di 1 lead
    //this.pxPerMM -> pixels da disegnare per 1mm reale.
    //const mmPerSecond = 25; -> quanti mm disegnare per ogni secondo di dati.
    //const samplesPerSecond = 500; -> il numero di campioni per secondo.
    //Secondi attuali con i dati caricati:
    try {
      const numSamples = this.ecgData.data[this.ecgData.leads[0]].values.length;
      const numSeconds = numSamples / this.samplesPerSecond;
      console.log("numSamples & numSeconds:", numSamples, numSeconds);
      return numSamples;
    } catch (error) {
      console.error("Error on getMaxDataOffset", error);
    }
    return 0;
  }

  computePxPerMM() {
    // Ottieni DPI fisico approssimato usando 1mm CSS
    const mmDiv = document.createElement("div");
    mmDiv.style.width = "1mm";
    mmDiv.style.position = "absolute";
    mmDiv.style.visibility = "hidden";
    document.body.appendChild(mmDiv);
    const mmSize = mmDiv.getBoundingClientRect().width;
    document.body.removeChild(mmDiv);
    this.pxPerMM = mmSize * this.scalingFactor;
  }

  redraw() {
    this.computePxPerMM();
    this.drawEcg();
  }

  setEcgData(newValue) {
    if (!(newValue instanceof EcgData)) {
      throw new Error("I dati devono essere classe EcgData.");
    }
    this.ecgData = newValue;
  }

  drawEcg() {
    // Imposta dimensioni canvas in pixel reali
    const dpi = window.devicePixelRatio || 1;
    this.canvas.width = this.canvasWidth * dpi;
    this.canvas.height = this.canvasHeight * dpi;
    this.ctx.scale(dpi, dpi);

    // Colori e stili
    const fineLineColor = this.fineLineColor; //'#ffcccc';
    const boldLineColor = this.boldLineColor; //'#cc0000';
    this.ctx.lineCap = "butt";

    // Sfondo rosa pallido
    this.ctx.fillStyle = this.gridStrokeStyle;
    this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

    //Se voglio vedere i millimetri 1 mm di default
    let step = this.showSmallGrid ? 1 : 5;

    // === Griglia ===
    for (let x = 0; x <= this.canvasWidth; x += this.pxPerMM * step) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, 0);
      this.ctx.lineTo(x, this.canvasHeight);
      this.ctx.strokeStyle = Math.round(x / this.pxPerMM) % 5 === 0 ? boldLineColor : fineLineColor;
      this.ctx.lineWidth = Math.round(x / this.pxPerMM) % 5 === 0 ? 1 : 0.5;
      this.ctx.stroke();
    }

    for (let y = 0; y <= this.canvasHeight; y += this.pxPerMM * step) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);
      this.ctx.lineTo(this.canvasWidth, y);
      this.ctx.strokeStyle = Math.round(y / this.pxPerMM) % 5 === 0 ? boldLineColor : fineLineColor;
      this.ctx.lineWidth = Math.round(y / this.pxPerMM) % 5 === 0 ? 1 : 0.5;
      this.ctx.stroke();
    }
    let startSecond = Math.round(this.dataOffsetIndex / this.samplesPerSecond);

    this.ctx.lineWidth = 1.5;
    this.ctx.strokeStyle = this.gridStrokeStyle || "#cc0000";
    this.ctx.font = `${Math.round(4 * this.pxPerMM)}px Arial`;
    this.ctx.textAlign = "center";
    this.ctx.fillStyle = this.strokeStyle; //'black';

    let yPosition = 4 * this.pxPerMM;

    for (let x = this.paddingMM; x <= this.leadBoxWidthMM + this.paddingMM; x += this.mmPerSecond) {
      this.ctx.fillText(`${startSecond}s`, x * this.pxPerMM, yPosition);
      if (this.cols == 2) {
        let secondX = x + this.paddingMM + this.leadBoxWidthMM;
        this.ctx.fillText(`${startSecond}s`, secondX * this.pxPerMM, yPosition);
      }
      startSecond++;
    }

    this.drawAllLeads();
  }
  drawAllLeads() {
    const leadBoxHeight = this.leadBoxHeightMM * this.pxPerMM;
    const leadBoxWidth = this.leadBoxWidthMM * this.pxPerMM;
    const padding = this.paddingMM * this.pxPerMM;

    this.ctx.lineWidth = this.lineWidth;
    this.ctx.strokeStyle = this.strokeStyle;
    this.ctx.font = `${Math.round(4 * this.pxPerMM)}px Arial`;
    this.ctx.fillStyle = this.strokeStyle; //'black';

    for (let i = 0; i < 12; i++) {
      const col = i % this.cols;
      const row = Math.floor(i / this.cols);

      const x = col * (leadBoxWidth + padding) + padding;
      const y = row * (leadBoxHeight + padding) + padding;

      const order = this.cols == 1 ? this.ecgData.leads : this.ecgData.order;

      const leadData = this.ecgData.data[order[i]].values;
      const leadLabel = this.ecgData.data[order[i]].label;

      const baseY = y + leadBoxHeight / 2;

      this.drawSingleLead(leadData, x, baseY, leadBoxWidth);

      // Etichetta del lead
      this.ctx.fillText(leadLabel, x - 4, baseY - 30);
    }
  }
  drawSingleLead(data, startX, baseY, widthLimit) {
    const mmPerSample = this.mmPerSecond / this.samplesPerSecond;
    const pxPerSample = this.pxPerMM * mmPerSample;
    //Parametri per la gestione di valori ADC invece di mv.
    //const mvPerUnit = 1 / 127; // ogni unità corrisponde a 1/127 mV
    //const pixelsPerMV = 10 * pxPerMM; // 10 mm = 1 mV
    //const amplitudeScale = pixelsPerMV * mvPerUnit;
    //ora con valori mv:
    const pixelsPerMV = 10 * this.pxPerMM; // 1 mV = 10 mm
    const amplitudeScale = pixelsPerMV; // ogni mV corrisponde a pixelsPerMV pixel
    this.ctx.beginPath();
    this.ctx.moveTo(startX, baseY - data[0] * this.scalingFactor);
    let x = startX;
    for (let i = 1 + this.dataOffsetIndex; i < data.length && x < startX + widthLimit; i++) {
      x += pxPerSample;
      const y = baseY - data[i] * amplitudeScale * this.scalingFactor;
      this.ctx.lineTo(x, y);
    }
    this.ctx.stroke();
  }
}
