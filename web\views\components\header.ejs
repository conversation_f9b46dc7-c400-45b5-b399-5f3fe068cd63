<% const isBreadcumbVisible = typeof invisibleBreadcrumb == 'undefined' || !invisibleBreadcrumb; %>
<div class="flex flex-row items-center gap-x-2.5 w-full bg-darkred text-white">
  <div class="flex items-center justify-center bg-darkgray text-center">
    <img src="/logo.png" alt="Logo" class="w-[350px] select-none" />
  </div>
  <div class="h-20 w-full flex flex-col justify-center">
    <div class="flex flex-row items-center gap-x-2">
      <% if (title !== '' || icon !== '') { %> <% if (icon !== '') { %>
      <i class="<%= icon %> text-4xl px-4"></i>
      <% } %> <% if (title !== '') { %>
      <h1 class="text-5xl flex-grow"><%= title %></h1>
      <% } %> <% } %>
      <div class="flex items-center w-20 h-20 text-center ml-auto">
        <!-- Power Icon Button -->
        <button id="powerMenuBtn" class="w-full h-full flex justify-center items-center px-8" title="Power Menu">
          <i class="fa-solid fa-power-off text-5xl text-white"></i>
        </button>
        <!-- Dropdown Menu -->
        <div id="powerMenu" class="bg-darkgray text-white rounded-lg shadow-lg fixed top-6 right-6 transform w-[300px] hidden z-50">
          <div class="p-4 flex flex-col gap-4">
            <button
              class="w-full text-lg p-3 bg-darkgray-surface text-on-darkred rounded-lg transition flex items-center justify-start gap-4 disabled:opacity-60 disabled:cursor-not-allowed"
            >
              <i class="fa-solid fa-lock text-2xl"></i>
              <%= t("lock_workstation") %>
            </button>
            <button
              id="logoutBtn"
              class="w-full text-lg p-3 bg-darkgray-surface text-on-darkred rounded-lg transition flex items-center justify-start gap-4 disabled:opacity-60 disabled:cursor-not-allowed"
            >
              <i class="fa-solid fa-right-from-bracket text-2xl"></i>
              <%= t("logout_user") %>
            </button>
            <button
              class="w-full text-lg p-3 bg-darkgray-surface text-on-darkred rounded-lg transition flex items-center justify-start gap-4 disabled:opacity-60 disabled:cursor-not-allowed"
            >
              <i class="fa-solid fa-plug-circle-xmark text-2xl"></i>
              <%= t("shutdown") %>
            </button>
          </div>
        </div>
      </div>
    </div>
    <% if (isBreadcumbVisible) { %> <%- include('header-breadcrumbs') %> <% } %>
  </div>
</div>
<% if (selectedPatient && selectedPatient.id) { %>
<div class="flex items-left w-screen m-4"><%- include('header-patient') %></div>
<% } %>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const powerMenuBtn = document.getElementById("powerMenuBtn");
    const powerMenu = document.getElementById("powerMenu");

    powerMenuBtn.addEventListener("click", (event) => {
      powerMenu.classList.toggle("hidden");
    });

    document.addEventListener("click", (event) => {
      if (!powerMenu.contains(event.target) && !powerMenuBtn.contains(event.target)) {
        powerMenu.classList.add("hidden");
      }
    });

    const logoutBtn = document.getElementById("logoutBtn");
    logoutBtn.addEventListener("click", () => {
      window.location.href = "/logout";
    });
  });
</script>
