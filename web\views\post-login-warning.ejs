<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <%- include('components/head') %>
  </head>
  <body class="h-screen m-0 bg-darkgray text-on-darkgray flex items-center justify-center px-4">
    <div class="w-full max-w-4xl bg-darkgray-light p-8 rounded-lg shadow-2xl">
      <h1 class="text-4xl font-bold mb-12 text-center"><%= t("postLoginWarning.heading", version) %></h1>
      <h2 class="text-3xl mb-12"><%- t("postLoginWarning.message", t("confirm")) %></h2>
      <!-- row accpet or cancel -->
        <div class="flex flex-row justify-center gap-4">
          <button
            onclick="navigateTo('/')"
            class="w-full text-lg p-3 bg-darkred hover:bg-darkred-light text-on-darkred rounded-lg transition flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed"
          >
            <%= t("confirm") %>
          </button>
          <button
            onclick="navigateTo('/login')"
            class="w-full text-lg p-3 bg-darkred hover:bg-darkred-light text-on-darkred rounded-lg transition flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed"
          >
            <%= t("cancel") %>
          </button>
        </div>
    </div>
    <script>
      function navigateTo(link) {
        window.location.href = link;
      }
    </script>
  </body>
</html>
