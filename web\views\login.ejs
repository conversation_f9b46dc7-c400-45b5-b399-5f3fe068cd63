<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%= t("login.heading") %></title>
    <link href="/css/tailwind.css" rel="stylesheet" />
  </head>
  <body class="h-screen m-0 bg-darkgray text-on-darkgray flex items-center justify-center px-4">
    <div class="w-full max-w-md bg-darkgray-light p-8 rounded-lg shadow-2xl">
      <h2 class="text-2xl font-semibold text-center mb-6"><%= t("login.heading") %></h2>
      <form id="loginForm" class="space-y-4" method="POST">
        <div>
          <label for="username" class="block mb-2"><%= t("login.username_label") %></label>
          <input class="w-full text-lg p-3 bg-darkgray text-on-darkgray border border-[#444] rounded-lg focus:outline-none focus:ring-2 focus:ring-darkred-light transition" 
          type="text" id="username" name="username" value="<%=username%>" required autofocus/>
        </div>
        <div>
          <label for="password" class="block mb-2"><%= t("login.password_label") %></label>
          <input class="w-full text-lg p-3 bg-darkgray text-on-darkgray border border-[#444] rounded-lg focus:outline-none focus:ring-2 focus:ring-darkred-light transition"
            type="password" id="password" name="password" required/>
        </div>
        <button class="w-full text-lg p-3 bg-darkred hover:bg-darkred-light text-on-darkred rounded-lg transition flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed"
          id="loginButton" type="submit">
          <svg id="loadingSpinner" class="hidden animate-spin h-5 w-5 text-on-darkred" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
          </svg>
          <span id="loginText"><%= t("login.button") %></span>
        </button>
      </form>
      <% if (typeof errors !== 'undefined' && errors && errors.length) { %>
        <% for (let i = 0; i < errors.length; i++) { %>
          <div class="errorDiv mt-4 text-error text-center"><%= t(errors[i]) %></div>
        <% } %>
      <% } %>
      <div class="mt-4 text-center">v <%= version %></div>
    </div>
    <script>
      const loginForm = document.getElementById('loginForm');
      const loginButton = document.getElementById('loginButton');
      const loadingSpinner = document.getElementById('loadingSpinner');
      const loginText = document.getElementById('loginText');
      const loginError = document.getElementById('loginError');
      loginForm.addEventListener('submit', async function (e) {
        loginError.classList.add('hidden');
        loginButton.disabled = true;
        loadingSpinner.classList.remove('hidden');
        loginText.textContent = '';
      });

      // set custom validity
      const usernameInput = document.getElementById('username');
      const passwordInput = document.getElementById('password');
    </script>
  </body>
</html>
