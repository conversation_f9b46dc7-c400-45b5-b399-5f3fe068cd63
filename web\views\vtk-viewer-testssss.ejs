<!DOCTYPE html>
<head></head>
<body>
  <canvas id="vtk-3d-canvas" width="800" height="600" style="width: 800px; height: 600px;"></canvas>
  <script src="/js/vtk-viewer.js" type="module"></script>
  <script>
    const canvas = document.getElementById("vtk-3d-canvas");
    window.onload = async () => {
      window.viewer = new window.vtkModule.GeometryViewer();
      console.log('Viewer creato');
      await initViewer();
    };

    async function initViewer() {
      const viewer = window.viewer;
      console.log('viewetInit->initialize');
      await viewer.initialize();
      console.log('viewetInit->start');
      await viewer.start();
      // Run both fetch operations in parallel
      await Promise.all([loadVTUFile('<%=vtuModel%>'), loadPLYFile('<%=plyModel%>')]);
      console.log('viewetInit->models loaded');
      // Set color for the VTU model
      //document.getElementById('loadingScreen').classList.add('hidden');
      //const vtkContainer = document.getElementById('vtk-3d-canvas').classList;
      //console.log('viewetInit->vtkContainer', vtkContainer);
      //vtkContainer.remove('hidden');
      await viewer.resetView(0.9);
      await viewer.render();
    }

    const loadVTUFile = async (vtuModel) => {
      const response = await fetch(vtuModel);
      const arrayBuffer = await response.arrayBuffer();
      const size = arrayBuffer.byteLength;
      const ptr = vtkModule._malloc(size);
      vtkModule.HEAPU8.set(new Uint8Array(arrayBuffer), ptr);
      await viewer.loadHeartModelFromMemory(ptr, size);
      vtkModule._free(ptr);
    };

    const loadPLYFile = async (plyModel) => {
      const plyResponse = await fetch(plyModel);
      const plyArrayBuffer = await plyResponse.arrayBuffer();
      // Allocate memory for PLY
      const plySize = plyArrayBuffer.byteLength;
      const plyPtr = vtkModule._malloc(plySize);
      vtkModule.HEAPU8.set(new Uint8Array(plyArrayBuffer), plyPtr);
      // Load the PLY data into the viewer with a different actor ID
      //await viewer.loadCoronaryFileFromMemory('model.ply', plyPtr, plySize);
      await viewer.loadCoronaryModelFromMemory(plyModel, plyPtr, plySize);
      vtkModule._free(plyPtr);
    };
  </script>
</body>
