const logger = require("medcommon/logger");

const { getDbAdapter } = require("./db-modules");

class AbstractDbCrudModel {
  #db;
  #dbType;
  #schema;
  #mapper = [];
  constructor(data = {}, dbType) {
    this.#dbType = dbType || process.env.DEFAULT_DB;
    this.#db = getDbAdapter(this.#dbType);
    this.#schema = this.constructor.schema;
    for (const [key, prop] of Object.entries(this.#schema.mapping)) {
      if (data[prop] !== undefined) {
        this[prop] = data[prop];
      } else {
        this[prop] = getLowerKeyValue(data, [key]);
      }
      this.#mapper[prop] = key;
    }
    //logger.silly("MAPPER MAPPER MAPPER MAPPER MAPPER %O", this.#mapper);
    this.#db.init();
  }

  //#region CRUD Operations
  /** Cerca il record con i valori delle chiavi fornite e ritorna un istanza della classe da cui
   * derivo. 
   * NO TRY/CATCH
   * @param {*} keys
   * @returns
   */
  async findByKeys(keys = []) {
    if (keys.length !== this.#schema.keys.length) {
      throw new Error("Il numero di chiavi non corrisponde allo schema.");
    }
    const sql = this.#selectSql();
    logger.silly("findByKeys sql:\n%s", sql);
    const result = await this.#db.dbExecute(sql, keys);
    logger.info("this.#db.dbExecute result:%O", result);
    if (result.rows.length === 1) {
      logger.silly("result row:\n%O", result.rows[0]);
      return new this.constructor(result.rows[0], this.#dbType);
    }
    return false;
  }
  /**
   * where sono elenco parametri con o senza i due punti SENZA WHERE davanti,
   * es. NAME like :search AND IS_ACTIVE = 'Y'
   * order i campi con il tipo ordinamento:
   * es. CREATED_AT ASC
   * Il vantaggio di questo approccio è l'utilizzo dei parametri e non di query cablata,
   * che dal punto di vista del piano di esecuzione delle queries questa query viene salvata
   * in hash e rimane preparata, i successivi utilizzi pertanto risultano + veloci rispetto ad
   * una query con i parametri like = 'valorefisso' ...
   *
   * @param {*} values
   * @param {*} where
   * @param {*} order
   * @param {bool} json -> invece dell'oggetto ritorna oggetto json (values)
   * @returns
   * NO TRY/CATCH RETHROW ECCEZIONE
   */
  async findFree(values = [], where = "", order = "", jsonFormat = false) {
    const toRet = [];
    let sql = this.#selectAllSql();
    if (where) {
      // Trova tutti i parametri nel where (esempio: :campo1, :campo2)
      const paramRegex = this.#db.dbParamRegex();
      const paramsInWhere = where.match(paramRegex) || [];
      // Rimuovi duplicati nel caso ci siano parametri ripetuti
      const uniqueParams = [...new Set(paramsInWhere)];
      if (uniqueParams.length !== values.length) {
        throw new Error(`Numero di parametri nel WHERE (${paramsInWhere.length}) non corrisponde alla lunghezza di values (${values.length})`);
      }
      sql += ` WHERE ${where}`;
    }
    if (order) {
      sql += ` ORDER BY ${order}`;
    }
    //logger.silly("findFree sql %s", sql);
    const result = await this.#db.dbExecute(sql, values);
    //logger.info("this.#db.dbExecute result:%O", result);
    if (result.rows.length > 0) {
      result.rows.forEach((row) => {
        if (jsonFormat) {
          toRet.push(new this.constructor(row, this.#dbType).values());
        } else {
          toRet.push(new this.constructor(row, this.#dbType));
        }
      });
    }
    return toRet;
  }

  /** Recupera TUTTE le righe senza filtri ne ordinamenti
   *  @returns [] array di questo classe
   *  NO TRY/CATCH RETHROW ECCEZIONE
   */
  async getAll(jsonFormat = false) {
    const toRet = [];
    const selAllSql = this.#selectAllSql();
    const result = await this.#db.dbExecute(selAllSql);
    if (result.rows.length > 0) {
      result.rows.forEach((row) => {
        if (jsonFormat) {
          toRet.push(new this.constructor(row, this.#dbType).values());
        } else {
          toRet.push(new this.constructor(row, this.#dbType));
        }
      });
    }
    return toRet;
  }
  async save(conn) {
    const sql = this.#insertSql();
    //logger.silly("save sql:%s", sql);
    const values = [...this.#keyValues(), ...this.#fieldValues()];
    //logger.silly("save values:%O", values);
    let result;
    if (conn) {
      result = await this.#db.transactExecute(conn, sql, values);
    } else {
      result = await this.#db.dbExecute(sql, values);
    }
    //logger.info("this.#db.dbExecute result:%O", result);
    return result;
  }
  async update(conn) {
    const sql = this.#updateSql();
    //logger.silly("update sql:%s", sql);
    const values = [...this.#fieldValues(), ...this.#keyValues()];
    //logger.silly("update values:%O", values);
    let result;
    if (conn) {
      result = await this.#db.transactExecute(conn, sql, values);
    } else {
      result = await this.#db.dbExecute(sql, values);
    }
    //logger.info("this.#db.dbExecute result:%O", result);
    return result;
  }
  async delete(keys = [], conn) {
    if (keys.length !== this.#schema.keys.length) {
      throw new Error("Il numero di chiavi non corrisponde allo schema.");
    }
    const sql = this.#deleteSql();
    let result;
    if (conn) {
      result = await this.#db.transactExecute(conn, sql, keys);
    } else {
      result = await this.#db.dbExecute(sql, keys);
    }
    //logger.info("this.#db.dbExecute result:%O", result);
    return result;
  }
  //#endregion

  //#region PRIVATE queries & values builder
  #selectSql() {
    const fieldsStr = this.#db.formatAllFieldsNames(this.#schema);
    const keysStr = this.#db.formatWhereParams(this.#schema);
    const tableName = this.#db.formatTableName(this.#schema);
    return `SELECT ${fieldsStr} FROM ${tableName} WHERE ${keysStr}`;
  }
  #selectAllSql() {
    const fieldsStr = this.#db.formatAllFieldsNames(this.#schema);
    const tableName = this.#db.formatTableName(this.#schema);
    return `SELECT ${fieldsStr} FROM ${tableName}`;
  }
  #insertSql() {
    const fieldsParams = this.#db.formatAllFieldsParams(this.#schema);
    const fieldsNames = this.#db.formatAllFieldsNames(this.#schema);
    const tableName = this.#db.formatTableName(this.#schema);
    return `INSERT INTO ${tableName} (${fieldsNames}) VALUES (${fieldsParams})`;
  }
  #updateSql() {
    const updateStr = this.#db.formatUpdateParams(this.#schema);
    const keysStr = this.#db.formatWhereParams(this.#schema, this.#schema.fields.length);
    const tableName = this.#db.formatTableName(this.#schema);
    return `UPDATE ${tableName} SET ${updateStr} WHERE ${keysStr}`;
  }
  #deleteSql() {
    const keysStr = this.#db.formatWhereParams(this.#schema);
    const tableName = this.#db.formatTableName(this.#schema);
    return `DELETE FROM ${tableName} WHERE ${keysStr}`;
  }
  #keyValues() {
    const result = [];
    const schema = this.constructor.schema;
    schema.keys.forEach((key) => {
      if (this.hasOwnProperty(schema.mapping[key])) {
        result.push(this[schema.mapping[key]]);
      }
    });
    return result;
  }
  #fieldValues() {
    const result = [];
    const schema = this.constructor.schema;
    schema.fields.forEach((field) => {
      if (this.hasOwnProperty(schema.mapping[field])) {
        result.push(this[schema.mapping[field]]);
      }
    });
    return result;
  }
  //#endregion

  //#region PUBLIC
  //Json con nomi campi db e valori da prop
  jsonValues = () => {
    let toRet = {};
    for (const [key, prop] of Object.entries(this.#schema.mapping)) {
      toRet[key] = this[prop];
    }
    logger.debug("get jsonValues:%O", toRet);
    return toRet;
  };
  //Valore delle solo proprietà (per i forms, etc)
  values = () => {
    let toRet = {};
    for (const [key, prop] of Object.entries(this.#schema.mapping)) {
      toRet[prop] = this[prop];
    }
    logger.debug("get values:%O", toRet);
    return toRet;
  };
  //#endregion

  getDbFieldName = (name) => this.#db.formatDbField(name);
  getDbParamName = (name, index) => this.#db.formatDbParam(name, index);

  createNewTransaction = async () => await this.#db.getNewTransaction();
  commitTransaction = async (conn) => await this.#db.commit(conn);
  rollbackTransaction = async (conn) => await this.#db.rollback(conn);
  disposeTransaction = async (conn) => await this.#db.release(conn);

  validate() {
    const { error, value } = this.#schema.joi.validate(this.jsonValues());
    if (error) {
      throw new Error(`Validation error: ${error.details[0].message}`);
    }
    return value;
  }
}

// funzione di utilità (assicurati che questa funzione sia definita)
function getLowerKeyValue(data, keys, defaultValue = undefined) {
  const normalizedKeys = keys.map((k) => k.toLowerCase());
  for (const key in data) {
    if (normalizedKeys.includes(key.toLowerCase())) {
      return data[key];
    }
  }
  return defaultValue;
}

module.exports = { AbstractDbCrudModel };
