import { AbstractDbCrudModel } from "cruder/abstractCrud";
import  <PERSON><PERSON>  from 'joi';
class PatientModel extends AbstractDbCrudModel {
  static schema = {
    schemaName: "NICE",
    tableName: "PATIENTS",
    keys: ["ID"],
    fields: ["CODE", "FIRST_NAME", "LAST_NAME", "BIRTH_DATE", "GENDER", "EF", "ESV", "STATUS_TAG", "DELETED"],
    mapping: {
      ID: "id",
      CODE: "code",
      FIRST_NAME: "firstName",
      LAST_NAME: "lastName",
      BIRTH_DATE: "birthDate",
      GENDER: "gender",
      EF: "ef",
      ESV: "esv",
      STATUS_TAG: "statusTag",
      DELETED: 'deleted'
    },
    joi: Joi.object({
      ID: Joi.string().max(50).required(),
      BIRTH_DATE: Joi.date().required(),
      FIRST_NAME: Joi.string().max(25).required(),
      LAST_NAME: Joi.string().max(25).required(),
      /*NAME: Joi.string().max(50).allow(null),
      SALARY: Joi.number().allow(null),
      IS_ACTIVE: Joi.string().max(1).allow("Y").allow("N"),
      CREATED_AT: Joi.date().allow(null),*/
    }),
  };
  constructor(data = {}, dbType = "oracle") {
    super(data, dbType);
  }
}
export default PatientModel;
