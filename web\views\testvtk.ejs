<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1" />
    <link
      rel="icon"
      type="image/x-icon"
      href="https://images.squarespace-cdn.com/content/v1/612f2e13d90b21039c48c496/b8deb88b-a86b-4a19-8579-6185584710c4/favicon.ico?format=100w"
    />
    <link href="/css/tailwind.css" rel="stylesheet" />
    <title>AnatomyViewer</title>
  </head>
  <body>
    <!-- Full-screen loading overlay -->
    <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
      <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
      <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
    </div>

    <!-- 3D Viewer -->
    <canvas id="vtk-3d-canvas" class="w-1/2 h-full hidden"></canvas>

    <div id="buttonRow" class="absolute top-4 left-1/2 -translate-x-1/2 flex gap-4 z-50 bg-darkgray bg-opacity-80 px-4 py-2 rounded shadow-lg">
      <button onclick="">TEST</button>
      <%= t("viewer.view_ll") %>
    </div>
    <%- include('components/viewer-hamburger') %>
    <script>
      window.MODELS = {
        oldVtuModel: '<%= vtuModel %>',
        vtuModel: 'case_PSS_map before.vtu',
        oldPlyModel: '<%= plyModel %>',
        plyModel: 'case_PSS_veins.obj',
      };
    </script>
    <script>
      let timeout;
      function debounce(func, delay) {
        return function (...args) {
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(this, args), delay);
        };
      }
      function handleCheckboxChange(event) {
        const checkboxId = event.target.id; // Ottieni l'ID del checkbox
        const isChecked = event.target.checked; // Ottieni lo stato (checked o not checked)
        const dataId = event.target.getAttribute('data-id');
        console.log(`Checkbox ${checkboxId} (data-id: ${dataId}) is now ${isChecked ? 'checked' : 'unchecked'}`);
        const combobox = document.querySelector(`select[data-id="${dataId}"]`);
        const slider = document.querySelector(`input[type="range"][data-id="${dataId}"]`);
        if (combobox && slider) {
          if (isChecked) {
            combobox.style.display = 'block';
            slider.style.display = 'block';
          } else {
            combobox.style.display = 'none';
            slider.style.display = 'none';
          }
        }
        window.setActorVisibility(dataId, isChecked);
      }
      function handleSliderChange(event) {
        const dataId = event.getAttribute('data-id');
        window.setActorOpacity(dataId, event.value / 100);
      }
      // Funzione per gestire il cambiamento di stato dei checkbox
      function handleComboboxChange(event) {
        const checkboxId = event.target.id; // Ottieni l'ID del checkbox
        const selValue = event.target.value; // Ottieni lo stato (checked o not checked)
        const dataId = event.target.getAttribute('data-id');
        window.setActorMapName(dataId, selValue);
      }
      // Aggiungi il gestore di eventi solo ai checkbox all'interno del div con ID 'hamburgerMenu'
      document.querySelectorAll('#hamburgerMenu input[type="checkbox"]').forEach((checkbox) => {
        checkbox.addEventListener('change', handleCheckboxChange);
      });
      document.querySelectorAll('#hamburgerMenu input[type="range"]').forEach((slider) => {
        slider.addEventListener(
          'input',
          debounce(() => handleSliderChange(slider), 100)
        );
      });
      document.querySelectorAll('#hamburgerMenu select').forEach((select) => {
        select.addEventListener('change', handleComboboxChange);
      });
    </script>
    <script src="/js/anatomy-viewer.js" type="module"></script>
    <script type="module">
      // call init after viewer is loaded
      window.addEventListener('viewerLoaded', async () => {
        await window.viewerInit();
        //const lista = window.getScalarNamesArray();
        //console.log("lista:", lista);
      });
    </script>
  </body>
</html>
