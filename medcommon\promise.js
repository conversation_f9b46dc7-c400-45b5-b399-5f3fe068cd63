const handlers = require("./promiseHandlers");

module.exports = function promiseMiddleware(req, res, next) {
  res.promise = (p) => {
    let promiseToResolve;
    if (p.then && p.catch) {
      promiseToResolve = p;
    } else if (typeof p === "function") {
      promiseToResolve = Promise.resolve().then(() => p());
    } else {
      promiseToResolve = Promise.resolve(p);
    }
    return promiseToResolve.then((data) => handlers.handleResponse(res, data)).catch((e) => handlers.handleError(res, e));
  };
  next();
};

