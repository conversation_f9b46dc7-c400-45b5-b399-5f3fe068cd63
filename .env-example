# parametri db oracle (per oauth2 e session)
INSTANTCLIENT=C:\oracle\instantclient_19_11\
CONNECTSTRING=**********:1522/MELITTA
USERORACLE=OAUTH2
PASSORACLE=OAUTH2
ORA_POOL_INCREMENT=4
ORA_POOL_MIN=5
ORA_POOL_MAX=200

# Node network settings. 
IP_ADDRESS=0.0.0.0
PORT=3000
PROXY_LIST=1
VIRTUALDIRPATH=/

# Environment variables
NODE_ENV=dev

#gestione dei LOGS
LOGPATH=logs
LOG_CONSOLE_LEVEL="silly"
LOG_FILE_LEVEL="silly"

#Campi it medarchiver
APPLICATION=Nice v3 backend 
CODE=
SITE=

# La passord di cookie-sessions
COOKIE_SECRET=M3d4rch!v3r
#se voglio hostare /api/oauth/ ... in locale attivo self_api..
OAUTH2_SELF_API=1
#la secret del jwt integrato nel token
OAUTH2_JWT_SECRET=AULIULETULILEMBLEMBLUM
#il nome dell'issuer all'interno di jwt per convalida grant token_exchange.
OAUTH2_JWT_ISSUER=http://localhost:3133
#id & secret DEVONO essere censiti nell'oauth2
APP_CLIENT_ID=e5edfacd-f9c3-400e-b0c0-0b3197ce3983
APP_CLIENT_SECRET=0ed4f85b-e2df-4eab-9df7-0f2416557474
# Mettere la stessa porta di ascolto PORT=
APP_OAUTH_REDIRECT_URL=http://localhost:3000/callback
# L'indirizzo dell'OAuth2 da utilizzare
APP_OAUTH_URL=http://*********:3232/api/oauth
# Questi sonno abbastanza standard:
APP_OAUTH_TOKEN_ENDPOINT=/token
APP_OAUTH_AUTHORIZE_ENDPOINT=/authorize
APP_OAUTH_USERINFO_ENDPOINT=/userinfo
# SCOPE Obbligatorio (e deve essere presente nello scope del client oauth2)
APP_OAUTH_SCOPE=device_id

AUTH_KEY_SECRET=string

STORAGE_PATH=/path/to/storage/data
IMMENSIVE_HOST=http://localhost:80
DICOM_URL=http://*********:8999/viewer/DisplayStudy.html?username=xspline&password=xspline&
PROCESSING_URL=http://*********:1217
#Questo viene fornito al browser dei clients..
WEB_SOCKET_URL=ws://*********
#Usato sia da server sia da clients (browser, etc..)
WEB_SOCKET_PORT=8080
#Sul backend docker ATTIVARLO a true!
RUN_JOB_SCHEDULER=