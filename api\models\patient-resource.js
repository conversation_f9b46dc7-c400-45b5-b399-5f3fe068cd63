import { AbstractDbCrudModel } from "cruder/abstractCrud";
import  <PERSON><PERSON>  from 'joi';
class PatientResourceModel extends AbstractDbCrudModel {
  static schema = {
    schemaName: "NICE",
    tableName: "RESOURCES",
    keys: ["ID"],
    fields: ["ID_PATIENT", "ID_USER_CREATE", "ID_USER_UPDATE", "ID_RESOURCE_TYPE", "RESOURCE_DATE", "RESOURCE_TAG", "RESOURCE_NOTES", "RESOURCE_ORDER", "UNIQUE_ID"],
    mapping: {
      ID: "id",
      ID_PATIENT: "idPatient",
      ID_USER_CREATE: "idUserCreate",
      ID_USER_UPDATE: "idUserUpdate",
      ID_RESOURCE_TYPE: "idResourceType",
      RESOURCE_DATE: "resourceDate",
      RESOURCE_TAG: "resourceTag",
      RESOURCE_NOTES: "resourceNotes",
      RESOURCE_ORDER: "resourceOrder",
      UNIQUE_ID: "uuid",
    },
    joi: Joi.object({
      ID: Joi.string().max(50).required(),
      ID_PATIENT: Joi.string().max(50).required(),
      RESOURCE_NOTES: Joi.string().max(25).allow(null),
      /*NAME: Joi.string().max(50).allow(null),
      BIRTH_DATE: Joi.date().required(),
      SALARY: Joi.number().allow(null),
      IS_ACTIVE: Joi.string().max(1).allow("Y").allow("N"),
      CREATED_AT: Joi.date().allow(null),*/
    }),
  };
  constructor(data = {}, dbType = "oracle") {
    super(data, dbType);
  }
}
export default PatientResourceModel;
