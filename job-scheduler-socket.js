import logger from "medcommon/logger";
import { WebSocket, WebSocketServer } from "ws";
import axios from "axios";
import path from "path";
import fs from "fs";
import JobSchedulerSocketModel from "./api/models/job-scheduler.js";
import VMapResourceModel from "./api/models/view-map-resource.js";
import PatientModel from "./api/models/patient.js";

import CommonUtils from "./api/utils.js";
const utils = new CommonUtils();

const typesDicts = {
  f14_veins_ply: 4,
  f26_activation_map: 3,
  f31_tp_coords: 5,
};
const projectRoot = process.cwd();

export class JobSchedulerSocket {
  #started = false;

  /** Crea un istanza della gestione connessioni socket.io
   *
   * @param {*} options le opzioni utilizzate in fase di start del socket.
   */
  constructor(options) {
    this.options = options || {};
    this.wss = new WebSocketServer({ port: options.port });
    this.wss.on("connection", this.onConnection);
    this.wss.on("error", logger.error);
    this.#started = true;
    this.#initStatusTimer();
  }

  /** Questo avviene UNA sola volta, quando il CLIENT si collega a questo server.
   *  L'oggetto socket contiene tutte le informazioni riguardanti il CLIENT.
   *  Attualmente non è previsto la connessione protetta dall'access_token ma è stato
   *  riportato (e commentato) il codice per eventualmente gestire l'accesso protetto da token.
   *  Essendo ospitato da Express Server che ha la gestione del cookie-session questo è accessibile
   *  anche dal socket.
   * @param {*} socket
   */
  onConnection = async function (clientWs) {
    logger.info("WebSocket Client connected");
    // Invia un messaggio al client
    clientWs.send(JSON.stringify({ message: "Welcome to the WebSocket server!" }));

    // Gestisci i messaggi ricevuti dal client
    clientWs.on("message", async (message) => {
      logger.info(`clientWs on message: %O`, message);
      // Invia un messaggio di risposta al client
      //ws.send(`Server received: ${message}`);
      //Provo a fare il parse del messaggio in json e se valido cerco l'azione..
      try {
        const jsonData = JSON.parse(message);
        if (jsonData && jsonData["api-request"]) {
          const eventType = jsonData["api-request"].toLowerCase();
          switch (eventType) {
            case "jobs-list":
              new JobSchedulerSocketModel().findFree([], "JOB_STATUS = 0", "CREATED_AT", true).then((result) => {
                clientWs.send(JSON.stringify({ "api-response": "jobs-list", list: result }));
              });
              break;
            default:
              break;
          }
        }
      } catch (error) {}
    });
    // Gestisci la chiusura della connessione
    clientWs.on("close", () => {
      logger.info("WebSocket Client disconnected");
    });
  };

  #broadcastData(data = {}) {
    if (this.#started) {
      this.wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          // Controlla se il client è ancora connesso
          client.send(JSON.stringify(data));
        } else {
          client.terminate();
        }
      });
    }
  }
  //STAGES:
  // SEGMENTATION_STAGE:
  // -> Quando è finita ? statusTrigger
  // -> Cosa fare quando è finita ?
  //    !-> Avviare ACTMAP_STAGE Con ECG della mappa.
  //    !-> job finito -> attivo next job.
  // ACTMAP_STAGE:
  // -> Quando è finita ? statusTrigger
  // -> Cosa fare quando è finita ?
  //    !-> Scaricare i dati  e attaccarli alla mappa indicata.
  //    Cosa devo scaricare:
  //    f14_veins_ply:
  //    f26_activation_map:
  //    f31_tp_coords:
  //    aggiorno il campo status della mappa a zero.
  //    !-> job finito -> attivo next job (se esiste).
  // BIPOL_SIM:
  // -> Quando è finita ? statusTrigger
  // -> Cosa fare quando è finita ?
  //    !-> Scaricare i dati  e attaccarli alla mappa indicata.
  //    !-> job finito -> attivo next job (se esiste).
  #doJob = async () => {
    this.#clearStatusTimer();
    try {
      //Step 1) Legge da una tabella db i patienti e gli steps da controllare:
      const jobsList = await new JobSchedulerSocketModel().findFree([], "JOB_STATUS = 0", "CREATED_AT", true);
      if (jobsList && jobsList.length > 0) {
        //Prendo solo il primo per ordine di tempo.
        const jobRow = jobsList[0];
        //Invio il broadcast del
        const sendData = { "event-type": "job-scheduler-run", ...jobRow };
        this.#broadcastData(sendData);

        let idPatientUrl = jobRow.idPatient;

        const patientData = await new PatientModel().findByKeys([jobRow.idPatient], true);
        if (patientData.code.includes("-")) {
          idPatientUrl = patientData.code;
        }
        const statusUrl = `${process.env.PROCESSING_URL}/api/processing/V2/patient/rtam_${idPatientUrl}/status`;

        //Step 2) se ho cose da fare le faccio in base allo step da fare (segmentation- > controllo nn cose e via cosi)
        switch (jobRow.stage) {
          case "create_segmentation":
            {
              const postUrl = `${process.env.PROCESSING_URL}/api/processing/V1/patient/rtam_${idPatientUrl}/segmentation_stage/start`;
              const projectRoot = process.cwd();
              const mapEcg = await new VMapResourceModel().findFree(
                [jobRow.idMap, jobRow.idPatient, 8],
                "ID_MAP = :1 AND ID_PATIENT = :2 AND ID_RESOURCE_TYPE = :3",
                "",
                true
              );
              //TODO: Check if exist mapEcg...
              const fullFileName = path.join(projectRoot, "storage", `${jobRow.idPatient}`, `${mapEcg[0].uniqueId}`);
              const uploadResult = await utils.uploadFile(postUrl, "file_full_dicom", fullFileName);
              if (uploadResult) {
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "create_segmentation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "poll_segmentation":
          case "segmentation":
            {
              const response = await axios.get(statusUrl);
              const data = response.data.details;
              logger.info("PROCESSING STATUS:%O", data);
              let done = 0;
              if (data["f0_full_study_dicom_zip"] === "Finished") done++;
              if (data["f1_torso_dicom_zip"] === "Finished") done++;
              if (data["f4_heart_dicom_zip"] === "Finished") done++;
              if (data["f6_heart_nii"] === "Finished") done++;
              if (data["f10_ventricle_ply"] === "Finished") done++;
              if (data["f11_ventricle_ply_preview"] === "Finished") done++;
              if (data["f14_veins_ply"] === "Finished") done++;
              if (data["f15_veins_ply_preview"] === "Finished") done++;
              if (data["f16_torso_mesh"] === "Finished") done++;
              if (data["f17_lungs_mesh"] === "Finished") done++;
              if (data["f18_aha_center_points"] === "Finished") done++;
              if (data["f19_aha_ventricle"] === "Finished") done++;
              if (data["f20_tetra_mesh"] === "Finished") done++;
              if (data["f21_leads"] === "Finished") done++;
              if (data["f22_cube"] === "Finished") done++;
              if (data["f23_synt_activation_map"] === "Finished") done++;
              if (data["f24_ecg12"] === "Finished") done++;
              if (data["f34_cs_seg_nii"] === "Finished") done++;
              if (data["f35_v_seg_nii"] === "Finished") done++;
              if (done == 19) {
                //TODO: Quando la segmentazione è finita scaricare le risorse create e associarle al ctscan...
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "segmentation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "create_actmap_stage":
            {
              const postUrl = `${process.env.PROCESSING_URL}/api/processing/V2/patient/rtam_${idPatientUrl}/actmap_stage/start/${jobRow.idEcg}`;
              const projectRoot = process.cwd();
              const mapEcg = await new VMapResourceModel().findFree(
                [jobRow.idMap, jobRow.idPatient, 1],
                "ID_MAP = :1 AND ID_PATIENT = :2 AND ID_RESOURCE_TYPE = :3",
                "",
                true
              );
              const fullFileName = path.join(projectRoot, "storage", `${jobRow.idPatient}`, `${mapEcg[0].uniqueId}`);
              const uploadResult = await utils.uploadFile(postUrl, "file_ecg", fullFileName);
              if (uploadResult) {
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "create_actmap_stage", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "poll_actmap_stage":
          case "actmap_stage":
            {
              const response = await axios.get(statusUrl);
              const data = response.data.details;
              logger.debug("Axios response:%O", data);
              let done = 0;
              if (data["f14_veins_ply"] == "Finished") {
                const type = "f14_veins_ply";
                const fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, jobRow.idEcg);
                if (fileName) {
                  await utils.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);
                  done++;
                }
              }
              if (data["f26_activation_map"][jobRow.idEcg] == "Finished") {
                const type = "f26_activation_map";
                const fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, jobRow.idEcg);
                if (fileName) {
                  await utils.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);
                  done++;
                }
              }
              if (data["f31_tp_coords"][jobRow.idEcg] == "Finished") {
                const type = "f31_tp_coords";
                const fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, jobRow.idEcg);
                if (fileName) {
                  await utils.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);
                  done++;
                }
              }
              if (done == 3) {
                const result = await utils.setMapReady(jobRow.idMap);
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "actmap_stage", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "create_simulation": //id_map punta su id_map_result di simulations
            {
              const jobRowData = JSON.parse(jobRow.jobData);
              const postUrl = `${process.env.PROCESSING_URL}/api/processing/V2/patient/rtam_${idPatientUrl}/actmap_stage/start/rtam_${jobRow.idEcg}/bipolsimulation?pdelay=-1&ppos=${jobRowData.ppol}`;
              const mapEcg = await new VMapResourceModel().findFree(
                [jobRow.idMap, jobRow.idPatient, 1],
                "ID_MAP = :1 AND ID_PATIENT = :2 AND ID_RESOURCE_TYPE = :3",
                "",
                true
              );
              const fullFileName = path.join(projectRoot, "storage", `${jobRow.idPatient}`, `${mapEcg[0].uniqueId}`);
              const uploadResult = await utils.uploadFile(postUrl, "file_ecg", fullFileName);
              if (uploadResult) {
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "create_simulation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "poll_simulation":
          case "simulation": //id_map punta su id_map_result di simulations
            {
              const response = await axios.get(statusUrl);
              const jobRowData = JSON.parse(jobRow.jobData);
              const data = response.data.details;
              logger.info("PROCESSING STATUS:%O", data);
              let done = 0;
              const idJob = `rtam_${jobRow.idEcg}`;
              if (data["f7_ecg_original"][idJob] == "Finished") done++;
              if (data["f25_cube_features"][idJob] == "Finished") done++;
              if (data["f26_activation_map"][idJob] == "Finished") done++;
              if (data["f27_cs_geometry"][idJob] == "Finished") done++;
              if (data["f28_skel_geometry"][idJob] == "Finished") done++;
              if (data["f29_cs_data"][idJob] == "Finished") done++;
              if (data["f30_skel_data"][idJob] == "Finished") done++;
              if (data["f31_tp_coords"][idJob] == "Finished") done++;
              if (data["f32_v_geometry"][idJob] == "Finished") done++;
              if (data["f33_v_data"][idJob] == "Finished") done++;
              if (done == 10) {
                //TODO: Trasformare in transazione il tutto.

                let type = "f14_veins_ply";
                let fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, idJob);
                await utils.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);

                type = "f26_activation_map";
                fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, idJob);
                await utils.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);

                type = "f31_tp_coords";
                fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, idJob);
                await utils.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);

                const resultSim = await utils.setSimulationReady(jobRowData.idSim);
                const resultMap = await utils.setMapReady(jobRow.idMap);

                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "simulation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          default:
            break;
        }
      }
    } catch (error) {
      logger.error("Error on job-scheduler-run: %O", error);
    }
    this.#initStatusTimer();
  };

  async updateJobRowStatus(jobRow) {
    try {
      const jobUpdate = new JobSchedulerSocketModel(jobRow);
      jobUpdate.jobStatus = 1;
      await jobUpdate.update();
    } catch (error) {
      logger.error("Error on updateJobRowStatus:%O", error);
    }
  }

  /**Get File and Save to storage. return falsy */
  async dowloadAndSaveFile(idPatient, idMap, type, ecgId = "default") {
    let toRet = false;
    const url = `${process.env.PROCESSING_URL}/api/processing/V2/patient/rtam_${idPatient}/f/${type}/${ecgId}`;
    const newFileResponse = await utils.downloadFile(url);
    if (newFileResponse) {
      const fileFullPath = utils.getStorageFullPath(idPatient, type, idMap, true);
      const fullFileName = path.join(fileFullPath, newFileResponse.fileName);
      try {
        if (!fs.existsSync(fullFileName)) {
          fs.writeFileSync(fullFileName, Buffer.from(newFileResponse.data));
        } else {
          logger.verbose("file '%s' already exist, save skipped", fullFileName);
        }
        toRet = newFile.fileName;
      } catch (error) {
        logger.error("Error on downloadAndSaveFile:%O", error);
      }
    }
    return toRet;
  }

  async #initStatusTimer() {
    logger.debug("#rearmJobStatusTimer");
    this.statusTimer = setTimeout(() => {
      if (process.env.RUN_JOB_SCHEDULER) {
        this.#doJob();
      }
    }, 1000 * 15);
    this.statusTimer.unref();
  }

  #clearStatusTimer() {
    if (this.statusTimer) {
      logger.debug("#clearJobStatusTimer");
      clearTimeout(this.statusTimer);
      this.statusTimer = undefined;
    }
  }
}
