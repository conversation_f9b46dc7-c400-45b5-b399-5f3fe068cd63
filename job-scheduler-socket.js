import logger from "medcommon/logger";
import { WebSocket, WebSocketServer } from "ws";
import axios from "axios";
import path from "path";
import fs from "fs";
import oracle from "medcommon/oracle";
import JobSchedulerSocketModel from "./api/models/job-scheduler.js";
import VMapResourceModel from "./api/models/view-map-resource.js";
import utils from "./api/utils.js";
import PatientModel from "./api/models/patient.js";

const typesDicts = {
  f14_veins_ply: 4,
  f26_activation_map: 3,
  f31_tp_coords: 5,
};

export class JobSchedulerSocket {
  #started = false;

  /** Crea un istanza della gestione connessioni socket.io
   *
   * @param {*} options le opzioni utilizzate in fase di start del socket.
   */
  constructor(options) {
    this.options = options || {};
    this.wss = new WebSocketServer({ port: options.port });
    this.wss.on("connection", this.onConnection);
    this.wss.on("error", logger.error);
    this.#started = true;
    this.#initStatusTimer();
  }

  /** Questo avviene UNA sola volta, quando il CLIENT si collega a questo server.
   *  L'oggetto socket contiene tutte le informazioni riguardanti il CLIENT.
   *  Attualmente non è previsto la connessione protetta dall'access_token ma è stato
   *  riportato (e commentato) il codice per eventualmente gestire l'accesso protetto da token.
   *  Essendo ospitato da Express Server che ha la gestione del cookie-session questo è accessibile
   *  anche dal socket.
   * @param {*} socket
   */
  onConnection = async function (ws) {
    logger.info("WebSocket Client connected");
    // Invia un messaggio al client
    ws.send(JSON.stringify({ message: "Welcome to the WebSocket server!" }));

    // Gestisci i messaggi ricevuti dal client
    ws.on("message", (message) => {
      logger.info(`Received: ${message}`);
      // Invia un messaggio di risposta al client
      //ws.send(`Server received: ${message}`);
    });
    // Gestisci la chiusura della connessione
    ws.on("close", () => {
      logger.info("WebSocket Client disconnected");
    });
  };

  /** Questo metodo invia a TUTTI i clients attualmente connessi l'evento 'server-status' con
   *  alcune informazioni 'pubbliche'.  */
  #statusBroadcast() {
    if (this.#started) {
      this.wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          // Controlla se il client è ancora connesso
          client.send(JSON.stringify({ type: "server-status", status: "alive", uptime: process.uptime() }));
        } else {
          client.terminate();
        }
      });
    }
  }

  #broadcastData(data = {}) {
    if (this.#started) {
      this.wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          // Controlla se il client è ancora connesso
          client.send(JSON.stringify(data));
        } else {
          client.terminate();
        }
      });
    }
  }
  //STAGES:
  // SEGMENTATION_STAGE:
  // -> Quando è finita ? statusTrigger
  // -> Cosa fare quando è finita ?
  //    !-> Avviare ACTMAP_STAGE Con ECG della mappa.
  //    !-> job finito -> attivo next job.
  // ACTMAP_STAGE:
  // -> Quando è finita ? statusTrigger
  // -> Cosa fare quando è finita ?
  //    !-> Scaricare i dati  e attaccarli alla mappa indicata.
  //    Cosa devo scaricare:
  //    f14_veins_ply:
  //    f26_activation_map:
  //    f31_tp_coords:
  //    aggiorno il campo status della mappa a zero.
  //    !-> job finito -> attivo next job (se esiste).
  // BIPOL_SIM:
  // -> Quando è finita ? statusTrigger
  // -> Cosa fare quando è finita ?
  //    !-> Scaricare i dati  e attaccarli alla mappa indicata.
  //    !-> job finito -> attivo next job (se esiste).
  #doJob = async () => {
    this.#clearStatusTimer();
    try {
      //Step 1) Legge da una tabella db i patienti e gli steps da controllare:
      const jobsList = await new JobSchedulerSocketModel().findFree([], "JOB_STATUS = 0", "CREATED_AT", true);
      if (jobsList && jobsList.length > 0) {
        //Prendo solo il primo per ordine di tempo.
        const jobRow = jobsList[0];
        //Invio il broadcast del
        const sendData = { "event-type": "job-scheduler-run", ...jobRow };
        this.#broadcastData(sendData);

        let idPatientUrl = jobRow.idPatient;
        
        const patientData = await new PatientModel().findByKeys([jobRow.idPatient],true);
        if (patientData.code.includes('-')) {
          idPatientUrl = patientData.code;
        }

        const statusUrl = `${process.env.PROCESSING_URL}/api/processing/V2/patient/rtam_${idPatientUrl}/status`;
        //Step 2) se ho cose da fare le faccio in base allo step da fare (segmentation- > controllo nn cose e via cosi)
        switch (jobRow.stage) {
          case "create_segmentation":
            {
              const postUrl = `${process.env.PROCESSING_URL}/api/processing/V1/patient/rtam_${idPatientUrl}/segmentation_stage/start`;
              const projectRoot = process.cwd();
              const mapEcg = await new VMapResourceModel().findFree(
                [jobRow.idMap, jobRow.idPatient, 8],
                "ID_MAP = :1 AND ID_PATIENT = :2 AND ID_RESOURCE_TYPE = :3",
                "",
                true
              );
              const fullFileName = path.join(projectRoot, "storage", `${jobRow.idPatient}`, `${mapEcg[0].uniqueId}`);
              const uploadResult = await utils.uploadFile(postUrl, "file_full_dicom", fullFileName);
              if (uploadResult) {
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "create_segmentation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "segmentation":
            {
              const response = await axios.get(statusUrl);
              const data = response.data.details;
              logger.info("PROCESSING STATUS:%O", data);
              let done = 0;
              if (data["f0_full_study_dicom_zip"] === "Finished") done++;
              if (data["f1_torso_dicom_zip"] === "Finished") done++;
              if (data["f4_heart_dicom_zip"] === "Finished") done++;
              if (data["f6_heart_nii"] === "Finished") done++;
              if (data["f10_ventricle_ply"] === "Finished") done++;
              if (data["f11_ventricle_ply_preview"] === "Finished") done++;
              if (data["f14_veins_ply"] === "Finished") done++;
              if (data["f15_veins_ply_preview"] === "Finished") done++;
              if (data["f16_torso_mesh"] === "Finished") done++;
              if (data["f17_lungs_mesh"] === "Finished") done++;
              if (data["f18_aha_center_points"] === "Finished") done++;
              if (data["f19_aha_ventricle"] === "Finished") done++;
              if (data["f20_tetra_mesh"] === "Finished") done++;
              if (data["f21_leads"] === "Finished") done++;
              if (data["f22_cube"] === "Finished") done++;
              if (data["f23_synt_activation_map"] === "Finished") done++;
              if (data["f24_ecg12"] === "Finished") done++;
              if (data["f34_cs_seg_nii"] === "Finished") done++;
              if (data["f35_v_seg_nii"] === "Finished") done++;
              if (done == 19) {
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "segmentation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "create_actmap_stage":
            {
              const postUrl = `${process.env.PROCESSING_URL}/api/processing/V2/patient/rtam_${idPatientUrl}/actmap_stage/start/${jobRow.idEcg}`;
              const projectRoot = process.cwd();
              const mapEcg = await new VMapResourceModel().findFree(
                [jobRow.idMap, jobRow.idPatient, 1],
                "ID_MAP = :1 AND ID_PATIENT = :2 AND ID_RESOURCE_TYPE = :3",
                "",
                true
              );
              const fullFileName = path.join(projectRoot, "storage", `${jobRow.idPatient}`, `${mapEcg[0].uniqueId}`);
              const uploadResult = await utils.uploadFile(postUrl, "file_ecg", fullFileName);
              if (uploadResult) {
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "create_actmap_stage", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "actmap_stage":
            {
              const response = await axios.get(statusUrl);
              const data = response.data.details;
              logger.debug("Axios response:%O", data);
              let done = 0;
              if (data["f14_veins_ply"] == "Finished") {
                const type = "f14_veins_ply";
                const fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, jobRow.idEcg);
                await this.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);
                done++;
              }
              if (data["f26_activation_map"][jobRow.idEcg] == "Finished") {
                const type = "f26_activation_map";
                const fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, jobRow.idEcg);
                await this.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);
                done++;
              }
              if (data["f31_tp_coords"][jobRow.idEcg] == "Finished") {
                const type = "f31_tp_coords";
                const fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, jobRow.idEcg);
                await this.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);
                done++;
              }
              if (done == 3) {
                const updMapSql = "UPDATE MAPS SET STATUS=0 WHERE ID = :1";
                const resultMap = await oracle.dbExecute(updMapSql, [jobRow.idMap]);
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "actmap_stage", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "create_simulation": //id_map punta su id_map_result di simulations
            {
              const jobRowData = JSON.parse(jobRow.jobData);
              const postUrl = `${process.env.PROCESSING_URL}/api/processing/V2/patient/rtam_${idPatientUrl}/actmap_stage/start/rtam_${jobRow.idEcg}/bipolsimulation?pdelay=-1&ppos=${jobRowData.ppol}`;
              const projectRoot = process.cwd();
              const mapEcg = await new VMapResourceModel().findFree(
                [jobRow.idMap, jobRow.idPatient, 1],
                "ID_MAP = :1 AND ID_PATIENT = :2 AND ID_RESOURCE_TYPE = :3",
                "",
                true
              );
              const fullFileName = path.join(projectRoot, "storage", `${jobRow.idPatient}`, `${mapEcg[0].uniqueId}`);
              const uploadResult = await utils.uploadFile(postUrl, "file_ecg", fullFileName);
              if (uploadResult) {
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "create_simulation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          case "simulation": //id_map punta su id_map_result di simulations
            {
              const response = await axios.get(statusUrl);
              const jobRowData = JSON.parse(jobRow.jobData);
              const data = response.data.details;
              logger.info("PROCESSING STATUS:%O", data);
              let done = 0;
              const idJob = `rtam_${jobRow.idEcg}`;
              if (data["f7_ecg_original"][idJob] == "Finished") done++;
              if (data["f25_cube_features"][idJob] == "Finished") done++;
              if (data["f26_activation_map"][idJob] == "Finished") done++;
              if (data["f27_cs_geometry"][idJob] == "Finished") done++;
              if (data["f28_skel_geometry"][idJob] == "Finished") done++;
              if (data["f29_cs_data"][idJob] == "Finished") done++;
              if (data["f30_skel_data"][idJob] == "Finished") done++;
              if (data["f31_tp_coords"][idJob] == "Finished") done++;
              if (data["f32_v_geometry"][idJob] == "Finished") done++;
              if (data["f33_v_data"][idJob] == "Finished") done++;
              if (done == 10) {
                let type = "f14_veins_ply";
                let fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, idJob);
                await this.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);

                type = "f26_activation_map";
                fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, idJob);
                await this.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);

                type = "f31_tp_coords";
                fileName = await this.dowloadAndSaveFile(jobRow.idPatient, jobRow.idMap, type, idJob);
                await this.createDbResource(jobRow.idPatient, jobRow.idMap, typesDicts[type], type, fileName);

                const updSimStatusSql = "UPDATE SIMULATIONS SET STATUS=0 WHERE ID = :1";
                const resultSim = await oracle.dbExecute(updSimStatusSql, [jobRowData.idSim]);

                const updMapSql = "UPDATE MAPS SET STATUS=0 WHERE ID = :1";
                const resultMap = await oracle.dbExecute(updMapSql, [jobRow.idMap]);
                await this.updateJobRowStatus(jobRow);
                const sendData = { "event-type": "job-completed", stage: "simulation", mapId: jobRow.mapId };
                this.#broadcastData(sendData);
              }
            }
            break;
          default:
            break;
        }
      }
    } catch (error) {
      logger.error("Error on job-scheduler-run: %O", error);
    }
    this.#initStatusTimer();
  };

  async updateJobRowStatus(jobRow) {
    try {
      const jobUpdate = new JobSchedulerSocketModel(jobRow);
      jobUpdate.jobStatus = 1;
      await jobUpdate.update();
    } catch (error) {
      logger.error("Error on updateJobRowStatus:%O", error);
    }
  }

  /**Get File and Save to storage.*/
  async dowloadAndSaveFile(idPatient, idMap, type, ecgId = "default") {
    const url = `http://*********:1217/api/processing/V2/patient/rtam_${idPatient}/f/${type}/${ecgId}`;
    const response = await axios({
      method: "get",
      url: url,
      responseType: "stream", // Imposta il tipo di risposta su stream
    });
    const disposition = response.headers["content-disposition"];
    let fileName = "downloaded_file"; // Nome di default
    if (disposition && disposition.indexOf("attachment") !== -1) {
      const matches = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (matches != null && matches[1]) {
        fileName = matches[1].replace(/['"]/g, ""); // Rimuovi eventuali virgolette
      }
    }
    logger.debug("Axios response fileName:%O", fileName);
    const projectRoot = process.cwd();
    //creo la cartella.
    const dirPath = path.join(projectRoot, "storage", `${idPatient}`, `map-${idMap}`);
    fs.mkdirSync(dirPath, { recursive: true });

    const fullFileName = path.join(dirPath, fileName);
    if (!fs.existsSync(fullFileName)) {
      const writer = fs.createWriteStream(fullFileName);
      response.data.pipe(writer);
    } else {
      logger.debug("file '%s' already exist, skipped", fullFileName);
    }
    return fileName;
  }
  /**Add resource to map.*/
  async createDbResource(idPatient, idMap, resIdType, resNotes, fileName) {
    try {
      //Verifico se la risorsa è stata già aggiunta.
      const resourceExist = await oracle.dbSingleRow("SELECT id from RESOURCES where ID_PATIENT = :1 AND UNIQUE_ID = :2", [idPatient, fileName]);
      if (!resourceExist["ID"]) {
        const getResId = "SELECT s_resources_id.nextval id FROM DUAL";
        const nextVal = await oracle.dbSingleRow(getResId);
        const newResId = nextVal["ID"];
        //Save storage
        const insSql =
          "Insert into NICE.RESOURCES (id, ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values (:1, :2,'1','1',:3,sysdate,:4,'before','1',:5)";
        const db1 = await oracle.dbExecute(insSql, [newResId, idPatient, resIdType, resNotes, fileName]);
        const db2 = await oracle.dbExecute("insert into map_resources (id_map, id_resource) values(:1,:2)", [idMap, newResId]);
      } else {
        logger.debug("Resource '%s' already exists with id: %s", fileName, resourceExist["ID"]);
      }
    } catch (error) {
      logger.error("Error on createDbResource:%O", error);
    }
  }

  async #initStatusTimer() {
    logger.debug("#rearmJobStatusTimer");
    this.statusTimer = setTimeout(() => {
      if (process.env.RUN_JOB_SCHEDULER) {
        this.#doJob();
      }
    }, 1000 * 15);
    this.statusTimer.unref();
  }

  #clearStatusTimer() {
    if (this.statusTimer) {
      logger.debug("#clearJobStatusTimer");
      clearTimeout(this.statusTimer);
      this.statusTimer = undefined;
    }
  }

  /**
   * insert into NICE.processing_jobs (id, id_map, id_patient, stage) values(nice.s_processing_jobs_id.nextval, 10,  9, 'segmentation');
     insert into NICE.processing_jobs (id, id_map, id_patient, stage) values(nice.s_processing_jobs_id.nextval, 10,  9, 'actmap_stage');
     select * from nice.processing_jobs where job_status = 0 order by created_at asc;
   */
}
//export default JobSchedulerSocket;
