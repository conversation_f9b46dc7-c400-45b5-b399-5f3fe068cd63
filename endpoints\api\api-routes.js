import express from "express";
import patController from "./api-controllers.js";
import oauthRoutes from "oauth2/oauth2.routes";

import axios from "axios";
import logger from "medcommon/logger";
import { v4 } from "uuid";
import path from "path";
import * as fs from "fs/promises";

const apiRoutes = express.Router();

if (process.env.OAUTH2_SELF_API) {
  apiRoutes.use("/oauth", oauthRoutes);
}
//Le altre routes passano per Authorization middleware check token/track user.
apiRoutes.use(async (req, res, next) => {
  //TODO: Token check else 401
  next();
});

//TODO: Spostare implementazione su file separato
apiRoutes.route("/ecg/acquire").get(async (req, res) => {
  let toRet = false;
  const deviceId = process.env.HL7_ECG_DEVICE_ID;
  const ecgUrl = process.env.HL7_BACKEND_URL;
  const sessionId = v4();
  const url = `${ecgUrl}/acquire-ecg-10s/${deviceId}/${sessionId}`;
  let response;
  try {
    response = await axios.request({
      method: "GET",
      url: url,
    });
  } catch (error) {
    logger.error("error on acquireNewEcg: %O", error);
    throw error.response.data;
  }
  //res.send({ecgId:sessionId});
  let ecgData;
  try {
    const downUrl = `${ecgUrl}/download-ecg/${sessionId}`;
    ecgData = await axios.request({
      method: "GET",
      url: downUrl,
      responseType: "arrayBuffer",
    });
    if (ecgData) {
      const fileName = `${sessionId}.csv`;
      const projectRoot = process.cwd();
      //console.log(projectRoot);
      const filePath = path.join(projectRoot, "storage", "ecgs");
      const fullName = path.join(filePath, fileName);
      await fs.writeFile(fullName, Buffer.from(ecgData.data));
      toRet = { ecgId: fileName };
    }
  } catch (error) {
    logger.error(error);
  } finally {
    const purgeUrl = `${ecgUrl}/purge-ecg/${sessionId}`;
    const purgeResponse = await axios.request({
      method: "GET",
      url: purgeUrl,
    });
  }
  res.send(toRet);
});

/*  apiRoutes
  .route("/patients")
  .get(patController.patientGet)
  .post(patController.patientPost)
  .delete(patController.patientDelete);
*/

export default apiRoutes;
