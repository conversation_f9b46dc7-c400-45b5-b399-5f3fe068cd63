"use strict";
/**
 * Module dependencies.
 */
var UnauthorizedRequestError = require("@node-oauth/oauth2-server/lib/errors/unauthorized-request-error");
var NodeOAuthServer = require("@node-oauth/oauth2-server");
var Request = require("@node-oauth/oauth2-server").Request;
var Response = require("@node-oauth/oauth2-server").Response;
var InvalidArgumentError = require("@node-oauth/oauth2-server/lib/errors/invalid-argument-error");

/**
 * Constructor.
 */
class CustomOAuthServer {
  constructor(options) {
    options = options || {};

    if (!options.model) {
      throw new InvalidArgumentError("Missing parameter: `model`");
    }
    /*if (!options.model.logRequestToDb) {
      throw new InvalidArgumentError("Missing parameter: `logRequestToDb`");
    }*/
    this.useErrorHandler = options.useErrorHandler ? true : false;
    delete options.useErrorHandler;

    this.continueMiddleware = options.continueMiddleware ? true : false;
    delete options.continueMiddleware;

    this.server = new NodeOAuthServer(options);
  }

  /**
   * Authentication Middleware.
   * Returns a middleware that will validate a token.
   *
   * @param options {object=} will be passed to the authenticate-handler as options, see linked docs
   * @see https://node-oauthoauth2-server.readthedocs.io/en/master/api/oauth2-server.html#authenticate-request-response-options
   * @see: https://tools.ietf.org/html/rfc6749#section-7
   * @return {function(req, res, next):Promise.<Object>}
   */
  authenticate(options) {
    const fn = async function (req, res, next) {
      const request = new Request(req);
      const response = new Response(res);
      let token;
      try {
        token = await this.server.authenticate(request, response, options);
      } catch (error) {
        return handleError.call(this, error, req, res, null, next);
      }
      res.locals.oauth = { token };
      await this.logRequestToDb(req, res, "authenticate", token.accessToken, token.refreshToken);
      next();
    };
    return fn.bind(this);
  }

  /**
   * Authorization Middleware.
   * Returns a middleware that will authorize a client to request tokens.
   *
   * @param options {object=} will be passed to the authorize-handler as options, see linked docs
   * @see https://node-oauthoauth2-server.readthedocs.io/en/master/api/oauth2-server.html#authorize-request-response-options
   * @see: https://tools.ietf.org/html/rfc6749#section-3.1
   * @return {function(req, res, next):Promise.<Object>}
   */
  authorize(options) {
    const fn = async function (req, res, next) {
      const request = new Request(req);
      const response = new Response(res);
      let code;
      try {
        code = await this.server.authorize(request, response, options);
      } catch (e) {
        return handleError.call(this, e, req, res, response, next);
      }
      res.locals.oauth = { code };
      await this.logRequestToDb(req, res, "authorize");
      if (this.continueMiddleware) {
        next();
      }
      return handleResponse.call(this, req, res, response);
    };
    return fn.bind(this);
  }

  /**
   * Grant Middleware.
   * Returns middleware that will grant tokens to valid requests.
   *
   * @param options {object=} will be passed to the token-handler as options, see linked docs
   * @see https://node-oauthoauth2-server.readthedocs.io/en/master/api/oauth2-server.html#token-request-response-options
   * @see: https://tools.ietf.org/html/rfc6749#section-3.2
   * @return {function(req, res, next):Promise.<Object>}
   */
  token(options) {
    const fn = async function (req, res, next) {
      const request = new Request(req);
      const response = new Response(res);
      let token;
      try {
        token = await this.server.token(request, response, options);
      } catch (e) {
        return handleError.call(this, e, req, res, response, next);
      }
      res.locals.oauth = { token };
      await this.logRequestToDb(req, res, "token", token.accessToken, token.refreshToken);
      if (this.continueMiddleware) {
        next();
      }
      return handleResponse.call(this, req, res, response);
    };
    return fn.bind(this);
  }

  logRequestToDb = async function logRequestToDb(req, res, action, accToken, refToken) {
    try {
      let agent = req.headers["user-agent"] || "not found";
      await this.server.options.model.logRequestToDb(req.path, action, req.ip, req.fullUrl, agent, accToken, refToken);
    } catch (error) {
      //ok cosi..
      //console.error("save logRequest FAILED:%O", error);
    }
  };
}

/**
 * Handle response.
 * @private
 */
const handleResponse = function (req, res, response) {
  if (response.status === 302) {
    const location = response.headers.location;
    delete response.headers.location;
    res.set(response.headers);
    res.redirect(location);
  } else {
    res.set(response.headers);
    res.status(response.status).send(response.body);
  }
};

/**
 * Handle error.
 * @private
 */
const handleError = function (e, req, res, response, next) {
  if (this.useErrorHandler === true) {
    next(e);
  } else {
    if (response) {
      res.set(response.headers);
    }
    res.status(e.code);
    if (e instanceof UnauthorizedRequestError) {
      return res.send();
    }
    res.send({ error: e.name, error_description: e.message });
  }
};

/**
 * Export constructor.
 */

module.exports = CustomOAuthServer;
