#include "GeometryViewer.h"

#include <array>
#include <iomanip>
#include <sstream>
#include <vtkActor.h>
#include <vtkAxesActor.h>
#include <vtkScalarBarActor.h>
#include <vtkCallbackCommand.h>
#include <vtkCamera.h>
#include <vtkCameraOrientationRepresentation.h>
#include <vtkCameraOrientationWidget.h>
#include <vtkCellArray.h>
#include <vtkCellData.h>
#include <vtkCharArray.h>
#include <vtkCollectionRange.h>
#include <vtkColorSeries.h>
#include <vtkColorTransferFunction.h>
#include <vtkCompositeDataSet.h>
#include <vtkCompositePolyDataMapper.h>
#include <vtkDataObjectTreeIterator.h>
#include <vtkDataObjectTreeRange.h>
#include <vtkTextActor.h>
#include <vtkTextProperty.h>
// #include <vtkGLTFReader.h>
#include <vtkGeometryFilter.h>
// #include <vtkHardwarePicker.h>
#include <vtkIdList.h>
#include <vtkInteractorStyleSwitch.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkMath.h>
#include <vtkMemoryResourceStream.h>
// #include <vtkMinimalStandardRandomSequence.h>
#include <vtkNew.h>
#include <vtkOBJReader.h>
#include <vtkOpenGLLowMemoryPolyDataMapper.h>
#include <vtkPLYReader.h>
// #include <vtkPNGWriter.h>
#include <vtkPointData.h>
#include <vtkPointPicker.h>
#include <vtkCellPicker.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkPolyDataReader.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
// #include <vtkRenderedAreaPicker.h>
#include <vtkRenderer.h>
#include <vtkRendererCollection.h>
// #include <vtkSTLReader.h>
// #include <vtkSelectionNode.h>
#include <vtkSetGet.h>
// #include <vtkShaderProgram.h>
#include <vtkSmartPointer.h>
#include <vtkSphereSource.h>
// #include <vtkTubeFilter.h>
#include <vtkType.h>
#include <vtkUnsignedCharArray.h>
#include <vtkWebAssemblyOpenGLRenderWindow.h>
#include <vtkWebAssemblyRenderWindowInteractor.h>
#include <vtkWebAssemblyWebGPURenderWindow.h>
// #include <vtkWindowToImageFilter.h>
// #include <vtkXMLMultiBlockDataReader.h>
// #include <vtkXMLPartitionedDataSetCollectionReader.h>
#include <vtkXMLPolyDataReader.h>
#include <vtkXMLUnstructuredGridReader.h>
#include <vtksys/SystemTools.hxx>
#include <vtkTextProperty.h>
#include <emscripten.h>
#include <iostream>
#include <string>
#include <unordered_set>
#include <vtkSmartPointer.h>
#include <vtkDepthSortPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkLookupTable.h>
#include <vtkPolyData.h>
// #include <vtkCamera.h>
#include <vtkPointData.h>
// #include <vtkTriangle.h>
#include <vtkUnstructuredGrid.h>
#include <vtkSmartPointer.h>
#include <vtkPoints.h>
// #include <vtkTriangle.h>
#include <vtkFloatArray.h> // Assicurati di includere questo
#include <vector>
#include <vtkThreshold.h>
#include <vtkPolyDataNormals.h>
#include <vtkAppendPolyData.h>
#include <vtkDataSetSurfaceFilter.h>
#include <vtkPolyDataWriter.h>
#include <vtkKdTreePointLocator.h>
/*
enum class AnatomyChannel
{
  WHOLE_HEART = 0,
  CORONARY_SINUS = 1,
  EPICARDIUM = 2,
  LV_ENDOCARDIUM = 3,
  RV_ENDOCARDIUM = 4,
  FIBROSIS = 5
};*/
std::vector<std::string> VIEWED_FIELD_NAMES = {
    "Anatomy",
    "Activation map",
    "AHA sectorization",
    "Epi-endo surfaces",
    "Left-right ventricles",
    "Angles",
    "Diameters",
    "Distances"};
std::vector<std::string> VIEWED_FIELD_UNITS = {
    "",
    "ms",
    "N sector",
    "",
    "",
    "degrees",
    "mm",
    "mm"};
std::string SOLID_COLOR = "Solid color";
std::string ACT_TIME_NAME = "Activation_time";
std::string AHA_NAME = "AHA_new";
std::string HEART_REGIONS_NAME = "For LDBR";
std::string LVRV_OVER_SEPTUM_NAME = "Neibors for epi-base";
std::string ANGLES_NAME = "Angles";
std::string THICKNESS_NAME = "Thickness";
std::string DISTANCES_NAME = "Distances";

const std::array<double, 3> MAX_RAINBOW_COLOR = {0.4, 0.0, 1.0};
const std::array<double, 3> MIN_RAINBOW_COLOR = {1.0, 0.0, 0.0};
class ColorMap
{
public:
  static vtkSmartPointer<vtkLookupTable> getClassicRainbow(int ind_white = -1, int tableSize = 40)
  {
    vtkSmartPointer<vtkLookupTable> lut = vtkSmartPointer<vtkLookupTable>::New();
    lut->SetNumberOfColors(tableSize);
    lut->SetNumberOfTableValues(tableSize);

    vtkSmartPointer<vtkColorTransferFunction> ctf = vtkSmartPointer<vtkColorTransferFunction>::New();
    ctf->SetColorSpaceToRGB();

    ctf->AddRGBPoint(0.00, MAX_RAINBOW_COLOR[0], MAX_RAINBOW_COLOR[1], MAX_RAINBOW_COLOR[2]);
    ctf->AddRGBPoint(0.25, 0.000, 1.000, 1.000);
    ctf->AddRGBPoint(0.50, 0.000, 1.000, 0.000);
    ctf->AddRGBPoint(0.75, 1.000, 1.000, 0.000);
    ctf->AddRGBPoint(1.00, MIN_RAINBOW_COLOR[0], MIN_RAINBOW_COLOR[1], MIN_RAINBOW_COLOR[2]);
    ctf->SetScaleToLinear();
    ctf->Build();

    // Here count of color is hardcoded
    int color_count = 12;
    double step = static_cast<double>(tableSize) / color_count;

    for (int i = 1; i < tableSize; ++i)
    {
      double rgb[3];
      ctf->GetColor((i / step) / color_count, rgb);
      lut->SetTableValue(tableSize - (i + 1), rgb[0], rgb[1], rgb[2]);
    }
    lut->SetTableValue(tableSize - 1, MAX_RAINBOW_COLOR[0], MAX_RAINBOW_COLOR[1], MAX_RAINBOW_COLOR[2]);
    lut->SetTableValue(0, MIN_RAINBOW_COLOR[0] + 1, MIN_RAINBOW_COLOR[1], MIN_RAINBOW_COLOR[2]);
    lut->SetTableValue(0, 2, 0, 0);

    if (ind_white != -1 && ind_white != tableSize)
    {
      lut->SetTableValue(ind_white, 1.0, 1.0, 1.0);
    }

    lut->SetRampToLinear();
    lut->Build();

    return lut;
  }

  static vtkSmartPointer<vtkLookupTable> getRainbowDesaturated(int tableSize = 26)
  {
    vtkSmartPointer<vtkLookupTable> lut = vtkSmartPointer<vtkLookupTable>::New();
    lut->SetNumberOfTableValues(tableSize);

    vtkSmartPointer<vtkColorTransferFunction> ctf = vtkSmartPointer<vtkColorTransferFunction>::New();
    ctf->SetColorSpaceToRGB();

    ctf->AddRGBPoint(0, 0.35, 0.35, 0.9);
    ctf->AddRGBPoint(0.15, 0, 0, 0.36);
    ctf->AddRGBPoint(0.25, 0, 1, 1);
    ctf->AddRGBPoint(0.43, 0, 0.5, 0);
    ctf->AddRGBPoint(0.55, 1, 1, 0);
    ctf->AddRGBPoint(0.70, 1, 0.38, 0);
    ctf->AddRGBPoint(0.80, 0.41, 0, 0);
    ctf->AddRGBPoint(1, 0.9, 0.4, 0.4);

    for (int i = 0; i < tableSize; ++i)
    {
      double rgb[3];
      ctf->GetColor(static_cast<double>(i) / tableSize, rgb);
      lut->SetTableValue(i, rgb[0], rgb[1], rgb[2]);
    }

    lut->SetRampToLinear();
    lut->Build();

    return lut;
  }

  static vtkSmartPointer<vtkLookupTable> getCoolToWarm(int tableSize = 20)
  {
    vtkSmartPointer<vtkLookupTable> lut = vtkSmartPointer<vtkLookupTable>::New();
    lut->SetNumberOfTableValues(tableSize);

    vtkSmartPointer<vtkColorTransferFunction> ctf = vtkSmartPointer<vtkColorTransferFunction>::New();
    ctf->SetColorSpaceToRGB();

    ctf->AddRGBPoint(0, 0.23, 0.27, 0.95);
    ctf->AddRGBPoint(0.5, 0.5, 0.0, 0.5);
    ctf->AddRGBPoint(1, 1.0, 0.01, 0.1);

    for (int i = 0; i < tableSize; ++i)
    {
      double rgb[3];
      ctf->GetColor(static_cast<double>(i) / tableSize, rgb);
      lut->SetTableValue(i, rgb[0], rgb[1], rgb[2]);
    }

    // These lines are redundant since the points are already added above
    ctf->AddRGBPoint(0, 0.23, 0.27, 0.95);
    ctf->AddRGBPoint(1, 1.0, 0.01, 0.1);

    lut->SetRampToLinear();
    lut->Build();

    return lut;
  }

  static vtkSmartPointer<vtkLookupTable> getSolidColorMap(const std::array<double, 3> &color)
  {
    double r_channel = color[0];
    double g_channel = color[1];
    double b_channel = color[2];

    vtkSmartPointer<vtkLookupTable> lut = vtkSmartPointer<vtkLookupTable>::New();
    lut->SetTableValue(0, r_channel, g_channel, b_channel);
    lut->Build();

    return lut;
  }
};
const std::vector<std::array<double, 3>> SOLID_COLORS = {
    {1.0, 0.0, 0.0}, // _RED_COLOR_
    {0.5, 0.5, 0.5}, // _GREY_COLOR_
    {1.0, 1.0, 1.0}, // _WHITE_COLOR_
    {0.0, 0.0, 0.0}  // _BLACK_COLOR_
};
const std::unordered_map<std::string, std::array<double, 3>> SOLID_COLORS_MAP = {
    {"RED", {1.0, 0.0, 0.0}},    // _RED_COLOR_
    {"GREY", {0.5, 0.5, 0.5}},   // _GREY_COLOR_
    {"WHITE", {1.0, 1.0, 1.0}},  // _WHITE_COLOR_
    {"BLACK", {0.0, 0.0, 0.0}},  // _BLACK_COLOR_
    {"BLUE", {0.0, 0.0, 1.0}},   // _BLUE_COLOR_
    {"GREEN", {0.0, 1.0, 0.0}},  // _GREEN_COLOR_
    {"YELLOW", {1.0, 1.0, 0.0}}, // _YELLOW_COLOR_
    {"CYAN", {0.0, 1.0, 1.0}},   // _CYAN_COLOR_
    {"MAGENTA", {1.0, 0.0, 1.0}} // _MAGENTA_COLOR_
};

/*
static std::map<std::string, vtkLookupTable *> lookupTableMap = {
    {"Activation_time", ColorMap::getClassicRainbow()},
    {"AHA_new", ColorMap::getRainbowDesaturated()},
    {"Solid color", ColorMap::getSolidColorMap(SOLID_COLORS[1])}};
*/
/** Legge i dati del modello e filtra
 */
vtkSmartPointer<vtkPolyData> GetThreshold(vtkSmartPointer<vtkDataObject> data, double min, double max, const std::string &channel_name, bool ContinuousCell = false, bool AllScalars = false)
{
  // Creazione del filtro di soglia
  vtkSmartPointer<vtkThreshold> threshold = vtkSmartPointer<vtkThreshold>::New();
  threshold->SetInputData(data);
  threshold->SetInputArrayToProcess(0, 0, 0, 0, channel_name.c_str());
  threshold->SetUpperThreshold(max);
  threshold->SetLowerThreshold(min);

  // Impostazione delle opzioni del filtro
  if (ContinuousCell)
  {
    threshold->UseContinuousCellRangeOn();
  }
  if (AllScalars)
  {
    threshold->AllScalarsOff();
  }

  // Aggiornamento del filtro
  threshold->Update();

  // Creazione della geometria dai dati di soglia
  vtkSmartPointer<vtkGeometryFilter> geometryFilter = vtkSmartPointer<vtkGeometryFilter>::New();
  geometryFilter->SetInputConnection(threshold->GetOutputPort());
  geometryFilter->Update();

  // Restituzione dei dati di poligonale
  return geometryFilter->GetOutput();
}
// TODO: Capire l'utilizzo qui oppure integrare ove usata.
vtkSmartPointer<vtkPolyDataNormals> CalculateNormals(vtkSmartPointer<vtkPolyData> input, bool flip = false)
{
  // Creazione del filtro per le normali
  vtkSmartPointer<vtkPolyDataNormals> normals_filter = vtkSmartPointer<vtkPolyDataNormals>::New();
  normals_filter->SetInputData(input);
  // Impostazione dell'opzione per capovolgere le normali
  if (flip)
  {
    normals_filter->FlipNormalsOn();
  }
  // Aggiornamento del filtro
  normals_filter->Update();
  // Restituzione del filtro delle normali
  return normals_filter;
}
// TODO: Se usata in punto solo rimuovere e spostare altrove.
vtkSmartPointer<vtkPolyData> appendPolyData(const std::vector<vtkSmartPointer<vtkPolyData>> &polydata_list)
{
  // Creazione del filtro di append
  vtkSmartPointer<vtkAppendPolyData> appendFilter = vtkSmartPointer<vtkAppendPolyData>::New();

  // Controllo se la lista non è vuota
  if (!polydata_list.empty())
  {
    for (const auto &data : polydata_list)
    {
      appendFilter->AddInputData(data);
    }
  }
  // Aggiornamento del filtro
  appendFilter->Update();
  // Restituzione dell'output del filtro
  return appendFilter->GetOutput();
}

vtkSmartPointer<vtkDepthSortPolyData> sortedPolyData(vtkSmartPointer<vtkPolyData> data, vtkSmartPointer<vtkCamera> camera = nullptr)
{
  vtkSmartPointer<vtkDepthSortPolyData> depthSort = vtkSmartPointer<vtkDepthSortPolyData>::New();
  depthSort->SetInputData(data); // Usa SetInputData
  depthSort->SetDirectionToBackToFront();
  depthSort->SetVector(1, 1, 1);
  if (camera)
  {
    depthSort->SetCamera(camera);
  }
  depthSort->SortScalarsOn();
  depthSort->Update();
  return depthSort; // Ritorna l'oggetto vtkDepthSortPolyData
}

vtkSmartPointer<vtkActor> CreateActor(vtkSmartPointer<vtkPolyData> poliData, vtkSmartPointer<vtkLookupTable> lut, const char *channel = "Activation_time")
{
  std::unordered_set<std::string> pointDataArrays;
  // Create a mapper
  auto mapper = vtkSmartPointer<vtkPolyDataMapper>::New();
  mapper->SetInputData(poliData);
  mapper->SetLookupTable(lut);

  if (auto *dataSet = vtkDataSet::SafeDownCast(poliData))
  {
    std::cout << "Processing vtkDataSet... searching for: " << channel << std::endl; // Log per il dataset
    vtkDataArray *data_array = dataSet->GetPointData()->GetScalars(channel);
    if (data_array)
    {
      std::cout << "mapper->SetScalarRange -> found:" << channel << std::endl;
      mapper->SetScalarRange(data_array->GetRange());
      mapper->SelectColorArray(channel);
      mapper->SetScalarVisibility(true);
      mapper->SetScalarModeToUsePointFieldData();
      mapper->SetColorModeToMapScalars();
      mapper->ScalarVisibilityOn();
      mapper->SetInterpolateScalarsBeforeMapping(1);
    }
    else
    {
      // mapper->SelectColorArray(channel);
      mapper->SetScalarVisibility(true);
      mapper->SetScalarModeToUsePointFieldData();
      // mapper->SetColorModeToMapScalars();
      mapper->ScalarVisibilityOn();
      mapper->UseLookupTableScalarRangeOn();
    }
  }

  mapper->Update();

  // Create an actor
  vtkSmartPointer<vtkActor> newActor = vtkSmartPointer<vtkActor>::New();
  newActor->SetMapper(mapper);

  // Set actor properties
  newActor->GetProperty()->SetSpecular(0.2);
  newActor->GetProperty()->SetSpecularPower(200);
  newActor->GetProperty()->SetAmbient(0.2);

  return newActor;
}

vtkSmartPointer<vtkDataSetSurfaceFilter> ExtractSurface(vtkSmartPointer<vtkDataSet> input)
{
  vtkSmartPointer<vtkDataSetSurfaceFilter> surfaceFilter = vtkSmartPointer<vtkDataSetSurfaceFilter>::New(); // Crea un'istanza del filtro per la superficie
  surfaceFilter->SetInputData(input);                                                                       // Imposta il dataset di input
  surfaceFilter->Update();                                                                                  // Aggiorna il filtro
  return surfaceFilter;                                                                                     // Restituisce il filtro
}

void AddEmptyScalars(vtkSmartPointer<vtkPolyData> polyData)
{
  // Crea un array di scalari di tipo float
  vtkSmartPointer<vtkFloatArray> scalars = vtkSmartPointer<vtkFloatArray>::New();
  scalars->SetName("Solid color");
  for (vtkIdType i = 0; i < polyData->GetNumberOfPoints(); ++i) // Imposta tutti i punti a 0
  {
    if (i == 0)
    {
      vtkSmartPointer<vtkPoints> points = polyData->GetPoints();
      if (points && points->GetNumberOfPoints() > 0)
      {
        double p[3];                                                                                           // Array to hold the coordinates
        points->GetPoint(0, p);                                                                                // Get the coordinates of the first point
        std::cout << "First point coordinates: (" << p[0] << ", " << p[1] << ", " << p[2] << ")" << std::endl; // Print the coordinates S
      }
    }
    scalars->InsertNextValue(0); // Imposta il valore a 0
  }
  polyData->GetPointData()->SetScalars(scalars); // Aggiungi l'array di scalari al dataset
}

vtkSmartPointer<vtkSphereSource> CreateSphere(const std::tuple<double, double, double> &point_coordinates)
{
  vtkSmartPointer<vtkSphereSource> sphere = vtkSmartPointer<vtkSphereSource>::New();
  // sphere->SetRadius(1.5);
  // sphere->SetThetaResolution(18);
  // sphere->SetPhiResolution(18);
  sphere->SetRadius(4);
  sphere->SetThetaResolution(20);
  sphere->SetPhiResolution(20);
  sphere->SetCenter(std::get<0>(point_coordinates), std::get<1>(point_coordinates), std::get<2>(point_coordinates));
  sphere->GenerateNormalsOn();
  sphere->Update();
  return sphere;
}
/*vtkSmartPointer<vtkTextActor> CreateTextLabel(const std::string &text, double x, double y)
{
  vtkSmartPointer<vtkTextActor> label = vtkSmartPointer<vtkTextActor>::New();
  label->SetTextScaleModeToNone();
  label->GetTextProperty()->SetTextProperty(text.c_str()); // Imposta il testo
  label->GetPositionCoordinate()->SetCoordinateSystemToNormalizedDisplay();
  label->SetPosition(x, y); // Posizione della label
  label->GetTextProperty()->SetFontSize(24);
  label->GetTextProperty()->SetColor(1.0, 1.0, 1.0); // Colore bianco
  return label;
}*/
std::tuple<float, float, float, float> getUniversalVentricularCoordinatesFromCartesian(const std::tuple<float, float, float> &point, vtkDataSet *data)
{
  vtkNew<vtkKdTreePointLocator> kdTree;
  kdTree->SetDataSet(data);
  kdTree->BuildLocator();
  vtkNew<vtkIdList> result;

  double pointArray[3] = {std::get<0>(point), std::get<1>(point), std::get<2>(point)};
  kdTree->FindClosestNPoints(1, pointArray, result);
  vtkIdType point_ind = result->GetId(0);

  int lv_rv = static_cast<int>(data->GetPointData()->GetArray("Neibors for epi-base")->GetTuple1(point_ind));
  float apex_base = data->GetPointData()->GetArray("apex_base")->GetTuple1(point_ind);
  float lv_circle = data->GetPointData()->GetArray("circle2")->GetTuple1(point_ind);
  float rv_circle = data->GetPointData()->GetArray("RV_Circle")->GetTuple1(point_ind);
  float transversal_coord = data->GetPointData()->GetArray("transversal")->GetTuple1(point_ind);

  if (lv_rv == 0)
  {
    std::cout << "getUniversalVentricularCoordinates (lv_rv=0): " << apex_base << ":" << rv_circle << ":" << transversal_coord << ":" << lv_rv << ":" << std::endl;
    return std::make_tuple(apex_base, rv_circle, transversal_coord, lv_rv);
  }
  else
  {
    std::cout << "getUniversalVentricularCoordinates (lv_rv!=0): " << apex_base << ":" << rv_circle << ":" << transversal_coord << ":" << lv_rv << ":" << std::endl;
    return std::make_tuple(apex_base, lv_circle, transversal_coord, lv_rv);
  }
}
void OnMouseClick(vtkObject *caller, long unsigned int eventId, void *clientData, void *callData)
{
  vtkRenderWindowInteractor *interactor = static_cast<vtkRenderWindowInteractor *>(caller);
  int *clickPos = interactor->GetEventPosition();
  // Create a picker
  vtkSmartPointer<vtkCellPicker> picker = vtkSmartPointer<vtkCellPicker>::New();
  picker->Pick(clickPos[0], clickPos[1], 0, interactor->GetRenderWindow()->GetRenderers()->GetFirstRenderer());
  if (picker->GetActor())
  {
    double *pickedPosition = picker->GetPickPosition();
    std::string actorName = picker->GetActor()->GetObjectName().c_str();
    std::cout << "Clicked actor: " << actorName << " at position: (" << pickedPosition[0] << ", " << pickedPosition[1] << ", " << pickedPosition[2] << ")" << std::endl;

    std::tuple<float, float, float, float> result = {0, 0, 0, 0};
    if (actorName == "WHOLE_HEART")
    {
      result = getUniversalVentricularCoordinatesFromCartesian({pickedPosition[0], pickedPosition[1], pickedPosition[2]}, picker->GetActor()->GetMapper()->GetInput());
    }
    // std::string jsCode = "onCellClickEvent('" + actorName + "', " + std::to_string(pickedPosition[0]) + ", " + std::to_string(pickedPosition[1]) + ", " + std::to_string(pickedPosition[2]) + ", [" + std::get<0>(result) + "," + std::get<1>(result) + "," + std::get<2>(result) + "," + std::get<3>(result) + "]);";
    std::string jsCode = "onCellClickEvent('" + actorName + "', " +
                         std::to_string(pickedPosition[0]) + ", " +
                         std::to_string(pickedPosition[1]) + ", " +
                         std::to_string(pickedPosition[2]) + ", [" +
                         std::to_string(std::get<0>(result)) + ", " +
                         std::to_string(std::get<1>(result)) + ", " +
                         std::to_string(std::get<2>(result)) + ", " +
                         std::to_string(std::get<3>(result)) + "]);";
    emscripten_run_script(jsCode.c_str());
  }
}
vtkSmartPointer<vtkActor> FindActorByName(vtkRenderer *renderer, const std::string &name)
{
  for (int i = 0; i < renderer->GetActors()->GetNumberOfItems(); ++i)
  {
    vtkActor *actor = vtkActor::SafeDownCast(renderer->GetActors()->GetItemAsObject(i));
    if (actor && actor->GetObjectName() == name)
    {
      return actor; // Trovato l'attore
    }
  }
  return nullptr; // Attore non trovato
}
/*void SavePolyDataToBuffer(vtkPolyData *polyData, std::string &buffer)
{
  // Crea un writer per scrivere i dati in un buffer
  // vtkSmartPointer<vtkPolyDataWriter> writer = vtkSmartPointer<vtkPolyDataWriter>::New();
  vtkNew<vtkPolyDataWriter> writer;
  writer->SetInputData(polyData);
  writer->WriteToOutputStringOn(); // Abilita la scrittura su stringa
  writer->Write();                 // Scrivi i dati nel buffer
  // Ottieni la stringa di output
  const char *outputString = writer->GetOutputString();
  buffer = std::string(outputString); // Copia i dati nel buffer
}*/

class GeometryViewer::Internal
{
public:
  float ScrollSensitivity = 1.0;
  vtkNew<vtkScalarBarActor> ScalarBarActor;
  vtkNew<vtkRenderWindowInteractor> Interactor;
  vtkNew<vtkCameraOrientationWidget> CameraManipulator;
  vtkNew<vtkRenderWindow> Window;
  vtkNew<vtkRenderer> Renderer;
  vtkNew<vtkPolyData> heartData;
  vtkNew<vtkPolyData> csData;
  vtkNew<vtkPolyData> targetData;
  vtkNew<vtkPolyData> lvLeadData;
  vtkNew<vtkPolyData> rvLeadData;
  int MouseMoveObserverTag = -1;
};

GeometryViewer::GeometryViewer()
{
  this->P = std::unique_ptr<Internal>(new Internal());
  this->P->Interactor = vtkNew<vtkRenderWindowInteractor>();
  std::cout << __func__ << std::endl;
}

GeometryViewer::~GeometryViewer()
{
  std::cout << __func__ << std::endl;
  this->P->Interactor->TerminateApp();
}

void GeometryViewer::LoadHeartModelFromMemory(std::uintptr_t buffer, std::size_t nbytes)
{
  std::cout << __func__ << "(" << buffer << ',' << nbytes << ")" << std::endl;
  auto wrappedBuffer = vtk::TakeSmartPointer(vtkBuffer<char>::New());
  wrappedBuffer->SetBuffer(reinterpret_cast<char *>(buffer), nbytes);
  wrappedBuffer->SetFreeFunction(true);
  auto xmlReader = vtk::TakeSmartPointer(vtkXMLUnstructuredGridReader::New());
  xmlReader->SetReadFromInputString(true);
  vtkNew<vtkCharArray> wrappedArray;
  wrappedArray->SetArray(reinterpret_cast<char *>(buffer), nbytes, 1);
  xmlReader->SetInputArray(wrappedArray);
  auto geometryFilter = vtk::TakeSmartPointer(vtkGeometryFilter::New());
  geometryFilter->SetInputConnection(xmlReader->GetOutputPort());
  geometryFilter->Update();
  auto surface = ExtractSurface(geometryFilter->GetOutput());
  auto surfaceNormals = CalculateNormals(surface->GetOutput());
  auto poliData = surfaceNormals->GetOutput();
  AddEmptyScalars(poliData);
  auto sortedData = sortedPolyData(poliData, this->P->Renderer->GetActiveCamera());
  this->P->heartData->ShallowCopy(sortedData->GetOutput());
}

void GeometryViewer::LoadCoronaryModelFromMemory(const std::string &filename, std::uintptr_t buffer, std::size_t nbytes)
{
  std::cout << __func__ << "(" << buffer << ',' << nbytes << ")" << std::endl;
  using systools = vtksys::SystemTools;
  auto wrappedBuffer = vtk::TakeSmartPointer(vtkBuffer<char>::New());
  wrappedBuffer->SetBuffer(reinterpret_cast<char *>(buffer), nbytes);
  wrappedBuffer->SetFreeFunction(true);
  vtkNew<vtkMemoryResourceStream> stream;
  stream->SetBuffer(wrappedBuffer);

  vtkSmartPointer<vtkPolyData> poliData;
  if (systools::StringEndsWith(filename, ".obj"))
  {
    auto objreader = vtk::TakeSmartPointer(vtkOBJReader::New());
    objreader->SetStream(stream);
    objreader->Update();
    vtkSmartPointer<vtkGeometryFilter> geometryFilter = vtkSmartPointer<vtkGeometryFilter>::New();
    geometryFilter->SetInputConnection(objreader->GetOutputPort());
    poliData = objreader->GetOutput();
  }
  else if (systools::StringEndsWith(filename, ".ply"))
  {
    auto plyreader = vtk::TakeSmartPointer(vtkPLYReader::New());
    plyreader->SetReadFromInputStream(true);
    plyreader->SetStream(stream);
    plyreader->Update();
    vtkSmartPointer<vtkGeometryFilter> geometryFilter = vtkSmartPointer<vtkGeometryFilter>::New();
    geometryFilter->SetInputConnection(plyreader->GetOutputPort());
    poliData = plyreader->GetOutput();
  }
  if (poliData)
  {
    // dEmptyScalars(poliData);
    this->P->csData->ShallowCopy(poliData);
  }
}

void GeometryViewer::LoadVtkModelFromMemory(const std::string &filename, std::uintptr_t buffer, std::size_t nbytes, const std::string &actorname)
{
  std::cout << __func__ << "(" << buffer << ',' << nbytes << ")" << std::endl;
  using systools = vtksys::SystemTools;
  vtkSmartPointer<vtkPolyData> poliData;
  if (systools::StringEndsWith(filename, ".vtk"))
  {
    auto polydataReader = vtk::TakeSmartPointer(vtkPolyDataReader::New());
    polydataReader->ReadFromInputStringOn();
    vtkNew<vtkCharArray> wrappedArray;
    wrappedArray->SetArray(reinterpret_cast<char *>(buffer), nbytes, 1);
    polydataReader->SetInputArray(wrappedArray);
    polydataReader->Update();
    poliData = polydataReader->GetOutput();
    AddEmptyScalars(poliData);
  }
  if (poliData)
  {
    if (actorname.rfind("LV_TARGET", 0) == 0)
    {
      this->P->lvLeadData->ShallowCopy(poliData);
      std::cout << actorname << ": lvLeadData" << std::endl;
    }
    else if (actorname.rfind("RV_TARGET", 0) == 0)
    {
      this->P->rvLeadData->ShallowCopy(poliData);
      std::cout << actorname << ": rvLeadData" << std::endl;
    }
    else if (actorname.rfind("LAST-ACT-TIME", 0) == 0)
    {
      this->P->targetData->ShallowCopy(poliData);
      std::cout << actorname << ": targetData" << std::endl;
    }
  }
}

/**
 *  Solo quando ho impostato sia heartData sia csData, allora posso fare una serie di operazioni .
 */
void GeometryViewer::AddActorsToRender()
{
  // WHOLE_HEART = 0,
  // CORONARY_SINUS = 1,
  // EPICARDIUM = 2,
  // LV_ENDOCARDIUM = 3,
  // RV_ENDOCARDIUM = 4,
  // FIBROSIS = 5
  //  La LUT di default.
  auto defaultLut = ColorMap::getClassicRainbow();

  auto heartActor = CreateActor(this->P->heartData, defaultLut);
  heartActor->SetObjectName("WHOLE_HEART");
  this->P->Renderer->AddActor(heartActor);

  auto csLut = ColorMap::getSolidColorMap(SOLID_COLORS[1]);
  auto csActor = CreateActor(this->P->csData, csLut, "Solid color");
  csActor->GetProperty()->SetOpacity(0.9);
  csActor->SetObjectName("CORONARY_SINUS");
  this->P->Renderer->AddActor(csActor);

  // LV_ENDO = 3; std::string HEART_REGIONS_NAME = "For LDBR";
  auto lv_endo_data = GetThreshold(this->P->heartData, 3, 3, HEART_REGIONS_NAME, true);
  auto lv_normals = CalculateNormals(lv_endo_data, false);
  auto lvActor = CreateActor(lv_normals->GetOutput(), defaultLut);
  lvActor->SetVisibility(false);
  lvActor->SetObjectName("LV_ENDOCARDIUM");
  this->P->Renderer->AddActor(lvActor);

  // RV_ENDO = 6; std::string HEART_REGIONS_NAME = "For LDBR";
  auto rv_endo_data = GetThreshold(this->P->heartData, 6, 6, HEART_REGIONS_NAME, true);
  auto rv_normals = CalculateNormals(rv_endo_data, false);
  auto rvActor = CreateActor(rv_normals->GetOutput(), defaultLut);
  rvActor->SetVisibility(false);
  rvActor->SetObjectName("RV_ENDOCARDIUM");
  this->P->Renderer->AddActor(rvActor);

  // EPI_SURFACE = 4, HEART_BASE = 5, HEART_APEX = 7 ; std::string HEART_REGIONS_NAME = "For LDBR";
  vtkSmartPointer<vtkPolyData> epi_surface = GetThreshold(this->P->heartData, 4, 4, HEART_REGIONS_NAME, true);
  vtkSmartPointer<vtkPolyData> heart_base_data = GetThreshold(this->P->heartData, 5, 5, HEART_REGIONS_NAME, true);
  vtkSmartPointer<vtkPolyData> heart_apex_data = GetThreshold(this->P->heartData, 7, 7, HEART_REGIONS_NAME, true);
  std::vector<vtkSmartPointer<vtkPolyData>> polydata_list = {epi_surface, heart_base_data, heart_apex_data};
  auto combined_data = appendPolyData(polydata_list);
  auto epi_normals = CalculateNormals(combined_data);
  auto epiActor = CreateActor(epi_normals->GetOutput(), defaultLut);
  epiActor->SetVisibility(false);
  epiActor->SetObjectName("EPICARDIUM");
  this->P->Renderer->AddActor(epiActor);

  if (this->P->targetData->GetNumberOfPoints() != 0)
  {
    auto targetActor = CreateActor(this->P->targetData, ColorMap::getSolidColorMap(SOLID_COLORS_MAP.at("WHITE")), "Solid color");
    targetActor->SetVisibility(true);
    targetActor->SetObjectName("LATE_ACT_POINT");
    this->P->Renderer->AddActor(targetActor);
    std::cout << "actor LATE_ACT_POINT from targetData " << std::endl;
  }

  if (this->P->lvLeadData->GetNumberOfPoints() != 0)
  {
    auto lvTargetActor = CreateActor(this->P->lvLeadData, ColorMap::getSolidColorMap(SOLID_COLORS_MAP.at("RED")), "Solid color");
    lvTargetActor->SetObjectName("LV_TARGET");
    lvTargetActor->SetVisibility(true);
    this->P->Renderer->AddActor(lvTargetActor);
    std::cout << "actor LV_TARGET from lvLeadData " << std::endl;
  }

  if (this->P->rvLeadData->GetNumberOfPoints() != 0)
  {
    auto rvTargetActor = CreateActor(this->P->rvLeadData, ColorMap::getSolidColorMap(SOLID_COLORS_MAP.at("BLUE")), "Solid color");
    rvTargetActor->SetObjectName("RV_TARGET");
    rvTargetActor->SetVisibility(true);
    this->P->Renderer->AddActor(rvTargetActor);
    std::cout << "actor RV_TARGET from rvLeadData " << std::endl;
  }

  this->P->ScalarBarActor->SetHeight(0.9);
  this->P->ScalarBarActor->SetWidth(.07);
  this->P->ScalarBarActor->SetPosition(0.02, 0.02);
  this->P->ScalarBarActor->SetNumberOfLabels(13);
  this->P->ScalarBarActor->SetLabelFormat("%3.0fms");
  this->P->ScalarBarActor->GetLabelTextProperty()->SetFontSize(2);
  this->P->ScalarBarActor->SetLookupTable(defaultLut);
  this->P->ScalarBarActor->SetObjectName("SCALAR_BAR");
  this->P->ScalarBarActor->SetMaximumNumberOfColors(13);

  this->P->Renderer->AddActor2D(this->P->ScalarBarActor);
}

void GeometryViewer::SetActorVisibility(std::string actorName, bool visible)
{
  std::cout << __func__ << "(" << actorName << "," << visible << ")" << std::endl;
  auto actor = FindActorByName(this->P->Renderer, actorName);
  if (actor)
  {
    std::cout << "Attore trovato: " << actor->GetObjectName() << std::endl;
    actor->SetVisibility(visible); // Set visibility
    actor->Modified();

    // FIXME: mostra/nascondi scalarbar...
    // La scalarBar la mostro solo se ho attivato il cuore intero e non altro.
    /*if (index == 0)
    {
      this->P->ScalarBarActor->SetVisibility(visible);
    }
    else if (visible)
    {
      this->P->ScalarBarActor->SetVisibility(false);
    }*/

    this->P->Renderer->GetRenderWindow()->Render();
  }
}

void GeometryViewer::SetActorOpacity(std::string actorName, double opacity)
{
  std::cout << __func__ << "(" << actorName << "," << opacity << ")" << std::endl;
  auto actor = FindActorByName(this->P->Renderer, actorName);
  if (actor)
  {
    std::cout << __func__ << " actor found: " << actor->GetObjectName() << std::endl;
    actor->GetProperty()->SetOpacity(opacity); // Set visibility
    actor->Modified();
    this->P->Renderer->GetRenderWindow()->Render();
  }
}

void GeometryViewer::SetActorMapName(std::string actorName, std::string mapName)
{
  std::cout << __func__ << "(" << actorName << "," << mapName << ")" << std::endl;
  auto actor = FindActorByName(this->P->Renderer, actorName);
  if (actor)
  {
    std::cout << __func__ << " actor found " << actor->GetObjectName() << std::endl;
    auto mapper = actor->GetMapper();
    auto poliData = mapper->GetInput();
    if (auto *dataSet = vtkDataSet::SafeDownCast(poliData))
    {
      // luts_list: list = [
      //   solid_lut,
      //   rainbow_lut,
      //   rainbow_desaturated,
      //   rainbow_desaturated,
      //   rainbow_lut,
      //   cool_to_warm,
      //   cool_to_warm,
      //   cool_to_warm
      //]
      if (mapName == "AHA_new")
      {
        mapper->SetLookupTable(ColorMap::getRainbowDesaturated());
      }
      else if (mapName == "Solid color")
      {
        if (actorName == "CORONARY_SINUS")
        {
          mapper->SetLookupTable(ColorMap::getSolidColorMap(SOLID_COLORS[1]));
        }
        else
        {
          mapper->SetLookupTable(ColorMap::getSolidColorMap(SOLID_COLORS[0]));
        }
      }
      else if (mapName == "Activation_time")
      {
        // Gestisci il caso in cui non viene trovato un lookup table
        // std::cerr << "Lookup table non trovata per il nome: " << mapName << std::endl;
        mapper->SetLookupTable(ColorMap::getClassicRainbow());
      }
      std::cout << "Processing vtkDataSet... searching for: " << mapName << std::endl; // Log per il dataset
      vtkDataArray *data_array = dataSet->GetPointData()->GetScalars(mapName.c_str());
      if (data_array)
      {
        std::cout << "mapper->SetScalarRange -> found:" << mapName << std::endl;
        mapper->SetScalarRange(data_array->GetRange());
        mapper->SelectColorArray(mapName.c_str());
        mapper->SetScalarVisibility(true);
        mapper->SetScalarModeToUsePointFieldData();
        mapper->SetColorModeToMapScalars();
        mapper->ScalarVisibilityOn();
        mapper->SetInterpolateScalarsBeforeMapping(1);
      }
      else
      {
        std::cout << "mapper->SetScalarRange -> NOT FOUND !!!" << mapName << std::endl;
        // mapper->SelectColorArray(channel);
        mapper->SetScalarVisibility(true);
        mapper->SetScalarModeToUsePointFieldData();
        // mapper->SetColorModeToMapScalars();
        mapper->ScalarVisibilityOn();
        mapper->UseLookupTableScalarRangeOn();
      }
    }
    mapper->Update();
    this->P->Renderer->GetRenderWindow()->Render();
  }
}

void GeometryViewer::AddNamedActor(std::string actorName, double x, double y, double z)
{
  auto newSphere = CreateSphere({x, y, z});
  auto poliData = newSphere->GetOutput();
  AddEmptyScalars(poliData);
  auto newActor = CreateActor(poliData, ColorMap::getSolidColorMap(SOLID_COLORS_MAP.at("BLUE")), "Solid color");
  newActor->SetObjectName(actorName);
  this->P->Renderer->AddActor(newActor);
}

void GeometryViewer::SetTargetPosition(std::string actorName, double x, double y, double z)
{

  auto actor = FindActorByName(this->P->Renderer, actorName);
  if (actor)
  {
    std::cout << __func__ << " Actor:" << actorName << " FOUND & REMOVED !!" << std::endl;
    this->P->Renderer->RemoveActor(actor);
  }
  try
  {
    auto lut = ColorMap::getSolidColorMap(SOLID_COLORS_MAP.at("WHITE"));
    auto newSphere = CreateSphere({x, y, z});
    auto poliData = newSphere->GetOutput();
    AddEmptyScalars(poliData);
    if (actorName.rfind("LV_TARGET", 0) == 0)
    {
      lut = ColorMap::getSolidColorMap(SOLID_COLORS_MAP.at("RED"));
    }
    else if (actorName.rfind("RV_TARGET", 0) == 0)
    {
      lut = ColorMap::getSolidColorMap(SOLID_COLORS_MAP.at("BLUE"));
    }
    auto newActor = CreateActor(poliData, lut, "Solid color");
    newActor->SetObjectName(actorName);
    this->P->Renderer->AddActor(newActor);
  }
  catch (const std::exception &e)
  {
    std::cerr << "Errore Creando nuovo attore: " << e.what() << std::endl;
  }
}
// Funzione per convertire un array di byte in Base64
std::string EncodeBase64(const uint8_t *data, size_t length)
{
  static const char *base64_chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
      "abcdefghijklmnopqrstuvwxyz"
      "0123456789+/";
  std::string result;
  int i = 0;
  int j = 0;
  unsigned char char_array_3[3];
  unsigned char char_array_4[4];
  while (length--)
  {
    char_array_3[i++] = *(data++);
    if (i == 3)
    {
      char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
      char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
      char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
      char_array_4[3] = char_array_3[2] & 0x3f;

      for (i = 0; (i < 4); i++)
        result += base64_chars[char_array_4[i]];
      i = 0;
    }
  }

  if (i)
  {
    for (j = i; j < 3; j++)
      char_array_3[j] = '\0';

    char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
    char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
    char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
    char_array_4[3] = char_array_3[2] & 0x3f;

    for (j = 0; (j < i + 1); j++)
      result += base64_chars[char_array_4[j]];

    while ((i++ < 3))
      result += '=';
  }
  return result;
}
// Funzione per leggere i dati di un attore e restituire un buffer binario
std::string GeometryViewer::GetActorDataAsBase64(const std::string &actorName)
{
  std::string base64String;
  auto actor = FindActorByName(this->P->Renderer, actorName);
  if (actor)
  {
    auto mapper = actor->GetMapper();
    auto polyData = mapper->GetInput();
    vtkNew<vtkPolyDataWriter> writer;                     // Crea un writer per scrivere i dati in un buffer
    writer->SetInputData(polyData);                       // Recupero il polyData
    writer->WriteToOutputStringOn();                      // Abilita la scrittura su stringa
    writer->Write();                                      // Scrivi i dati nel buffer
    const char *outputString = writer->GetOutputString(); // Ottieni la stringa di output
    size_t length = writer->GetOutputStringLength();
    base64String = EncodeBase64(reinterpret_cast<const uint8_t *>(outputString), length);
  }
  return base64String; // Restituisce il buffer come vettore
}

void GeometryViewer::Initialize(const std::string &canvasName)
{
  std::cout << __func__ << std::endl;
  this->P->Renderer->GradientBackgroundOn();
  this->P->Renderer->SetGradientMode(vtkRenderer::GradientModes::VTK_GRADIENT_RADIAL_VIEWPORT_FARTHEST_CORNER);
  //  create the default renderer
  this->P->Window->SetWindowName(canvasName.c_str());
  this->P->Window->AddRenderer(this->P->Renderer);
  this->P->Window->SetInteractor(this->P->Interactor);
  this->P->Window->SetSize(30, 30);
  this->P->Window->SetMultiSamples(0);
  this->P->Window->SetAlphaBitPlanes(1);
  auto iren = vtkWebAssemblyRenderWindowInteractor::SafeDownCast(this->P->Interactor);
  iren->SetCanvasSelector(canvasName.c_str()); //"#vtk-3d-canvas");
  if (auto wasmWebGPURenderWindow = vtkWebAssemblyWebGPURenderWindow::SafeDownCast(this->P->Window))
  {
    std::cout << __func__ << " WebGPU Render Window" << std::endl;
    wasmWebGPURenderWindow->SetCanvasSelector(canvasName.c_str()); //"#vtk-3d-canvas");
  }
  if (auto wasmOpenGLRenderWindow = vtkWebAssemblyOpenGLRenderWindow::SafeDownCast(this->P->Window))
  {
    std::cout << __func__ << " OpenGL Render Window" << std::endl;
    wasmOpenGLRenderWindow->SetCanvasSelector(canvasName.c_str()); //"#vtk-3d-canvas");
    // wasmOpenGLRenderWindow->SetFullScreen(false);
  }
  //vtkNew<vtkAxesActor> axes;
  //this->P->Renderer->AddActor(axes);
  // turn on camera manipulator
  this->P->CameraManipulator->SetParentRenderer(this->P->Renderer);
  this->P->CameraManipulator->SetAnimate(false);
  vtkCameraOrientationRepresentation::SafeDownCast(this->P->CameraManipulator->GetRepresentation())->AnchorToLowerRight();
  this->P->CameraManipulator->On();

  iren->InteractorManagesTheEventLoop = false;
  // do not simulate infinite loop.
  vtkRenderWindowInteractor::InteractorManagesTheEventLoop = false;
  // set the current style to TrackBallCamera. Default is joystick
  if (auto iStyle = vtkInteractorStyle::SafeDownCast(this->P->Interactor->GetInteractorStyle()))
  {
    if (auto switchStyle = vtkInteractorStyleSwitch::SafeDownCast(iStyle))
    {
      switchStyle->SetCurrentStyleToTrackballCamera();
    }
  }
  this->SetMouseWheelMotionFactor(0.15);

  // this->P->Renderer->SetUseDepthPeeling(1);
  this->P->Renderer->SetOcclusionRatio(0.1);
  // this->P->Renderer->SetMaximumNumberOfPeels(4);
  vtkSmartPointer<vtkCallbackCommand> callback = vtkSmartPointer<vtkCallbackCommand>::New();
  callback->SetCallback(OnMouseClick);
  iren->AddObserver(vtkCommand::LeftButtonPressEvent, callback);

  /*vtkNew<vtkCameraOrientationWidget> camOrientationManipulator;
  camOrientationManipulator->SetParentRenderer(this->P->Renderer);
  camOrientationManipulator->On();
  camOrientationManipulator->EnabledOn();*/
}

void GeometryViewer::Render() { this->P->Window->Render(); }

void GeometryViewer::ResetView(double zoom)
{
  std::cout << __func__ << std::endl;
  auto ren = this->P->Window->GetRenderers()->GetFirstRenderer();
  if (ren != nullptr)
  {
    ren->ResetCamera();
    ren->GetActiveCamera()->Zoom(zoom);
  }
}

void GeometryViewer::SetSize(int width, int height)
{
  std::cout << __func__ << width << ',' << height << std::endl;
  this->P->Interactor->UpdateSize(width, height);
}

void GeometryViewer::SetPosition(int x, int y, int z)
{
  std::cout << __func__ << x << ',' << y << ',' << z << std::endl;
  this->P->Renderer->GetActiveCamera()->SetPosition(x, y, z);
}

void GeometryViewer::SetViewUp(int x, int y, int z)
{
  std::cout << __func__ << x << ',' << y << ',' << z << std::endl;
  this->P->Renderer->GetActiveCamera()->SetViewUp(x, y, z);
}
//------------------------------------------------------------------------------
void GeometryViewer::Start()
{
  std::cout << __func__ << std::endl;
  this->P->Renderer->ResetCamera();
  this->P->Window->Render();
  this->P->Interactor->Start();
}
//------------------------------------------------------------------------------
void GeometryViewer::Halt() { emscripten_pause_main_loop(); }
//------------------------------------------------------------------------------
void GeometryViewer::Resume() { emscripten_resume_main_loop(); }
//------------------------------------------------------------------------------
void GeometryViewer::SetMouseWheelMotionFactor(float sensitivity)
{
  std::cout << __func__ << "(" << sensitivity << ")" << std::endl;
  if (auto iStyle = vtkInteractorStyle::SafeDownCast(this->P->Interactor->GetInteractorStyle()))
  {
    if (auto switchStyle = vtkInteractorStyleSwitch::SafeDownCast(iStyle))
    {
      switchStyle->GetCurrentStyle()->SetMouseWheelMotionFactor(sensitivity);
    }
    else
    {
      iStyle->SetMouseWheelMotionFactor(sensitivity);
    }
  }
  this->P->ScrollSensitivity = sensitivity;
}