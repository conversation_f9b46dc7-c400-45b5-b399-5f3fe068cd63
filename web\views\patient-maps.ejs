<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("patient.heading") %>
    <% icon = "fa-solid fa-user" %>
    <%- include('components/head') %>
  </head>
  <body class="h-screen">
    <%- include('components/header') %>
    <div class="flex flex-row items-center gap-x-12 w-full text-white px-4 pb-4">
      <!-- Edit and Delete buttons styled to match the UI -->
      <button class="flex-1 bg-zinc-700 hover:bg-zinc-600 text-white font-bold py-2 px-4 rounded flex items-center justify-center gap-2" onclick="window.location.href='/patients/<%=patient.id%>/edit'">
        <i class="fa-solid fa-pen"></i>
        <%= t("patient.edit") %>
      </button>
      <button class="flex-1 bg-red-700 hover:bg-red-600 text-white font-bold py-2 px-4 rounded flex items-center justify-center gap-2" onclick="deletePatient()">
        <i class="fa-solid fa-trash"></i>
        <%= t("patient.delete") %>
      </button>
    </div>
    <!-- main content fill entire height -->
    <main class="h-full w-full overflow-y-auto">
      <div class="h-full w-full overflow-x-hidden bg-zinc-800">
      <!-- Tabs -->
      <div class="flex space-x-2 border-t border-gray-700">
        <a href="/patients/<%=patient.id %>">
          <button class="tab-link px-8 py-4 text-3xl font-semibold text-gray-300 border-t-2 border-transparent hover:text-white" data-tab="activationMaps">
            <%= t("resources.heading") %> 
          </button>
        </a>
        <button class="tab-link px-8 py-4 text-3xl font-semibold text-white bg-zinc-700 border-t-2 border-darkred" data-tab="resources">
          <%= t("activationMaps.heading") %>
        </button>
      </div>
      <!-- FINE Tabs -->
      <div>&nbsp;<!-- spacer per distaccare da tabs --></div>
      <!-- Add new - Card -->
      <div class="flex flex-wrap gap-6 justify-center content-center items-center">
        <a href="#" id="addMapButton" class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between cursor-pointer overflow-hidden" data-id="">
          <div class="bg-[#2c2c2c] rounded-t-lg text-center">
            <div class="font-semibold uppercase tracking-wide p-2 text-3xl text-gray-200">&nbsp;</div>
          </div>
          <div class="text-sm leading-tight flex flex-col justify-between text-gray-300">
            <p class="text-center"><i class="fa-solid fa-plus text-6xl py-2"></i></p>
            <p class="text-center text-3xl"><%=t('activationMaps.addNew')%></p>
          </div>
          <div class="flex justify-end m-2"></div>
        </a>
        <% if (list && list.length > 0) { %> <% list.forEach(resource => { %>
          <a href="/patients/<%=patient.id%>/patient-map?mapId=<%=resource.idMap%>&title=<%=resource.mapDescription%>" >
          <div class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between cursor-pointer overflow-hidden"
            data-id="<%= resource.id %>">
            <div class="bg-[#2c2c2c] rounded-t-lg text-center">
              <div class="font-semibold uppercase tracking-wide p-2 text-gray-200 text-xl"><%= resource.mapDescription %></div>
            </div>
            <div class="flex flex-col text-3xl text-white p-3">
                <div class="flex justify-between">
                  <span class="font-bold ">CT Scan:</span>  
                  <div class="flex justify-end"><%= locDateTime(resource.resourceDateTc) %></div> 
                </div>
                <div class="flex justify-end pt-2">
                  <%- include('./components/badge-text', {
                    badgeColor: resource.resourceTagTc === 'before' ? 'bg-green-500' : resource.resourceTagTc === 'after' ? 'bg-yellow-500' : 'bg-red-500', 
                    badgeText: t('badge.' + resource.resourceTagTc)
                    }) %>
                </div>

                <div class="flex justify-between pt-8">
                  <span class="font-bold ">ECG:</span>  
                  <div class="flex justify-end"><%= locDateTime(resource.resourceDateEcg) %></div> 
                </div>
                <div class="flex justify-end pt-2">
                  <%- include('./components/badge-text', {
                    badgeColor: resource.resourceTagEcg === 'before' ? 'bg-green-500' : resource.resourceTagEcg === 'after' ? 'bg-yellow-500' : 'bg-red-500', 
                    badgeText: t('badge.' + resource.resourceTagEcg)
                    }) %>
                </div>
            </div>
            <!-- only border top 1px white -->
            <div class="flex justify-center py-2 border-t border-darkgray">
              <%- include('./components/badge-text', {
                    badgeColor: resource.mapTag === 'before' ? 'bg-green-500' : resource.mapTag === 'after' ? 'bg-yellow-500' : 'bg-red-500', 
                    badgeText: t('badge.' + resource.mapTag)
                    }) %>
            </div>
          </div>
          </a>
        <% }); } %> 
      </div>
      </div>
    </main>
    <%- include('components/add-activation-map-dialog') %>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card) => {
          if (card.dataset.id == '') return;
          card.addEventListener('click', () => {
            top.location.href = 'patient-map?resid=' + card.dataset.id;
          });
        });
        
        const addMapButton = document.getElementById('addMapButton');
        addMapButton.addEventListener('click', () => {
          const dialog = document.getElementById('activationMapDialog');
          dialog.showModal();
        });

      });
     function closeDialog() {
        const dialog = document.getElementById('activationMapDialog');
        dialog.close();
     }
    </script>
  </body>
</html>
