<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("patient.heading") %> <% icon = "fa-solid fa-user" %> <%- include('components/head') %>
    <style>
    </style>
  </head>
  <body class="h-screen">
    <%- include('components/header') %>
    <%- include('components/patient-btns-editdelete') %>
    <%- include('components/patient-tabs') %>
    <!-- main content fill entire height -->
    <main class="h-full w-full overflow-y-auto">
      <div class="h-full w-full overflow-x-hidden bg-zinc-800">
        <div>&nbsp;<!-- spacer per distaccare da tabs --></div>
        <!-- Add new - Card -->
        <div class="flex flex-wrap gap-6 justify-center content-center items-center">
          <a href="#" id="addMapButton"
            class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between cursor-pointer overflow-hidden"
            data-id="">
            <div class="bg-[#2c2c2c] rounded-t-lg text-center">
              <div class="font-semibold uppercase tracking-wide p-2 text-3xl text-gray-200">&nbsp;</div>
            </div>
            <div class="text-sm leading-tight flex flex-col justify-between text-gray-300">
              <p class="text-center"><i class="fa-solid fa-plus text-6xl py-2"></i></p>
              <p class="text-center text-3xl"><%=t('activationMaps.addNew')%></p>
            </div>
            <div class="flex justify-end m-2"></div>
          </a>
          <% if (list && list.length > 0) { %> <% list.forEach(map => { %>
            <div
              class="card w-[480px] h-[300px] rounded-lg bg-[#3a3a3a] font-sans flex flex-col justify-between overflow-hidden"
              data-id="<%= map.id %>">
              <div class="bg-[#2c2c2c] rounded-t-lg flex items-center justify-between p-1">
                <div class="font-semibold uppercase tracking-wide p-2 text-gray-200 text-xl">
                  <span class="mr-2"><%= map.description %></span>
                </div>
                <div class="px-3">
                  <button onclick="" class="text-white hover:text-gray-300">
                    <i class="fa-solid fa-ellipsis-vertical text-2xl"></i>
                  </button>
                </div>
              </div>
              <a href="/patients/<%=patient.id%>/patient-map?mapId=<%=map.id%>&title=<%=map.description%>" 
                <% if (map.status == -1) { %>
                  onclick="event.preventDefault();"
                <% } %>
                class="cursor-pointer">
              <div class="flex flex-col flex-grow text-3xl text-white p-3">
                <div class="flex justify-between">
                  <span class="font-bold">CT Scan:</span>
                  <div class="flex justify-end"><%= locDateTime(map.tcInfo.resourceDate) %></div>
                </div>
                <div class="flex justify-end pt-2">
                  <%- include('./components/badge-text', { badgeColor: map.tcInfo.resourceTag === 'before' ? 'bg-green-500' : map.tcInfo.resourceTag === 'after'
                  ? 'bg-yellow-500' : 'bg-red-500', badgeText: t('badge.' + map.tcInfo.resourceTag) }) %>
                </div>

                <div class="flex justify-between pt-8">
                  <span class="font-bold">ECG:</span>
                  <div class="flex justify-end"><%= locDateTime(map.ecgInfo.resourceDate) %></div>
                </div>
                <div class="flex justify-end pt-2">
                  <%- include('./components/badge-text', { badgeColor: map.ecgInfo.resourceTag === 'before' ? 'bg-green-500' : map.ecgInfo.resourceTag ===
                  'after' ? 'bg-yellow-500' : 'bg-red-500', badgeText: t('badge.' + map.ecgInfo.resourceTag) }) %>
                </div>
              </div>
              <!-- TODO: mettere loading indicator + evento sulla pagina -->
              <!-- if resource.mapStatus == -1 then show loaading indicator else show the div -->
               <% if (map.status == -1) { %>
                <div class="flex justify-center align-center py-2 border-t border-darkgray gap-2">
                  <div class="loading-spinner-small animate-spin rounded-full border-t-darkred border-solid w-8 h-8"></div>
                  <span class="ml-2 text-gray-300"><%= t('activationMaps.processing') %></span>
                </div>
              <% } else { %>
                <div class="flex justify-center py-2 border-t border-darkgray">
                  <%- include('./components/badge-text', { badgeColor: map.mapTag === 'before' ? 'bg-green-500' : map.mapTag === 'after' ?
                  'bg-yellow-500' : 'bg-red-500', badgeText: t('badge.' + map.mapTag) }) %>
                </div>
              <% } %>
              </a>
            </div>
          <% }); } %>
        </div>
      </div>
    </main>
    <%- include('components/add-activation-map-dialog') %>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const cards = document.querySelectorAll(".card");
        cards.forEach((card) => {
          if (card.dataset.id == "") return;
          card.addEventListener("click", () => {
            top.location.href = "patient-map?resid=" + card.dataset.id;
          });
        });

        const addMapButton = document.getElementById("addMapButton");
        addMapButton.addEventListener("click", () => {
          const dialog = document.getElementById("activationMapDialog");
          dialog.showModal();
        });
      });
      function closeDialog() {
        const dialog = document.getElementById("activationMapDialog");
        dialog.close();
      }
      // Aggiungi un listener per l'evento job-completed
      window.addEventListener("job-completed", (event) => {
          const data = event.detail; // Ottieni i dati dall'evento
          // Controlla se il stage è 'simulation'
          if (data.stage === 'actmap_stage') {
            window.location.reload(true);
          }
      });
    </script>
  </body>
</html>
