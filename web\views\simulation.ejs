<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("viewer.heading") %> <% icon = "fa-solid fa-notes-medical" %> <%- include('components/head') %>
  </head>
  <body>
    <%- include('components/header') %>
    <main>
      <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
        <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
        <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
      </div>

      <div class="flex flex-col items-center justify-center w-full rounded-lg bg-darkgray p-2 mx-4">
        <div class="flex flex-row w-full text-white gap-x-2 px-2 py-2">
          <div class="flex flex-grow">
            <p class="text-white text-5xl">NUOVA SIMULAZIONE</p>
          </div>
          <button id="hamburgerBtn" class="bg-darkred text-white px-4 py-2 rounded-lg flex items-center justify-center w-12 h-12">
            <i class="fa-solid fa-bars text-xl"></i>
          </button>
        </div>

        <!-- 3D Viewer -->
        <canvas id="vtk-3d-canvas" class="w-1/2 h-1/2 hidden"></canvas>

<div class="flex flex-col items-center justify-center w-full rounded-lg bg-darkgray p-4 mx-4">
  <div class="w-full text-white text-3xl font-semibold mb-2 px-2">Lead Settings</div>
  
  <div class="flex justify-around gap-6 p-4 rounded-lg text-white w-full">

    <!-- LV Lead -->
    <div class="flex flex-col gap-2">
      <div class="flex items-center gap-3">
        <input type="checkbox" id="lvTarget" name="lead" data-id="LV_TARGET" class="w-12 h-12 accent-darkred lead-toggle" />
        <label for="lvTarget" class="text-2xl font-semibold">LV Lead</label>
      </div>
      <div class="flex flex-wrap items-center gap-4 pt-2">
        <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
          <label for="lvx" class="text-xl">X</label>
          <input type="text" id="lvx" class="bg-gray-700 text-white w-36 text-center text-xl p-2 mb-4 rounded" />
        </div>
        <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
          <label for="lvy" class="text-xl">Y</label>
          <input type="text" id="lvy" class="bg-gray-700 text-white w-36 text-center text-xl p-2 mb-4 rounded" />
        </div>
        <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
          <label for="lvz" class="text-xl">Z</label>
          <input type="text" id="lvz" class="bg-gray-700 text-white w-36 text-center text-xl p-2 mb-4 rounded" />
        </div>
      </div>
    </div>

    <!-- RV Lead -->
    <div class="flex flex-col gap-2">
      <div class="flex items-center gap-3">
        <input type="checkbox" id="rvTarget" name="lead" data-id="RV_TARGET" class="w-8 h-8 accent-darkred lead-toggle" />
        <label for="rvTarget" class="text-lg">RV Lead</label>
      </div>
      <div class="flex flex-wrap items-center gap-4 pt-2">
        <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
          <label for="rvx">X</label>
          <input type="text" id="rvx" class="bg-gray-700 text-white w-20 text-center text-lg p-2 mb-4 rounded" />
        </div>
        <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
          <label for="rvy">Y</label>
          <input type="text" id="rvy" class="bg-gray-700 text-white w-20 text-center text-lg p-2 mb-4 rounded" />
        </div>
        <div class="flex flex-col-reverse border border-white rounded-lg p-2 justify-center items-center">
          <label for="rvz">Z</label>
          <input type="text" id="rvz" class="bg-gray-700 text-white w-20 text-center text-lg p-2 mb-4 rounded" />
        </div>
      </div>
    </div>
    <script>
      document.querySelectorAll('.lead-toggle').forEach(input => {
        input.addEventListener('change', function () {
          if (this.checked) {
            // Uncheck all others
            document.querySelectorAll('.lead-toggle').forEach(el => {
              if (el !== this) el.checked = false;
            });
          }
        });
      });
    </script>
  </div>
</div>

        <div id="hamburgerMenu" class="absolute top-80 right-4 bg-darkgray p-2 rounded-xl w-[300px] hidden">
          <div class="flex flex-col gap-2">
            <!-- close button -->
            <button onclick="hamburgerMenu.classList.add('hidden')" class="self-end text-white hover:text-gray-300">
              <i class="fa fa-times text-2xl"></i>
            </button>
            <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
              <div class="flex items-center gap-4">
                <input type="checkbox" id="ventricles" data-id="WHOLE_HEART" checked class="w-6 h-6 accent-darkred" />
                <span class="text-lg text-white">Ventricles</span>
              </div>
              <select id="ventriclesOptions" data-id="WHOLE_HEART" class="bg-darkgray-light text-white border border-gray-300 rounded p-1">
                <option value="Solid color">Anatomy</option>
                <option value="Activation_time" selected>Activation Map</option>
                <option value="AHA_new">AHA sectorization</option>
                <option value="For LDBR">Epi-endo surfaces</option>
                <option value="Neibors for epi-base">Neibors for epi-base</option>
              </select>
              <input type="range" id="ventriclesSlider" data-id="WHOLE_HEART" min="0" max="100" value="100" class="w-full" />
            </div>
            <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
              <div class="flex items-center gap-4">
                <input type="checkbox" id="coronarySinus" data-id="CORONARY_SINUS" checked class="w-6 h-6 accent-darkred" />
                <span class="text-lg text-white">Coronary Sinus</span>
              </div>
              <!-- Combobox -->
              <select id="coronarySinusOptions" data-id="CORONARY_SINUS" class="bg-darkgray-light text-white border border-gray-300 rounded p-1">
                <option value="Solid color">Anatomy</option>
                <option value="Normals">Normals</option>
              </select>
              <!-- Slider -->
              <input type="range" id="coronarySinusSlider" data-id="CORONARY_SINUS" min="0" max="100" value="90" class="w-full" />
            </div>
            <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white disabled">
              <div class="flex items-center gap-4">
                <input type="checkbox" id="epicardium" data-id="EPICARDIUM" class="w-6 h-6 accent-darkred" />
                <span class="text-lg text-white">Epicardium</span>
              </div>
              <!-- Combobox -->
              <select id="epicardiumOptions" data-id="EPICARDIUM" class="bg-darkgray-light text-white border border-gray-300 rounded p-1" style="display: none">
                <option value="Solid color">Anatomy</option>
                <option value="Activation_time" selected>Activation Map</option>
                <option value="AHA_new">AHA sectorization</option>
              </select>
              <!-- Slider -->
              <input type="range" id="epicardiumSlider" data-id="EPICARDIUM" min="0" max="100" value="100" class="w-full" style="display: none" />
            </div>

            <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
              <div class="flex items-center gap-4">
                <input type="checkbox" id="lvEndocardium" data-id="LV_ENDOCARDIUM" class="w-6 h-6 accent-darkred" />
                <span class="text-lg text-white">LV Endocardium</span>
              </div>
              <!-- Combobox -->
              <select
                id="lvEndocardiumOptions"
                data-id="LV_ENDOCARDIUM"
                class="bg-darkgray-light text-white border border-gray-300 rounded p-1"
                style="display: none"
              >
                <option value="Solid color">Anatomy</option>
                <option value="Activation_time" selected>Activation Map</option>
                <option value="AHA_new">AHA sectorization</option>
              </select>
              <!-- Slider -->
              <input type="range" id="lvEndocardiumSlider" data-id="LV_ENDOCARDIUM" min="0" max="100" value="100" class="w-full" style="display: none" />
            </div>

            <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
              <div class="flex items-center gap-4">
                <input type="checkbox" id="rvEndocardium" data-id="RV_ENDOCARDIUM" class="w-6 h-6 accent-darkred" />
                <span class="text-lg text-white">RV Endocardium</span>
              </div>
              <!-- Combobox -->
              <select
                id="rvEndocardiumOptions"
                data-id="RV_ENDOCARDIUM"
                class="bg-darkgray-light text-white border border-gray-300 rounded p-1"
                style="display: none"
              >
                <option value="Solid color">Anatomy</option>
                <option value="Activation_time" selected>Activation Map</option>
                <option value="AHA_new">AHA sectorization</option>
              </select>
              <!-- Slider -->
              <input type="range" id="rvEndocardiumSlider" data-id="RV_ENDOCARDIUM" min="0" max="100" value="100" class="w-full" style="display: none" />
            </div>
            <div class="flex flex-col gap-2 p-1 rounded-lg bg-darkgray-light text-white">
              <div class="flex items-center gap-4">
                <input type="checkbox" id="suggestedTargetPoints" data-id="LV_TARGET" class="w-6 h-6 accent-darkred" />
                <span class="text-lg text-white">Suggested Target Points</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <script src="/js/hamburger-menu.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const hamburgerBtn = document.getElementById("hamburgerBtn");
        const hamburgerMenu = document.getElementById("hamburgerMenu");
        hamburgerBtn.addEventListener("click", (event) => {
          hamburgerMenu.classList.toggle("hidden");
        });
      });
    </script>
    <script>
      window.MODELS = <%- resources %>;
      window.CANVAS_SIZE = {
        width: 1000,
        height: 800
      };
      let idx = 0;
      function onCellClickEvent(actor, x, y, z, ventricular) {
        console.log("onCellClickEvent received from C++: ", actor, x, y, z, ventricular);
        const rvTarget = document.getElementById("rvTarget");
        const lvTarget = document.getElementById("lvTarget");
        if (actor == "WHOLE_HEART") {
         if (rvTarget.checked) {
              // Logica per RV TARGET
              console.log("RV Target selected");
              window.setTargetPosition("RV_TARGET", x, y, z);
              document.getElementById("rvx").value = x;
              document.getElementById("rvy").value = y;
              document.getElementById("rvz").value = z;
          } else if (lvTarget.checked) {
              // Logica per LV TARGET
              console.log("LV Target selected");
              window.setTargetPosition("LV_TARGET", x, y, z);
              document.getElementById("lvx").value = x;
              document.getElementById("lvy").value = y;
              document.getElementById("lvz").value = z;
          }
        }
      }
    </script>
    <script src="/js/viewer.js" type="module"></script>
    <script type="module">
      // call init after viewer is loaded
      window.addEventListener("viewerLoaded", async () => {
        await window.viewerInit();
      });
    </script>
  </body>
</html>
