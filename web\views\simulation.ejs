<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("viewer.heading") %> <% icon = "fa-solid fa-notes-medical" %> <%- include('components/head') %>
  </head>
  <body>
    <%- include('components/header') %>
    <main>
      <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
        <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
        <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
      </div>
      <div class="flex flex-col w-full rounded-lg bg-darkgray p-1 m-1">
        <div class="flex flex-row w-full justify-between text-white p-2 m-1">
          <div class="flex flex-grow">
            <p class="text-2xl"><%=simulation.description%>&nbsp;-&nbsp;<%=locDateTime(simulation.createdAt)%></p>
          </div>
          <button id="hamburgerBtn" class="bg-darkred text-white px-4 py-2 rounded-lg flex items-center justify-center w-12 h-12">
            <i class="fa-solid fa-bars text-xl"></i>
          </button>
        </div>
      </div>
      <!-- 3D Viewer -->
      <canvas id="vtk-3d-canvas" class="w-1/2 h-1/2 hidden"></canvas>
      <script>
         const leadPoints = <%-simulation.data%>;
      </script>
      <div class="flex flex-col w-full rounded-lg bg-darkgray p-2 m-2">
        <div class="flex flex-row w-full justify-between text-white p-2 m-2">
          <div class="flex flex-col w-1/2">
            <p class="text-2xl">LV: </p>
            <p class="text-xl">axis:&nbsp;<%=simulation.leads["LV"]["axis"]%></p>
            <p class="text-xl">vent:&nbsp;<%=simulation.leads["LV"]["ventricular"]%></p>
          </div>
          <div class="flex flex-col w-1/2">
            <p class="text-2xl">RV:</p>
            <p class="text-xl">axis:&nbsp;<%=simulation.leads["RV"]["axis"]%></p>
            <p class="text-xl">vent:&nbsp;<%=simulation.leads["RV"]["ventricular"]%></p>
          </div>
        </div>
      </div>
      <div id="hamburgerMenu" class="absolute top-80 right-4 bg-darkgray p-2 rounded-xl w-[300px] hidden"><%-include("components/hamburger-options") %></div>
    </main>
    <script src="/js/hamburger-menu.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const hamburgerBtn = document.getElementById("hamburgerBtn");
        const hamburgerMenu = document.getElementById("hamburgerMenu");
        hamburgerBtn.addEventListener("click", (event) => {
          hamburgerMenu.classList.toggle("hidden");
        });
      });
    </script>
    <script>
      window.MODELS = <%- resources %>;
      window.CANVAS_SIZE = {
        width: 1000,
        height: 800
      };
    </script>
    <script src="/js/vtk-viewer.js" type="module"></script>
    <script>
      const loadingScreenDiv = document.getElementById("loadingScreen");
      const vtkContainer = document.getElementById("vtk-3d-canvas");
      function onCellClickEvent(a, b, c, d, e) {}
      let viewer;
      let canvas_width = (window.CANVAS_SIZE && window.CANVAS_SIZE.width) || undefined;
      let canvas_height = (window.CANVAS_SIZE && window.CANVAS_SIZE.height) || undefined;
      if (canvas_height && canvas_width) {
        vtkContainer.width = canvas_width;
        vtkContainer.height = canvas_height;
        vtkContainer.style.width = canvas_width + "px";
        vtkContainer.style.height = canvas_height + "px";
      }
      window.addEventListener("ModuleLoaded", async () => {
        viewer = new VtkViewer("#vtk-3d-canvas", { models: window.MODELS });
        await viewer.viewerInit();
        viewer.setSize(canvas_width, canvas_height);
        await viewer.refresh();

        if (leadPoints.RV) {
          const points = leadPoints.RV.axis.split(',');
          viewer.setTargetPosition("RV_TARGET", points[0], points[1], points[2]);
        }
        if (leadPoints.LV) {
          const points = leadPoints.LV.axis.split(',');
          viewer.setTargetPosition("LV_TARGET", points[0], points[1], points[2]);
        }

        if (loadingScreenDiv) {
          loadingScreenDiv.classList.add("hidden");
        }
        if (vtkContainer.classList) {
          vtkContainer.classList.remove("hidden");
        }
      });
    </script>
  </body>
</html>
