<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = t("viewer.heading") %> <% icon = "fa-solid fa-notes-medical" %> <%- include('components/head') %>
  </head>
  <body class="h-screen">
    <%- include('components/header') %>
    <!-- Simulation notes -->
    <div class="flex items-left w-full m-1">
      <div class="flex flex-col items-center justify-center w-full rounded-lg bg-darkgray p-2 mx-4">
        <div class="flex flex-row w-full text-white gap-x-2 px-2 py-2">
          <div class="flex flex-grow">
            <p class="text-2xl"><%=simulation.description%>&nbsp;-&nbsp;<%=locDateTime(simulation.createdAt)%></p>
          </div>
          <div class="flex flex-1 items-center justify-end">
            <button id="hamburgerBtn" class="bg-darkred text-white px-4 py-2 rounded-lg flex items-center justify-center w-12 h-12">
              <i class="fa-solid fa-bars text-xl"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 3D Viewer -->
    <div class="flex w-full justify-center items-center">
      <canvas id="vtk-3d-canvas" class="w-1/2 h-1/2 hidden"></canvas>
    </div>
    <div id="statsDiv" class="flex flex-col items-left w-full p-1">
      <div class="flex flex-row w-full rounded-lg bg-darkgray items-center text-2xl p-2">
        <div class="flex flex-row w-1/2 justify-center p-2">
          <span>TAT:&nbsp;<span>
          <span id="TAT"></span>
        </div>
        <div class="flex flex-row w-1/2 justify-center p-2">
          <span>VEU:&nbsp;</span>
          <span id="VEU"></span>
        </div>
      </div>
      <br />
      <div class="flex flex-row w-full">
        <div class="flex flex-col items-center w-1/2 rounded-lg bg-darkgray p-2">
          <div class="flex flex-row w-full">
            <p class="text-2xl text-red-600">LV:</p>
          </div>
          <div class="flex flex-col w-full text-white text-xl p-1 m-1">
            <p class="flex justify-between">
              <span>AXIS:&nbsp;</span>
              <span><%=simulation.leads["LV"]["axis"]%></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-xl p-1 m-1">
            <p class="flex justify-between">
              <span>VENTR:&nbsp;</span>
              <span><%=simulation.leads["LV"]["ventricular"]%></span>
            </p>
          </div>
          <br/>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>LV AT FULL:&nbsp;</span>
              <span id="LV_AT_FULL"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>LV SD FULL:&nbsp;</span>
              <span id="LV_SD_FULL"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>LV MEAN FULL:&nbsp;</span>
              <span id="LV_MEAN_FULL"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>LV AT ENDO:&nbsp;</span>
              <span id="LV_AT_ENDO"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>LV SD ENDO:&nbsp;</span>
              <span id="LV_SD_ENDO"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>LV MEAN ENDO:&nbsp;</span>
              <span id="LV_MEAN_ENDO"></span>
            </p>
          </div>
        </div>
        <div class="flex flex-col items-center w-1/2 rounded-lg bg-darkgray p-2 mx-4">
          <div class="flex flex-row w-full">
            <p class="text-2xl text-blue-600">RV:</p>
          </div>
          <div class="flex flex-col w-full text-white text-xl p-1 m-1">
            <p class="flex justify-between">
              <span>AXIS:&nbsp;</span>
              <span><%=simulation.leads["RV"]["axis"]%></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-xl p-1 m-1">
            <p class="flex justify-between">
              <span>VENTR:&nbsp;</span>
              <span><%=simulation.leads["RV"]["ventricular"]%></span>
            </p>
          </div>
          <br/>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>RV AT FULL:&nbsp;</span>
              <span id="RV_AT_FULL"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>RV SD FULL:&nbsp;</span>
              <span id="RV_SD_FULL"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>RV MEAN FULL:&nbsp;</span>
              <span id="RV_MEAN_FULL"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>RV AT ENDO:&nbsp;</span>
              <span id="RV_AT_ENDO"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>RV SD ENDO:&nbsp;</span>
              <span id="RV_SD_ENDO"></span>
            </p>
          </div>
          <div class="flex flex-col w-full text-white text-2xl p-1 m-1">
            <p class="flex justify-between">
              <span>RV MEAN ENDO:&nbsp;</span>
              <span id="RV_MEAN_ENDO"></span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div id="hamburgerMenu" class="absolute top-80 right-4 bg-darkgray p-2 rounded-xl w-[300px] hidden">
      <%-include("components/hamburger-options") %></div>
    <div id="loadingScreen" class="fixed inset-0 bg-darkgray flex flex-col justify-center items-center z-50">
      <div class="loading-spinner animate-spin rounded-full border-t-darkred border-solid"></div>
      <div class="mt-4 text-2xl text-gray-300"><%= t("please_wait") %></div>
    </div>
    <script src="/js/hamburger-menu.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const hamburgerBtn = document.getElementById("hamburgerBtn");
        const hamburgerMenu = document.getElementById("hamburgerMenu");
        hamburgerBtn.addEventListener("click", (event) => {
          hamburgerMenu.classList.toggle("hidden");
        });
      });
    </script>
    <script src="/js/vtk-viewer.js" type="module"></script>
    <script>
      let viewer;
      const vtkModels = <%- resources %>;
      const leadPoints = <%-simulation.data%>;

      const loadingScreenDiv = document.getElementById("loadingScreen");
      const vtkContainer = document.getElementById("vtk-3d-canvas");

      //Necessaria la definizione ma qui non viene usata.
      function onCellClickEvent(a, b, c, d, e) {}

      function updateSpanValues(values = {}) {
        const container = document.getElementById("statsDiv");
        const spans = container.querySelectorAll("span");
        for (const key in values) {
          const spanElement = container.querySelector(`span#${key}`);
          if (spanElement) {
            spanElement.textContent = values[key];
          }
        }
      }

      async function setupStatistics() {
        if (!leadPoints.STATS) {
          const stats = await viewer.getStatistics();
          const jsonStats = JSON.parse(stats);
          leadPoints.STATS = jsonStats;
          console.log("STATISTICS:", jsonStats);
          const response = await fetch(`/patients/<%=patientId%>/simulation?simId=<%=simulation.id%>`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ STATS: jsonStats }),
          });
          console.log("POST response");
        }
        updateSpanValues(leadPoints.STATS);
      }
      function setupTargetPoints() {
        if (leadPoints.RV) {
          const points = leadPoints.RV.axis.split(",");
          viewer.setTargetPosition("RV_TARGET", points[0], points[1], points[2]);
        }
        if (leadPoints.LV) {
          const points = leadPoints.LV.axis.split(",");
          viewer.setTargetPosition("LV_TARGET", points[0], points[1], points[2]);
        }
      }

      window.addEventListener("ModuleLoaded", async () => {
        viewer = new VtkViewer("#vtk-3d-canvas", { models: vtkModels });
        await viewer.viewerInit();
        viewer.setSize(1050, 800);
        setupTargetPoints();
        await setupStatistics();
        await viewer.refresh();
        if (loadingScreenDiv) {
          loadingScreenDiv.classList.add("hidden");
        }
        if (vtkContainer.classList) {
          vtkContainer.classList.remove("hidden");
        }
      });
    </script>
  </body>
</html>
