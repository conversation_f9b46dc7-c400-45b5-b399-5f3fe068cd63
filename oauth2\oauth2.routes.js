const logger = require("medcommon/logger");

const crypto = require("crypto");

const oauth2Api = require("./oauth2.apis");

const { OAuthModel } = require("./oauth2.models");

const { spacedArray } = require("medcommon/utils");

const authRoutes = require("express").Router();

//#region ENDPOINTS STANDARD OAUTH2.0
/** EndPoint authorize */
authRoutes.route("/authorize").post(
  oauth2Api.oauth2UserAuthentication,
  // #swagger.summary = 'Endpoint standard OAUTH di autorizzazione client.'
  // #swagger.description = ''
  oauth2Api.oauth2Authorize,
);

// Check token or Sends back token
authRoutes
  .route("/token")
  .head(
    // #swagger.tags = ['OAUTH2 ENDPOINTS']
    // #swagger.summary = "Endpoint HEAD di verifica validità dell' access_token."
    // #swagger.description = 'Ritorna status 204 se il token è valido oppure l'errore del caso.'
    oauth2Api.oauth2Authenticate,
    function (req, res) {
      return res.status(204).end();
    }
  )
  .post(
    // #swagger.tags = ['OAUTH2 ENDPOINTS']
    // #swagger.summary = 'Endpoint standard OAUTH di rilascio access token.'
    // #swagger.description = 'La generazione del token avviene a seconda del grant-type'
    oauth2Api.oauth2Token
  );

//https://openid.net/specs/openid-connect-core-1_0.html#UserInfo
/** oauth.authenticate() fà:
 * 1) legge il token dal'header
 * 2) recupera l'utente dal token letto
 * 3) recupera il client dal token
 */
authRoutes.route("/userinfo").get(
  //Questo legge il token dagli headers e li convalida.
  // #swagger.security = [{ "MED_OAuth2": [] }]
  oauth2Api.oauth2Authenticate,
  // #swagger.tags = ['OAUTH2 ENDPOINTS']
  // #swagger.summary = 'Recupera tutti i dati utente del possessore del token'
  // #swagger.description = 'L'endpoint può essere utilizzato dal client per recuperare tutte le informazioni dell\'utente possessore del token.'
  async (req, res) => {
    res.header("Access-Control-Allow-Headers", "Access-Control-Allow-Headers");
    let userInfo = await new OAuthModel({ transaction: req.transation }).getUserFromToken(res.locals.oauth.token.accessToken);
    if (userInfo) {
      let toSend = {
        ...userInfo,
      };
      toSend.email = `${userInfo.username}@medarchiver.com`;
      logger.debug("userInfo: %O", toSend, { transaction: req.transation });
      res.status(200).send(toSend);
    } else {
      res.status(400).send({ error: "invalid_request" });
    }
  }
);

authRoutes.route("/revoke").post(
  //Questo legge il token dagli headers e li convalida.
  // #swagger.security = [{ "MED_OAuth2": [] }]
  oauth2Api.oauth2Authenticate,
  // #swagger.tags = ['OAUTH2 ENDPOINTS']
  // #swagger.summary = 'Revoca la validità di un token e relativo refresh_token passato in body.'
  // #swagger.description = "Utilizzare questo Endpoint dal client per revocare i token."
  async (req, res) => {
    let clientId = res.locals.oauth.token.client.id;
    let userId = res.locals.oauth.token.user.id;
    let { refresh_token, client_secret, client_id } = req.body;
    //Devo controllare che il refreshToken sia del client indicato
    let token = {
      refreshToken: refresh_token,
    };
    let result = await new OAuthModel({ transaction: req.transaction }).revokeToken(token);
    res.status(200).send({ result: result }).end();
  }
);
//#endregion

authRoutes.route("/clientinfo").get(
  //Questo legge il token dagli headers e li convalida.
  // #swagger.security = [{ "MED_OAuth2": [] }]
  oauth2Api.oauth2Authenticate,
  // #swagger.tags = ['OAUTH2 ENDPOINTS']
  // #swagger.summary = 'Recupera tutti le informazioni del client (applicazione) es.parametri di configurazione.'
  // #swagger.description = "Utilizzare questo Endpoint dal client per recuperare le varie configurazioni dell'applicazione."
  async (req, res) => {
    let clientId = res.locals.oauth.token.client.id;
    let oauthModel = new OAuthModel({ transaction: req.transaction });
    let client = await oauthModel.apiGetClient(clientId);
    let result = await oauthModel.apiGetClientConfigs(clientId);
    let toRet = {
      client_id: clientId,
      name: client.name,
      description: client.description,
      config: result,
    };
    res.status(200).send(toRet).end();
  }
);

authRoutes.route("/clientconfig").get(
  // #swagger.tags = ['OAUTH2 ENDPOINTS']
  // #swagger.summary = "Recupera i parametri di configurazione dell'applicazione con client_id fornito in queryString."
  // #swagger.description = "Utilizzare questo Endpoint dal client per recuperare le varie configurazioni dell'applicazione."
  async (req, res) => {
    let { client_id } = req.query;
    let oauthModel = new OAuthModel({ transaction: req.transaction });
    let result = await oauthModel.apiGetClientConfigs(client_id);
    let toRet = { config: result };
    res.status(200).send(toRet).end();
  }
);

/** Richiede un devicecode da utilizzare per autenticarsi in altre app. */
authRoutes.route("/devicecode").get(
  //Questo legge il token dagli headers e li convalida.
  // #swagger.security = [{ "MED_OAuth2": [] }]
  oauth2Api.oauth2Authenticate,
  // #swagger.tags = ['OAUTH2 ENDPOINTS']
  // #swagger.summary = 'Richiede un devicecode temporaneo da passare ad altre app che identifica utente.'
  // #swagger.description = "Utilizzare questo Endpoint dal client per generare un codice temporaneo da passare ad altre applicazioni."
  async (req, res) => {
    let userId = res.locals.oauth.token.user.id;
    let oauthModel = new OAuthModel({ transaction: req.transaction });
    let newCode = await oauthModel.createDeviceCode(userId);
    res.status(200).send({ deviceCode: newCode }).end();
  }
);

/** Converte il codice QrCode + client_id + scope che sono obbligatori,
 * in un authorization_code che può essere scambiato per un token con il metodo standard...
 */
authRoutes.route("/devicecode").post(
  // #swagger.tags = ['OAUTH2 ENDPOINTS']
  // #swagger.summary = "Endpoint 'custom' OAUTH di accesso con qrcode."
  // #swagger.description = 'Si aspetta i parametri in QUERYSTRING'
  // #swagger.consumes = ['text/html']
  // #swagger.produces = ['application/json']
  //Trasforma il qrcode in authorization_code ...
  async function (req, res, next) {
    let { client_id, qrcode, scope, redirect_uri } = req.query;
    let oauthModel = new OAuthModel({ transaction: req.transaction });
    let validCode = await oauthModel.getDeviceCode(qrcode);
    let client = await oauthModel.getClient(client_id, null);
    let authCode;
    if (validCode && client) {
      let newAuthCode = {
        authorizationCode: crypto.randomBytes(40).toString("hex"),
        scope: spacedArray(scope),
        codeChallenge: null,
        codeChallengeMethod: null,
        redirectUri: redirect_uri,
        expiresAt: new Date(Date.now() + 3 * 60 * 1000), //3 minuti ??
      };
      authCode = await oauthModel.saveAuthorizationCode(newAuthCode, client, validCode.user);
    }
    if (authCode) {
      await oauthModel.revokeDeviceCode(qrcode);
      oauthSocketIO.broadcastData(qrcode, { message: "Code reclaimed" });
      res.status(200).send({ code: authCode.authorizationCode }).end();
    } else {
      res.status(400).send({ error: "bad-request" }).end();
    }
  }
);

authRoutes.use(async function (req, res, next) {
  //Questa qui esiste perchè ho messo il continueMiddleware.....
  logger.debug("continueMiddleware oauthServer /api/oauth/*");
  if (res.statusCode == 302) {
    res.end();
  }
});

authRoutes.use(async function (error, req, res, next) {
  logger.error("Error middleware. %O", error, { error });
  res.status(error.status || 500).send({ error: { status: error.status, message: error.message } });
});

/*
authRoutes.post('/client', async(req,res) => {
  await model.createClientId(req,res);
});

authRoutes.post('/createDevice', async(req,res) => {
  await model.createDevice(req,res);
})
*/
authRoutes.get("/decrypt", async (req, res) => {
  await new OAuthModel({ transaction: req.transation }).decryptToken(req, res);
});

module.exports = authRoutes;
