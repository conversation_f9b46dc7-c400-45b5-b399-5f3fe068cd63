{"dateFormat": "MM/DD/YYYY", "pickDateFormat": "m/d/Y", "dateTimeFormat": "MM/DD/YYYY h:mm A", "dateTimeSecondsFormat": "MM/DD/YYYY h:mm:ss A", "firstName": "First Name", "lastName": "Last Name", "birthDate": "Date of Birth", "selectBirthDate": "Select Date of Birth", "age": "Age", "statusTag": "Status", "dropDownSelect": "Select...", "gender": "Gender", "male": "Male", "female": "Female", "ef": "EF (%)", "efRangeHint": "EF must be between 10 and 80", "esv": "ESV (mL)", "esvRangeHint": "ESV must be between 10 and 1000", "ctScan": "CT Scan", "ecg": "ECG", "cancel": "Cancel", "delete": "Delete", "confirm": "Confirm", "add": "Add", "saveChanges": "Save Changes", "please_wait": "Please wait...", "back": "Go Back", "about": "About", "logout_user": "Logout User", "lock_workstation": "Lock Workstation", "shutdown": "Shutdown System", "noResultsFound": "No patients found", "required": "required", "breadcrumbs": {"patient": {"search": "Search Patient", "selected": "Patient Record", "ecg": "ECG", "ctscan": "CT Scan", "new": "New Patient", "activationMap": "Activation Map"}}, "patientTabs": {"summary": "Summary", "resources": "Resources", "initialAssessment": "Initial Assessments", "simulations": "Simulations", "implantation": "Implantation"}, "login": {"heading": "N.I.C.E.", "username_label": "Username", "password_label": "Password", "button": "<PERSON><PERSON>", "error": "Invalid credentials. Please try again.", "empty_error": "This field is required."}, "postLoginWarning": {"heading": "N.I.C.E v%s", "message": "This version of N.I.C.E. is intended for demonstration purposes only.\n\nN.I.C.E. is a Medical Device designed for use by healthcare\nprofessionals, specifically cardiologists and cardiac\nelectrophysiologists.\n\nFor assistance, please contact: <EMAIL>\n\nBy selecting '%s' you confirm that you have read and understood the information provided.", "confirm": "I have read and understood the information provided"}, "home": {"heading": "Home", "patientList": "Patient List", "recentActivities": "Recent Activities", "settings": "Settings"}, "patientList": {"heading": "Patient List", "search": "Search Patient", "patientName": "Patient Name", "patientCode": "Patient Code", "noResultsFound": "No Results Found", "searchError": "Error during search", "searchInProgress": "Search in progress"}, "patient": {"heading": "Patient Record", "search": "Search...", "name": "Patient Name", "code": "Patient Code", "addNew": "Add New Patient", "edit": "<PERSON>", "delete": "Delete Patient Record", "addConfirmation": "Are you sure you want to add a new patient?", "deleteConfirmation": "Are you sure you want to delete this patient?"}, "badge": {"filterStatus": "Filter Status", "all": "All", "before": "Pre-procedure", "after": "Post-procedure", "follow_up": "Follow-up"}, "resources": {"heading": "Resources", "addNew": "Acquire New Resource", "resourceType": "Resource Type", "resourceStatus": "Resource Status", "resourceText": "Resource Text", "confirmAdd": "Confirm acquisition of new resource"}, "dicom": {"heading": "Dicom Viewer"}, "activationMaps": {"heading": "Activation Maps", "addNew": "Create Assessment", "selectInitialData": "Select ECG and CT", "confirmAdd": "Create New Assessment", "notes": "Notes", "addNotes": "Add notes here..."}, "viewer": {"heading": "Activation Map", "view_ap": "AP", "view_pa": "PA", "view_ll": "LL", "view_rl": "RL", "view_lo": "LO", "view_ro": "RO"}, "notFound": {"heading": "Error", "message": "This feature is not currently available.", "noPermission": "Access to this feature has been denied."}, "ecgPage": {"heading": "ECG"}, "newEcg": {"acquiring_message": "Aquiring ECG, please wait...", "confirm_button_text": "Save ECG", "discard_button_text": "Discard ECG"}, "simulation": {"new": "New Simulation", "start": "Start Simulation", "lead_settings": "Lead Settings", "lvLead": "LV Lead", "rvLead": "RV Lead", "notesPlaceholder": "Add notes here...", "processing": "Generation in progress...", "ready": "Simulation ready", "emptylist": "No simulations created"}}