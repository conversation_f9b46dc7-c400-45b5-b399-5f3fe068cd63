import createGeometryViewerModule from "/js/GeometryViewer.js";

const Module = await createGeometryViewerModule({
  preRun: [
    (runtime) => {
      if (runtime._setDefaultExpandVTKCanvasToContainer) {
        runtime._setDefaultExpandVTKCanvasToContainer(false);
      }
      if (runtime._setDefaultInstallHTMLResizeObserver) {
        runtime._setDefaultInstallHTMLResizeObserver(false);
      }
    },
  ],
  locateFile: (path) => path,
  print: (text) => console.log(text),
  printErr: (text) => console.error(text),
});

export class VtkViewer {
  constructor(canvas, options = {}) {
    //console.log("OK, johnny, we got a canvas...", canvas);
    this.canvas = canvas;
    this.models = options.models;
    this.viewer = new Module.GeometryViewer();
  }
  setSize(newWidth, newHeight) {
    const vtkContainer = document.querySelector(this.canvas);
    vtkContainer.width = newWidth;
    vtkContainer.height = newHeight;
    vtkContainer.style.width = newWidth + "px";
    vtkContainer.style.height = newHeight + "px";
    this.viewer.setSize(newWidth, newHeight);
  }
  refresh = async () => {
    await this.viewer.resetView(0.0);
    await this.viewer.render();
  };

  setCameraView = async (viewName) => {
    const posDelta = 100000;
    switch (viewName) {
      case "AP":
        this.viewer.setPosition(0, 0, posDelta);
        this.viewer.setViewUp(0, 1, 0);
        break;
      case "PA":
        this.viewer.setPosition(0, 0, posDelta * -1);
        this.viewer.setViewUp(0, 1, 0);
        break;
      case "LL":
        this.viewer.setPosition(posDelta, 0, 0);
        this.viewer.setViewUp(0, 1, 0);
        break;
    }
    this.viewer.resetView(0.9);
    //await viewer.render();
    await this.refresh();
  };

  setActorVisibility(index, visible) {
    this.viewer.setActorVisibility(index, visible);
  }
  setActorOpacity(index, value) {
    this.viewer.setActorOpacity(index, value);
  }
  setActorMapName(index, map) {
    this.viewer.setActorMapName(index, map);
  }
  setPosition(x, y, z) {
    this.viewer.setPosition(x, y, z);
  }

  addNamedActor(name, x, y, z) {
    this.viewer.addNamedActor(name, x, y, z);
  }

  removeNamedActor(name) {
    this.viewer.removeNamedActor(name);
  }

  setTargetPosition = function (name, x, y, z) {
    this.viewer.setTargetPosition(name, x, y, z);
  };

  getActorDataAsBase64 = function (actorName) {
    const result = this.viewer.getActorDataAsBase64(actorName);
    console.log("Result:", result);
    return result;
  };

  getActorScalarsJson = function (actorName, scalarName) {
    return this.viewer.getActorScalarsJson(actorName, scalarName);
  };

  getStatistics = function () {
    return this.viewer.getStatistics();
  };

  viewerInit = async () => {
    await this.viewer.initialize(this.canvas);
    await this.viewer.start();
    if (this.models) {
      for (const resource of this.models) {
        console.log("RISORSA:", resource.type, resource.url);
        switch (resource.type) {
          case "HEART-MODEL": {
            await this.loadHeartFile(resource.url);
            console.log("aggiunta");
            break; // Esci dal case
          }
          case "CS-MODEL": {
            await this.loadCoronaryFile(resource.url);
            console.log("aggiunta");
            break; // Esci dal case
          }
          case "LAST-ACT-TIME": {
            await this.loadNamedFile(resource.url, "LAST-ACT-TIME");
            console.log("aggiunta");
            break; // Esci dal case
          }
          case "LV-LEAD": {
            await this.loadNamedFile(resource.url, "LV_TARGET");
            console.log("aggiunta");
            break;
          }
          case "RV-LEAD": {
            await this.loadNamedFile(resource.url, "RV_TARGET");
            console.log("aggiunta");
            break;
          }
          default: {
            console.log("IGNORATA");
            break; // Gestisci eventuali altri casi
          }
        }
      }
      //Aggiunge tutti gli attori nel vtkRender...
      await this.viewer.addActorsToRender();
    }
    await this.viewer.resetView(0.0);
    await this.viewer.render();
  };

  loadHeartFile = async (file) => {
    const response = await fetch(file);
    const arrayBuffer = await response.arrayBuffer();
    // Allocate memory in WASM
    const size = arrayBuffer.byteLength;
    const ptr = Module._malloc(size);
    Module.HEAPU8.set(new Uint8Array(arrayBuffer), ptr);
    // Load the data into the viewer
    await this.viewer.loadHeartModelFromMemory(ptr, size);
    Module._free(ptr);
  };

  loadCoronaryFile = async (file) => {
    const plyResponse = await fetch(file);
    const plyArrayBuffer = await plyResponse.arrayBuffer();
    // Allocate memory for PLY
    const plySize = plyArrayBuffer.byteLength;
    const plyPtr = Module._malloc(plySize);
    Module.HEAPU8.set(new Uint8Array(plyArrayBuffer), plyPtr);
    // Load the PLY data into the viewer with a different actor ID
    await this.viewer.loadCoronaryModelFromMemory(file, plyPtr, plySize);
    Module._free(plyPtr);
  };
  loadNamedFile = async (file, name) => {
    const fileResponse = await fetch(file);
    if (file.endsWith(".txt")) {
      const text = await fileResponse.text();
      const lines = text.trim().split("\n");
      if (lines[0]) {
        const coords = lines[0].split(",");
        await this.viewer.setTargetPosition(name, coords[0], coords[1], coords[2]);
      }
    } else {
      const arrayBuffer = await fileResponse.arrayBuffer();
      // Allocate memory for PLY
      const size = arrayBuffer.byteLength;
      const plyPtr = Module._malloc(size);
      Module.HEAPU8.set(new Uint8Array(arrayBuffer), plyPtr);
      // Load the PLY data into the viewer with a different actor ID
      await this.viewer.loadVtkModelFromMemory(file, plyPtr, size, name);
      console.log("DONE LOAD VTK_SOURCE", file, size);
      Module._free(plyPtr);
    }
  };
}
window.VtkViewer = VtkViewer;
window.dispatchEvent(new Event("ModuleLoaded"));
