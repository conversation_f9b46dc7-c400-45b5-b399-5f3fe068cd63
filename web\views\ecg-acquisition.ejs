<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <%- include('components/head') %>
  </head>
  <body class="h-screen">
    <%- include('components/header') %>
    <!--<div id="main">-->
    <main class="h-full w-full overflow-y-auto">
      <div class="h-full w-full overflow-x-hidden bg-zinc-800">
        <div class="flex w-full justify-center items-center">
          <canvas id="ecgCanvas" width="1000" height="800" class="block"></canvas>
        </div>
        <div id="offset-panel" class="w-fullbg-white shadow-sm rounded px-3 m-3 z-20">
          <input
            class="w-full h-6 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-500"
            type="range"
            id="offsetSlider"
            min="0"
            max="0"
            step="500"
            value="0"
          />
        </div>
        <form id="ecg-form" method="post">
          <div class="flex w-full flex-col gap-2 pt-1 px-2 mx-2">
            <label for="resourceText" class="block font-semibold text-on-darkgray"> <%= t("resources.resourceText") %> </label>
            <input
              type="text"
              id="resourceText"
              name="resourceText"
              required
              class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
              aria-required="true"
            />
          </div>
          <div>&nbsp;</div>
          <div class="flex items-center justify-center gap-6">
            <button type="submit">
              <div id="saveEcg" class="card rounded-lg bg-[#3a3a3a] font-sans flex flex-row justify-center items-center cursor-pointer overflow-hidden">
                <p class="text-center text-3xl p-2"><i class="fa-solid fa-save text-4xl py-2"></i>&nbsp;&nbsp;<%=t('newEcg.confirm_button_text')%></p>
              </div>
            </button>
            <a href="javascript:location.reload(true);">
              <div id="discardEcg" class="card rounded-lg bg-[#3a3a3a] font-sans flex flex-row justify-center items-center cursor-pointer overflow-hidden">
                <p class="text-center text-3xl p-2"><i class="fa-solid fa-recycle text-4xl py-2"></i>&nbsp;&nbsp;<%=t('newEcg.discard_button_text')%></p>
              </div>
            </a>
          </div>
          <!-- id del file del nuovo ecg acquisito -->
          <input type="hidden" id="uid" name="uid" />
        </form>
      </div>
    </main>
    <!--</div>-->
    <div id="overlay" class="absolute inset-0 bg-gray-800 bg-opacity-70 flex flex-col items-center justify-center z-10">
      <div class="w-24 h-24 border-4 border-gray-300 border-t-darkred-light rounded-full animate-spin"></div>
      <!-- Testo di caricamento -->
      <p id="loadingText" class="text-white text-lg font-sans mt-4"><%= t('newEcg.acquiring_message') %></p>
    </div>
    <script>
      const canvas = document.getElementById("ecgCanvas");
      const offsetPanel = document.getElementById("offset-panel");
      const offsetSlider = document.getElementById("offsetSlider");
      const overlay = document.getElementById("overlay");
      const mainDiv = document.getElementById("main");
      const rect = mainDiv.getBoundingClientRect();
      overlay.style.position = "absolute";
      overlay.style.left = `${rect.left + window.scrollX}px`;
      overlay.style.top = `${rect.top + window.scrollY}px`;
      overlay.style.width = `${rect.width}px`;
      overlay.style.height = `${rect.height}px`;
    </script>
    <script type="module">
      import { EcgData, EcgDrawer } from "/js/ecg-lib.js";

      const url = "/api/ecg/acquire";
      let ecgId = "";

      const drawer = new EcgDrawer(canvas, {
        cols: 1,
        rows: 12,
        canvasWidth: 1050,
        canvasHeight: 1400,
        //leadWidthMm: 135,
        //strokeStyle: "#000000",
        leadWidthMm: 235,
        leadHeightMm: 20,
        strokeWidth: 1.5,
        //showMm: true,
        //gridStrokeStyle: '#fefefe',
        gridFineColor: "#575757",
        gridBoldColor: "#444444",
        gridStrokeStyle: "#000000",
        strokeStyle: "#00ff46",
      });

      window.onload = async () => {
        overlay.style.display = "flex";
        offsetPanel.style.display = "none";
        offsetSlider.value = 0;
        await new Promise((resolve) => requestAnimationFrame(resolve));
        ecgId = await acquireEcg(url);
        document.getElementById("uid").value = ecgId;
        const ecgUrl = "/storage/ecgs/" + ecgId;
        let readData = await loadEcgFromUrl(ecgUrl);
        try {
          if (readData) {
            drawer.setEcgData(readData);
          }
        } catch (error) {
          console.error("Error on setEcgData:", error);
        }
        offsetSlider.max = drawer.getMaxDataOffset();
        overlay.style.display = "none";
        offsetPanel.style.display = "flex";
        //drawer.setScalingFactor(1.08);
        drawer.redraw();
      };
      //In caso di resize, ridisegno tutto.
      let resizeTimeout = null;
      window.addEventListener("resize", () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          drawer.redraw();
        }, 100); // 100ms dopo l'ultimo resize
      });

      offsetSlider.addEventListener("input", function (e) {
        drawer.setOffsetIndex(parseInt(this.value));
      });

      async function acquireEcg(url) {
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
          const data = await response.json();
          return data.ecgId;
        } catch (error) {
          console.error("Failed to load ECG data:", error);
          return null;
        }
      }

      /** Carica da remoto i dati ecg e restituisce un oggetto EcgData.  */
      async function loadEcgFromUrl(url) {
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
          const csv = await response.text();
          const ecgData = new EcgData();
          ecgData.fromCsvData(csv);
          return ecgData;
        } catch (error) {
          console.error("Failed to load ECG data:", error);
          return null;
        }
      }
    </script>
  </body>
</html>
