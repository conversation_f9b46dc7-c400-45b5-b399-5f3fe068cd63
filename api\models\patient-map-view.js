import { AbstractDbViewModel } from "cruder/abstractView";
class PatientMapViewModel extends AbstractDbViewModel {
  static schema = {
    schemaName: "NICE",
    tableName: "V_MAPS",
    keys: ["ID"],
    fields: [
      "ID_PATIENT",
      "DESCRIPTION",
      "CREATION_DATE",
      "MAP_TAG",
      "STATUS",
      "RESOURCE_DESC_ECG",
      "RESOURCE_DATE_ECG",
      "RESOURCE_TAC_ECG",
      "RESOURCE_DESC_TC",
      "RESOURCE_DATE_TC",
      "RESOURCE_TAG_TC",
    ],
    mapping: {
      ID: "id",
      ID_PATIENT: "idPatient",
      DESCRIPTION: "description",
      CREATION_DATE: "creationDate",
      MAP_TAG: "mapTag",
      STATUS: "status",
      RESOURCE_DESC_ECG: "resourceDescEcg",
      RESOURCE_DATE_ECG: "resourceDateEcg",
      RESOURCE_TAC_ECG: "resourceTagEcg",
      RESOURCE_DESC_TC: "resourceDescTc",
      RESOURCE_DATE_TC: "resourceDateTc",
      RESOURCE_TAG_TC: "resourceTagTc",
    },
  };
  constructor(data = {}, dbType = "oracle") {
    super(data, dbType);
  }
}
export default PatientMapViewModel;
