const socketio = require("socket.io");
const { Server } = require('socket.io');
const logger = require("medcommon/logger");

const getRandomInt = function (max, min) {
  return Math.floor(Math.random() * max);
};

class CustomOAuthSocketIO {
  #started = false;

  /** Crea un istanza della gestione connessioni socket.io
   * 
   * @param {*} options le opzioni utilizzate in fase di start del socket.
   */
  constructor(options) {
    this.options = options || {};
    this.io = new Server();
    //this.io.on("connection", this.onConnection);
  }

  /** Questo avviene UNA sola volta, quando il CLIENT si collega a questo server.
   *  L'oggetto socket contiene tutte le informazioni riguardanti il CLIENT.
   *  Attualmente non è previsto la connessione protetta dall'access_token ma è stato
   *  riportato (e commentato) il codice per eventualmente gestire l'accesso protetto da token.
   *  Essendo ospitato da Express Server che ha la gestione del cookie-session questo è accessibile
   *  anche dal socket.
   * @param {*} socket
   */
  onConnection = async function (socket) {
    let cookieString = socket.request.headers.cookie;
    logger.info("onConnection from origin %O", cookieString);
    const transport = socket.conn.transport.name; // in most cases, "polling"
    logger.info("onConnection(transport) -> %s", transport);
    socket.conn.on("upgrade", () => {
      const upgradedTransport = socket.conn.transport.name;
      logger.info("onConnection(upgrade) -> %s", upgradedTransport);
      return "OK"; // in most cases, "websocket"
    });

    ///Traduzione del codice sottostante per i neofiti.
    ///Attacco al socket l'evento 'streamAudioChunk' emesso da uno specifico client
    ///In questo evento ricevo un chunk (un buffer di valori bytes) che rappresentano i valori
    ///short 16-bit signed two's complement integer del segnale catturato dal microfono del client 
    ///fintantoche non è stato deciso di fermare la cattura (da gestire con eventi emit separati tbd)
    ///ogni chunk ricevuto viene salvato in APPEND sul file temporaneo che ha lo stesso nome dell' id della connessione 
    ///del client con questa istanza di socket.io.
    socket.on("streamAudioChunk", (data) => {
      let byteA = data.audioData[0];
      let byteB = data.audioData[1];
      //SMRACH: STA ROBA L'HO PRESA DA QUA
      //https://stackoverflow.com/questions/38298412/convert-two-bytes-into-signed-16-bit-integer-in-javascript
      var sign = byteA & (1 << 7);
      var shortValue = (((byteA & 0xFF) << 8) | (byteB & 0xFF));
      if (sign) {
        shortValue = 0xFFFF0000 | shortValue;  // fill in most significant bits with 1's
      }
      //shortValue mi serve come debug per capire se l'algoritmo di conversione di due bytes in short (signed) è esatta.
      logger.verbose("onAudioChunk -> bytes[%s] %O:%O", data.nRead, data.firstShortValue, shortValue);
      const fs = require('fs');
      let fileId = socket.id + '.pcm';
      const z = new Uint8Array(data.audioData, 0, data.nRead);
      fs.appendFileSync(fileId, z);
    });
  };
  /** Questo metodo invia a TUTTI i clients attualmente connessi l'evento 'server-status' con
   *  alcune informazioni 'pubbliche'.  */
  #ioStatusBroadcast() {
    if (this.#started) {
      this.io.sockets.emit("server-status", { status: "alive", uptime: process.uptime() });
    }
  }

  hearthBeat = setInterval(() => {
    if (this.#started) {
      this.io.sockets.emit("heart-rate", { rate: getRandomInt(100) });
    }
  }, 1000);

  #statusTimer = setInterval(() => this.#ioStatusBroadcast(), 1000 * 30);

  //#region Metodi pubblici.
  /** Metodo di broadcast a tutti i CLIENT connessi.
   *
   * @param {*} eventName
   * @param {*} eventData
   */
  broadcastData = function (eventName, eventData = {}) {
    if (this.#started) {
      this.io.sockets.emit(eventName, eventData);
    }
  };
  start = (httpServer, middleware) => {
    this.io = new Server(httpServer, this.options);
    //Per ora non condivido la sessione vedremo un domani...
    //this.io.engine.use(middleware);
    this.io.on("connection", this.onConnection);
    this.#started = true;
  };

  stop = function () {
    this.io.close();
    this.#started = false;
  };
  //#endregion
}
module.exports = CustomOAuthSocketIO;
