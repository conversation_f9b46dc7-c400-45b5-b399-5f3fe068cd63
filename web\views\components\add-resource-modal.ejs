<style>
  ::backdrop {
    background-color: rgba(0, 0, 0, 0.7); /* or use your bg-darkgray */
  }
</style>

<dialog id="addResourceModal" class="bg-[#2c2c2c] text-white rounded-lg shadow-lg fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[60%]">
  <!-- Close button (no padding here) -->
  <div class="flex justify-end pt-4 pr-4">
    <button onclick="addResourceModal.close()" class="text-lg text-white hover:text-gray-300">
      <i class="fa fa-times"></i>
    </button>
  </div>

  <!-- Modal content with padding -->
  <div class="p-6 pt-0 flex flex-col gap-6">
    <h3 class="text-xl font-bold"><%= t('resources.addNew') %></h3>

    <!-- Resource Type Selection -->
    <div class="flex flex-col gap-2">
      <label for="resourceType" class="block font-semibold text-on-darkgray"> <%= t("resources.resourceType") %> </label>
      <div class="flex gap-4 w-full">
        <!-- ECG -->
        <button
          type="button"
          id="ecgAcquireButton"
          class="type-btn flex-1 py-4 px-2 bg-[#3a3a3a] text-white rounded-lg text-lg font-bold hover:bg-[#4a4a4a]"
          data-value="ecg"
        >
          <i class="fa-solid fa-heartbeat text-2xl mb-2"></i>
          <div><%= t("ecg") %></div>
        </button>
        <!-- CT Scan -->
        <button type="button" class="type-btn flex-1 py-4 px-2 bg-[#3a3a3a] text-white rounded-lg text-lg font-bold hover:bg-[#4a4a4a]" data-value="ct-scan">
          <i class="fa-solid fa-x-ray text-2xl mb-2"></i>
          <div><%= t("ctScan") %></div>
        </button>
      </div>
      <input type="hidden" id="resourceType" name="resourceType" value="ecg" />
    </div>
    <!--
    <div class="flex flex-col gap-2">
      <label class="block font-semibold text-on-darkgray"><%= t("resources.resourceStatus") %></label>
      <div class="flex gap-6 w-full">
        <button type="button" class="status-btn flex-1 p-2 bg-green-700 text-white rounded-lg text-lg font-bold" data-value="0">
          <%= t("badge.before") %>
        </button>
        <button type="button" class="status-btn flex-1 p-2 bg-yellow-700 text-white rounded-lg text-lg font-bold" data-value="1">
          <%= t("badge.after") %>
        </button>
        <button type="button" class="status-btn flex-1 p-2 bg-red-700 text-white rounded-lg text-lg font-bold" data-value="2">
          <%= t("badge.follow_up") %>
        </button>
      </div>
      <input type="hidden" id="resourceStatus" name="resourceStatus" value="0">
    </div>
-->
    <!-- Resource Text 
    <div class="flex flex-col gap-2">
      <label for="resourceText" class="block font-semibold text-on-darkgray">
        <%= t("resources.resourceText") %>
      </label>
      <input
        type="text"
        id="resourceText"
        name="resourceText"
        required
        class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
        aria-required="true"
      />
    </div>
-->
    <!-- <p><%= t('resources.confirmAdd') %></p> -->

    <!-- Action Buttons 
    <div class="flex gap-6">
      <button onclick="addResourceModal.close()" class="flex flex-1 px-4 py-2 bg-gray-600 rounded hover:bg-gray-700"><%= t('cancel') %></button>
      <button id="confirmAddResource" class="flex flex-1 px-4 py-2 bg-darkred rounded hover:bg-darkred-light"><%= t('add') %></button>
    </div>
    -->
  </div>
</dialog>

<script type="module">
  import { Api } from "/js/api.js";
  const addResourceModal = document.getElementById("addResourceModal");
  const addResourceButton = document.getElementById("addResourceButton");
  const confirmAddResource = document.getElementById("confirmAddResource");
  const ecgAcquireButton = document.getElementById("ecgAcquireButton");
  
  addResourceButton.addEventListener("click", () => {
    addResourceModal.showModal();
  });

  ecgAcquireButton.addEventListener("click", async () => {
    // const resourceType = document.getElementById('resourceType').value
    // const resourceStatus = document.getElementById('resourceStatus').value
    // const resourceText = document.getElementById('resourceText').value
    // const res = await Api.addResource(resourceType, resourceStatus, resourceText)
    addResourceModal.close();
    // set url to ecg-acquisition with redirectUrl to todo-page
    window.location.href = "/ecg-acquisition";
  });
</script>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const statusButtons = document.querySelectorAll(".status-btn");
    const statusInput = document.getElementById("resourceStatus");

    // Set initial selected state
    document.querySelector('.status-btn[data-value="0"]').classList.add("ring-4");

    statusButtons.forEach((button) => {
      button.addEventListener("click", () => {
        // when selected give it the appropriate color with shade 500
        // Remove selected state from all buttons
        statusButtons.forEach((btn) => btn.classList.remove("ring-4"));

        // Add selected state to clicked button
        button.classList.add("ring-4");
        updateColor(button.dataset.value);

        // Update hidden input value
        statusInput.value = button.dataset.value;
      });
    });
  });

  function updateColor(value) {
    console.log(value);

    const buttons = document.querySelectorAll(".status-btn");

    buttons.forEach((button) => {
      if (button.dataset.value === value) {
        if (value === "0") {
          button.classList.remove("bg-green-700");
          button.classList.add("bg-green-600");
        } else if (value === "1") {
          button.classList.remove("bg-yellow-700");
          button.classList.add("bg-yellow-600");
        } else if (value === "2") {
          button.classList.remove("bg-red-700");
          button.classList.add("bg-red-600");
        }
      } else if (button.dataset.value === "0") {
        button.classList.remove("bg-green-600");
        button.classList.add("bg-green-700");
      } else if (button.dataset.value === "1") {
        button.classList.remove("bg-yellow-600");
        button.classList.add("bg-yellow-700");
      } else if (button.dataset.value === "2") {
        button.classList.remove("bg-red-600");
        button.classList.add("bg-red-700");
      }
    });
  }
</script>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const typeButtons = document.querySelectorAll(".type-btn");
    const typeInput = document.getElementById("resourceType");

    // Set initial selected state
    document.querySelector('.type-btn[data-value="ecg"]').classList.add("ring-4");

    typeButtons.forEach((button) => {
      button.addEventListener("click", () => {
        // Remove selected state from all buttons
        typeButtons.forEach((btn) => btn.classList.remove("ring-4"));

        // Add selected state to clicked button
        button.classList.add("ring-4");

        // Update hidden input value
        typeInput.value = button.dataset.value;
      });
    });
  });
</script>
