<style>
  ::backdrop {
    background-color: rgba(0, 0, 0, 0.7);
    /* or use your bg-darkgray */
  }
</style>

<dialog id="addResourceModal"
  class="bg-[#2c2c2c] text-white rounded-lg shadow-lg fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[60%]">
  <!-- Close button (no padding here) -->
  <div class="flex justify-end pt-4 pr-4">
    <button onclick="addResourceModal.close()" class="text-lg text-white hover:text-gray-300">
      <i class="fa fa-times"></i>
    </button>
  </div>

  <!-- Modal content with padding -->
  <div class="p-6 pt-0 flex flex-col gap-6">
    <h3 class="text-xl font-bold">
      <%= t('resources.addNew') %>
    </h3>

    <!-- checkbox for manual upload -->
    <div class="flex items-center gap-2">
      <input type="checkbox" id="manualUpload" name="manualUpload" value="1" class="w-6 h-6 accent-darkred" />
      <label for="manualUpload" class="text-on-darkgray">Upload manually</label>
    </div>

    <div class="flex flex-col gap-2" id="resourceTextField" style="display:none">
      <label for="resourceText" class="block font-semibold text-on-darkgray">
        <%= t("resources.resourceText") %>
      </label>
      <input type="text" id="resourceText" name="resourceText" required
        class="w-full p-3 rounded border border-gray-600 bg-darkgray-light text-on-darkgray focus:outline-none focus:ring-2 focus:ring-darkred-light"
        aria-required="true" />
    </div>
    <!-- Resource Type Selection -->
    <div class="flex flex-col gap-2">
      <label for="resourceType" class="block font-semibold text-on-darkgray">
        <%= t("resources.resourceType") %>
      </label>
      <div class="flex gap-4 w-full">
        <!-- ECG -->
        <button type="button" id="ecgAcquireButton"
          class="type-btn flex-1 py-4 px-2 bg-[#3a3a3a] text-white rounded-lg text-lg font-bold hover:bg-[#4a4a4a]"
          data-value="ecg">
          <i class="fa-solid fa-heartbeat text-2xl mb-2"></i>
          <div>
            <%= t("ecg") %>
          </div>
        </button>
        <!-- CT Scan -->
        <button type="button" id="ctScanAcquireButton"
          class="type-btn flex-1 py-4 px-2 bg-[#3a3a3a] text-white rounded-lg text-lg font-bold hover:bg-[#4a4a4a]"
          data-value="ct-scan">
          <i class="fa-solid fa-x-ray text-2xl mb-2"></i>
          <div>
            <%= t("ctScan") %>
          </div>
        </button>
      </div>
      <input type="hidden" id="resourceType" name="resourceType" value="ecg" />

    </div>

    <!-- Linear Loading indicator -->
    <div class="flex flex-col items-center gap-4" id="loadingIndicator" style="display:none">
      <div class="w-full h-1 bg-gray-200 overflow-hidden">
        <div class="h-full bg-darkred-light animate-loading-bar"></div>
      </div>

      <!-- Testo di caricamento -->
      <p id="loadingText" class="text-white text-lg font-sans mt-4">
        <%= t('please_wait') %>
      </p>
    </div>
  </div>
</dialog>

<script type="module">
  import { Api } from "/js/api.js";
  const addResourceModal = document.getElementById("addResourceModal");
  const addResourceButton = document.getElementById("addResourceButton");
  const confirmAddResource = document.getElementById("confirmAddResource");
  const ecgAcquireButton = document.getElementById("ecgAcquireButton");
  const ctScanAcquireButton = document.getElementById("ctScanAcquireButton");
  const notesInput = document.getElementById("resourceText");

  addResourceButton.addEventListener("click", () => {
    addResourceModal.showModal();
  });

  ecgAcquireButton.addEventListener("click", async () => {
    // const resourceType = document.getElementById('resourceType').value
    // const resourceStatus = document.getElementById('resourceStatus').value
    // const resourceText = document.getElementById('resourceText').value
    // const res = await Api.addResource(resourceType, resourceStatus, resourceText)
    // addResourceModal.close();
    // // set url to ecg-acquisition with redirectUrl to todo-page
    // window.location.href = "/ecg-acquisition";
    if (document.getElementById("manualUpload").checked) {
      uploadFile(".csv,.ecg", "1");
    } else {
      addResourceModal.close();
      // set url to ecg-acquisition with redirectUrl to todo-page
      window.location.href = "/ecg-acquisition";
    }
  });

  ctScanAcquireButton.addEventListener("click", async () => {
    if (document.getElementById("manualUpload").checked) {
      uploadFile(".zip", "8");
    }
  });

  const uploadFile = async (fileExtension, fileDbType) => {
    event.preventDefault();
    // make the file upload form pop up
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = fileExtension;
    fileInput.click();
    fileInput.addEventListener("change", async (event) => {
      const file = event.target.files[0];

      // show the loading
      document.getElementById("loadingIndicator").style.display = "flex";
      if (file) {
        console.log("Uploading file:", file.name, fileDbType);
        // wait 2 seconds to simulate loading
        await new Promise((resolve) => setTimeout(resolve, 2000));
        document.getElementById("loadingIndicator").style.display = "none";
        const formData = new FormData();
        formData.append("file", file);
        formData.append("notes", notesInput.value.trim());
        const response = await fetch("/patients/<%= patient.id %>/resources?fileDbType=" + fileDbType, {
          method: "POST",
          body: formData,
        });
        if (response.ok) {
          const data = await response.json();
          console.log(data);
          addResourceModal.close();
          window.location.reload();
        }
      }
    });
  };
</script>

<script>

  function updateColor(value) {
    console.log(value);

    const buttons = document.querySelectorAll(".status-btn");

    buttons.forEach((button) => {
      if (button.dataset.value === value) {
        if (value === "0") {
          button.classList.remove("bg-green-700");
          button.classList.add("bg-green-600");
        } else if (value === "1") {
          button.classList.remove("bg-yellow-700");
          button.classList.add("bg-yellow-600");
        } else if (value === "2") {
          button.classList.remove("bg-red-700");
          button.classList.add("bg-red-600");
        }
      } else if (button.dataset.value === "0") {
        button.classList.remove("bg-green-600");
        button.classList.add("bg-green-700");
      } else if (button.dataset.value === "1") {
        button.classList.remove("bg-yellow-600");
        button.classList.add("bg-yellow-700");
      } else if (button.dataset.value === "2") {
        button.classList.remove("bg-red-600");
        button.classList.add("bg-red-700");
      }
    });
  }
</script>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const typeButtons = document.querySelectorAll(".type-btn");
    const typeInput = document.getElementById("resourceType");

    // show textfield only if checkbox is checked\
    const manualUploadCheckbox = document.getElementById("manualUpload");
    const resourceTextField = document.getElementById("resourceTextField");
    manualUploadCheckbox.addEventListener("change", () => {
      if (manualUploadCheckbox.checked) {
        resourceTextField.style.display = "block";
      } else {
        resourceTextField.style.display = "none";
      }
    });
  });
</script>