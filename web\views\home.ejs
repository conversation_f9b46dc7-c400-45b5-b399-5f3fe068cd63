<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <% title = "Dashboard" %>
    <% icon = "fa-solid fa-house" %>
    <%- include('components/head') %>
  </head>
  <body class="bg-darkgray text-white">

    <%- include('components/header') %>
    <main class="px-6 py-12 max-w-7xl mx-auto">
      <!-- Grid container -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-10 place-items-center text-center">
        <% buttons.forEach(function(button) { %>
        <button
          onclick="navigateTo('<%= button.link %>')"
          class="flex flex-col justify-evenly items-center bg-darkgray-light p-6 rounded-2xl hover:bg-darkred transition duration-300 w-full max-w-[500px] h-[300px] shadow-lg">
          <%- button.icon %>
          <div class="text-2xl font-semibold"><%= t(button.label) %></div>
        </button>
        <% }); %>
      </div>
    </main>
    <script>
      function navigateTo(link) {
        window.location.href = link;
      }
    </script>
  </body>
</html>
