REM INSERTING into NICE.NICE_USERS
SET DEFINE OFF;
Insert into NICE.NICE_USERS (ID,USER_NAME,FIRST_NAME,LAST_NAME,USER_PASSWORD,LAST_LOGIN,SALT) values ('1','admin',null,null,'wRWq+AudA2P5LenNd2JuWA==', DATE '2025-07-16','XyPG+fEmb4CwsjLzGHSBWQ==');
Insert into NICE.NICE_USERS (ID,USER_NAME,FIRST_NAME,LAST_NAME,USER_PASSWORD,LAST_LOGIN,SALT) values ('24','medico','Questi dati','da dove vengono?','CTqsHV8Ga24zrG01bXYWaWIQNPeRhJ3DAWSx3EQxrvYzUdP1cAn5rqCmdrgi1abaBvouv9vnzkbC93bfr/AtSA==', DATE '2025-08-27','SrR5IDMyRd6tqpUZYAMQ6g==');
REM INSERTING into NICE.PATIENTS
SET DEFINE OFF;
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('1','53b7da2d-9158-4935-8714-cdfac607b213','Case 1','A1',DATE '1975-03-15','M','31','112','before','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('2','414c3aee-a0a4-4dcb-92e6-accd8cfcae0e','Case 2','A2',DATE '2000-09-09','F','60','230','before','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('3','8ca7a3da-1b27-4a55-90ae-1f263b9ce5ab','Case 3','A3',DATE '1991-07-09','M','32','480','before','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('4','8003082a-8c0f-4933-8fb0-37d3fa14181f','Case 4','A4',DATE '1979-01-27','F','78','150','after','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('8','8','REAL','CASE',DATE '1976-11-04','M','34','200','before','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('9','9','IMPORT','FIRST',DATE '1969-09-06','F','55','122','before','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('21','21','SEGMENT','T1',DATE '1991-08-08','M','33','55','before','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('23','23','SEGMENT','T2',DATE '1968-10-12','M','66','88','before','N');
Insert into NICE.PATIENTS (ID,CODE,FIRST_NAME,LAST_NAME,BIRTH_DATE,GENDER,EF,ESV,STATUS_TAG,DELETED) values ('22','22','STE','ZDELETE',DATE '1968-10-12','M','66','88','before','Y');

REM INSERTING into NICE.RESOURCE_LK
SET DEFINE OFF;
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('1','ECG');
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('2','CT-SCAN');
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('3','HEART-MODEL');
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('4','CS-MODEL');
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('8','DICOM-ZIP');
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('6','RV-LEAD');
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('7','LV-LEAD');
Insert into NICE.RESOURCE_LK (ID,DESCRIPTION) values ('5','LAST-ACT-TIME');
REM INSERTING into NICE.RESOURCES
SET DEFINE OFF;
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('1','1','1','1','3',DATE '2025-10-16','Review after treatment','before','1','case_PSS_map before.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('2','1','1','1','1',DATE '2025-08-01','Sinus rhythm, 70 bpm, right bundle branch block.','before','2','case_PSS_LBBB_own.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('3','1','1','1','4',DATE '2027-08-20','Outpatient follow-up','before','3','case_PSS_veins.obj');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('4','1','1','1','2',DATE '2020-12-25','CT scan of the abdomen with contrast.','before','4','1.2.276.0.7230010.3.1.2.**********.5316.**********.1324');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('5','2','1','1','3',DATE '2025-11-29','Follow-up required','before','1','case_GOV_map_own.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('6','2','1','1','2',DATE '2019-08-31','CT scan of the abdomen with contrast.','before','2','1.2.276.0.7230010.3.1.2.**********.5772.**********.994');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('7','2','1','1','4',DATE '2026-08-17','Review after treatment','after','3','GOV_veins.obj');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('8','3','1','1','1',DATE '2018-06-08','Sinus rhythm, 60 bpm, LBBB.','before','1','AIA_own before_10_complex.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('9','3','1','1','2',DATE '2018-06-10','CT scan of the abdomen with contrast.','before','2','1.2.276.0.7230010.3.1.2.**********.12044.**********.6115');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('24','1','1','1','3',DATE '2025-10-16','Implantation','after','1','case_PSS_map before.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('14','4','1','1','1',DATE '2025-01-04','QRS 123 ms','after','3','case D LOT_RVLV0ms_QRS123ms_2247ms-2370ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('12','4','1','1','1',DATE '2025-01-04','Sinus rhythm, 70 bpm, left bundle branch block.','before','1','case D_own_before.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('13','4','1','1','2',DATE '2026-07-05','CT scan of the abdomen with contrast.','before','2','********.1107.5.1.4.76270.30000024120606461010200000162');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('22','2','1','1','1', DATE '2019-08-31','Biventricular paced rhythm after CRT, 70 bpm','after','7','GOV_after_CRT_biV0ms(LV tip)_349ms-482ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('23','3','1','1','1', DATE '2025-08-11','Atrial-sensed, ventricular-paced rhythm (DDD), 70 bpm; wide QRS with LBBB morphology consistent with right-ventricular pacing.','after','3','AIA_biV0ms after_267ms-368ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('25','1','1','1','6', DATE '2025-08-08','RV Point','after','6','RV tip.vtk');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('26','1','1','1','7', DATE '2025-08-08','LV Point','after','7','LV tip.vtk');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('15','4','1','1','1', DATE '2025-01-04','QRS 149 ms','after','4','case D_A-LVonly_QRS149ms_349ms-498ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('16','4','1','1','1', DATE '2025-01-04','QRS 138 ms','after','5','case D_LBBAP_QRS138ms_1217ms-1355ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('17','4','1','1','1', DATE '2025-01-04','QRS 126 ms','after','6','case D_LOT_LVRV10ms_QRS126ms_1249ms-1375ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('18','4','1','1','1', DATE '2025-01-04','QRS 127 ms','after','7','case D_LOT_LVRV20ms_QRS127ms_1338ms-1465ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('19','4','1','1','1', DATE '2025-01-04','QRS 131 ms','after','8','case D_LOT_LVRV30ms_QRS131ms_1269ms-1400ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('27','4','1','1','1', DATE '2025-01-04','QRS 133 ms','after','9','case D_LOT_LVRV40ms_QRS133ms_1232ms-1365ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('28','4','1','1','1', DATE '2025-01-04','QRS 145 ms','after','10','case D_LOT_LVRV50ms_QRS145ms_1240ms-1385ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('29','4','1','1','1', DATE '2025-01-04','QRS 122 ms','after','11','case D_LOT_RVLV10ms_QRS122ms_1323ms-1445ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('30','4','1','1','1',  DATE '2025-01-04','QRS 115 ms','after','12','case D_LOT_RVLV20ms_QRS115ms_1325ms-1440ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('31','4','1','1','1',  DATE '2025-01-04','QRS 113 ms','after','13','case D_LOT_RVLV30ms_final QRS113ms_1403ms-1516ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('32','4','1','1','1',  DATE '2025-01-04','QRS 115 ms','after','14','case D_LOT_RVLV40ms_QRS115ms_1285ms-1400ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('33','4','1','1','1',  DATE '2025-01-04','QRS 121 ms','after','15','case D_LOT_RVLV50ms_QRS121ms_1209ms-1330ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('116','1','1','1','8', DATE '2010-10-10','Dicom Zip files','after','8',null);
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('117','1','1','1','8', DATE '2010-10-10','Dicom Zip files','after','8','CT raw_PSS.zip');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('118','21','1','1','1',DATE '2025-08-14','Ecg Study','before','1','case_PSS_LBBB_own.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('119','9','1','1','1', DATE '2025-08-14','Ecg Study','before','1','rtam_9_default_f7_ecg_original.csv');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('120','9','1','1','8', DATE '2010-10-10','Dicom Zip files','after','8','rtam_9_default_f0_full_study_dicom_zip.zip');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('121','9','1','1','4', DATE '2025-08-21','Veins','before','1','rtam_9_default_f14_veins_ply.ply');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('134','23','1','1','1',DATE '2025-08-22','ecg','before','1','GOV_own before_10_complex.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('135','23','1','1','8',DATE '2025-08-22','ctscan','before','1','CT raw_GOV.zip');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('136','23','1','1','3',DATE '2025-08-22','f26_activation_map','before','1','rtam_23_default_f26_activation_map.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('137','23','1','1','4',DATE '2025-08-22','f14_veins_ply','before','1','rtam_23_default_f14_veins_ply.ply');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('138','23','1','1','5',DATE '2025-08-22','f31_tp_coords','before','1','rtam_23_default_f31_tp_coords.txt');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('145','21','1','1','4',DATE '2025-08-26','f14_veins_ply','before','1','rtam_21_20_f14_veins_ply.ply');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('146','21','1','1','4',DATE '2025-08-26','f14_veins_ply','before','1','rtam_21_rtam_20_f14_veins_ply.ply');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('147','21','1','1','3',DATE '2025-08-26','f26_activation_map','before','1','rtam_21_rtam_20_f26_activation_map.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('148','21','1','1','5',DATE '2025-08-26','f31_tp_coords','before','1','rtam_21_rtam_20_f31_tp_coords.txt');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('79','4','1','1','3',  DATE '2026-07-09','Routine examination','follow_up','4','f26_activation_map own before CRT.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('80','4','1','1','4',  DATE '2025-01-09','Outpatient follow-up','follow_up','5','veins.obj');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('83','2','1','1','1',  DATE '2025-08-08','Sinus rhythm, 70 bpm, right bundle branch block.','before','4','GOV_own before_10_complex.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('149','1','1','1','4', DATE '2025-08-27','f14_veins_ply','before','1','rtam_1_rtam_21_f14_veins_ply.ply');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('150','1','1','1','3', DATE '2025-08-27','f26_activation_map','before','1','rtam_1_rtam_21_f26_activation_map.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('151','1','1','1','5', DATE '2025-08-27','f31_tp_coords','before','1','rtam_1_rtam_21_f31_tp_coords.txt');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('84','8','1','1','1',  DATE '2025-08-14',null,'before','1','default.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('85','8','1','1','3',  DATE '2025-08-14','test','before','2','eam_model.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('115','3','1','1','3', DATE '2025-08-14','test','before','2','AIA_map own.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('122','9','1','1','3', DATE '2025-08-21','ACT_MAP','before','1','rtam_9_default_f26_activation_map.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('130','9','1','1','5', DATE '2025-08-21','f31_tp_coords','before','1','rtam_9_default_f31_tp_coords.txt');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('131','21','1','1','4',DATE '2025-08-22','f14_veins_ply','before','1','rtam_21_default_f14_veins_ply.ply');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('114','8','1','1','1', DATE '2025-08-14','gyyy','before','114','18f5c77c-ea00-43de-81e0-c288aa793d3d.csv');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('132','21','1','1','3',DATE '2025-08-22','f26_activation_map','before','1','rtam_21_default_f26_activation_map.vtu');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('133','21','1','1','5',DATE '2025-08-22','f31_tp_coords','before','1','rtam_21_default_f31_tp_coords.txt');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('34','4','1','1','5',  DATE '2025-01-01','LBBAP marker.vtk','before','4','LBBAP marker.vtk');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('35','2','1','1','5',  DATE '2024-07-03','late activation zone_marker.vtk','before','5','late activation zone_marker.vtk');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('144','22','1','1','1',DATE '2025-08-26','ecg complex caso 1','before','1','055_AIA_11-06-2018_10_complex.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('153','3','1','1','8', DATE '2025-07-28','Dicom Zip files','before','1','CT raw_AIA.zip');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('152','2','1','1','8', DATE '2025-07-28','Dicom Zip files','before','1','CT raw_GOV.zip');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('154','4','1','1','8', DATE '2025-07-28','Dicom Zip files','before','1','CT before.zip');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('20','1','1','1','1',  DATE '2023-10-01','Biventricular paced rhythm after CRT, 70 bpm.','after','5','PSS_biV 0ms_375ms-500ms.ecg');
Insert into NICE.RESOURCES (ID,ID_PATIENT,ID_USER_CREATE,ID_USER_UPDATE,ID_RESOURCE_TYPE,RESOURCE_DATE,RESOURCE_NOTES,RESOURCE_TAG,RESOURCE_ORDER,UNIQUE_ID) values ('21','1','1','1','5',  DATE '2025-08-08','Late activaction point','before','6','late activation zone_marker.vtk');

REM INSERTING into NICE.MAPS
SET DEFINE OFF;
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('1','Initial Assessments','1',DATE '2025-07-16','0','before','1','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('2','MAP Case 2','1',DATE '2025-07-16','0','before','2','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('3','MAP Case 3','1',DATE '2025-07-16','0','after','3','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('4','MAP Case 4','1',DATE '2025-07-16','0','follow_up','4','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('5','Implantation','1',DATE '2025-07-16','0','after','1','2');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('10','Import Data','1',null,'0','before','9','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('27','sample','1',null,'-1',null,'22','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('40','prima simulazione','1',DATE '2025-08-26','-1','before','22','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('6','Initial MAP','1',DATE '2025-08-14','0','before','8','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('9','First segmentation','1',null,'0','before','21','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('43','prima simulazione 4','1',DATE '2025-08-26','0','before','21','1');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('41','prima simulazione','1',DATE '2025-08-26','-1','before','22','1');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('42','prima simulazione 3','1',DATE '2025-08-26','-1','before','22','1');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('16','Secondo segmentation stage','1',null,'0',null,'23','0');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('44','Simulation notes','1',DATE '2025-08-27','0','before','1','1');
Insert into NICE.MAPS (ID,DESCRIPTION,ID_USER_CREATE,CREATION_DATE,STATUS,MAP_TAG,ID_PATIENT,TYPE) values ('25','prima simulazione','1',null,'-1',null,'22','1');

REM INSERTING into NICE.MAP_RESOURCES
SET DEFINE OFF;
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('1','3');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('1','2');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('2','5');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('2','7');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('4','79');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('4','80');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('2','83');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','1');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','2');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','3');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('1','116');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('10','119');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('10','120');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('16','136');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('16','137');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('16','138');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('43','145');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('43','146');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('43','147');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('43','148');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('1','21');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('6','84');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('6','85');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('3','115');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','2');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','116');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('6','2');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('6','116');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('9','118');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('9','117');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('10','122');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('10','130');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('9','131');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('44','149');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('44','150');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('44','151');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('9','132');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('9','133');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','25');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','26');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('5','21');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('4','34');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('2','35');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('16','134');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('16','135');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('2','152');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('3','153');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('4','154');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('4','12');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('4','13');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('10','121');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('3','8');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('3','9');
Insert into NICE.MAP_RESOURCES (ID_MAP,ID_RESOURCE) values ('1','1');

REM INSERTING into NICE.SIMULATIONS
SET DEFINE OFF;
Insert into NICE.SIMULATIONS (ID,DESCRIPTION,ID_MAP,ID_MAP_RESULT,STATUS,DATA,CREATED_AT,ID_PATIENT) values ('17','prima simulazione','9','40','-1',null,DATE '2025-08-26','22');
Insert into NICE.SIMULATIONS (ID,DESCRIPTION,ID_MAP,ID_MAP_RESULT,STATUS,DATA,CREATED_AT,ID_PATIENT) values ('20','prima simulazione 4','9','43','0','{"RV":{"axis":"53.814546,-93.806183,-540.899727","ventricular":"0.79,2.890265,1,3"},"LV":{"axis":"30.672665,-177.537761,-495.983396","ventricular":"0.61,0.341477,1,0"}}',DATE '2025-08-26','21');
Insert into NICE.SIMULATIONS (ID,DESCRIPTION,ID_MAP,ID_MAP_RESULT,STATUS,DATA,CREATED_AT,ID_PATIENT) values ('21','Simulation notes','1','44','0','{"RV":{"axis":"15.739414,-150.309331,-468.031558","ventricular":"0.98,0.689618,1,0"},"LV":{"axis":"55.777601,-149.880028,-485.875137","ventricular":"0.67,1.099557,1,3"}}',DATE '2025-08-27','1');
Insert into NICE.SIMULATIONS (ID,DESCRIPTION,ID_MAP,ID_MAP_RESULT,STATUS,DATA,CREATED_AT,ID_PATIENT) values ('18','prima simulazione','9','41','-1','{"RV":{"axis":"54.340611,-93.729858,-539.791332","ventricular":"0.77,2.890265,1,3"},"LV":{"axis":"28.215859,-178.63647,-496.950762","ventricular":"0.62,0.250417,1,0"}}',DATE '2025-08-26','22');
Insert into NICE.SIMULATIONS (ID,DESCRIPTION,ID_MAP,ID_MAP_RESULT,STATUS,DATA,CREATED_AT,ID_PATIENT) values ('19','prima simulazione 3','9','42','-1','{"RV":{"axis":"53.827192,-93.811014,-540.888354","ventricular":"0.79,2.890265,1,3"},"LV":{"axis":"27.01236,-177.775279,-495.930527","ventricular":"0.64,0.250417,1,0"}}',DATE '2025-08-26','22');

REM INSERTING into NICE.PROCESSING_JOBS
SET DEFINE OFF;
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('4','21','9','segmentation',DATE '2025-08-22','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('5','21','9','create_actmap_stage',DATE '2025-08-22','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('6','21','9','actmap_stage',DATE '2025-08-22','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('1','8','6','create_actmap_stage',DATE '2025-08-21','1','quinto',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('7','23','16','create_segmentation',DATE '2025-08-22','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('8','23','16','segmentation',DATE '2025-08-22','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('9','23','16','create_actmap_stage',DATE '2025-08-22','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('10','23','16','actmap_stage',DATE '2025-08-22','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('2','9','10','segmentation',DATE '2025-08-21','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('3','9','10','actmap_stage',DATE '2025-08-21','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('30','21','9','create_simulation',DATE '2025-08-26','1','20','{"idSim":20,"ppol":"0.79;2.890265;1;3;0.61;0.341477;1;0"}');
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('31','21','43','simulation',DATE '2025-08-26','1','20','{"idSim":20,"ppol":"0.79;2.890265;1;3;0.61;0.341477;1;0"}');
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('11','23','17','sample',DATE '2025-08-25','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('12','23','17','segmentation',DATE '2025-08-25','1','default',null);
Insert into NICE.PROCESSING_JOBS (ID,ID_PATIENT,ID_MAP,STAGE,CREATED_AT,JOB_STATUS,ID_ECG,JOB_DATA) values ('13','23','17','create_actmap_stage',DATE '2025-08-25','1','default',null);