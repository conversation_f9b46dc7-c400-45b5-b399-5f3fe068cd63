import { AbstractDbCrudModel } from "cruder/abstractCrud";
import <PERSON><PERSON> from "joi";

export default class MapModel extends AbstractDbCrudModel {
  static schema = {
    schemaName: "NICE",
    tableName: "MAPS",
    keys: ["ID"],
    fields: ["DESCRIPTION", "ID_PATIENT", "ID_USER_CREATE", "CREATION_DATE", "STATUS", "MAP_TAG", "TYPE"],
    mapping: {
      ID: "id",
      DESCRIPTION: "description",
      ID_PATIENT: "idPatient",
      ID_USER_CREATE: "idUserCreate",
      STATUS: "status",
      CREATION_DATE: "creationDate",
      MAP_TAG: "mapTag",
      TYPE: "type", // 0: activactions maps, 1: simulations, 2: implantation
    },
    joi: Joi.object({}),
  };
  constructor(data = {}, dbType = "oracle") {
    super(data, dbType);
  }
}