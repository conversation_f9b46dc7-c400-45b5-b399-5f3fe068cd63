<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <%- include('components/head') %>
  </head>
  <body>
    <div id="ecgDiv"></div>
    <canvas id="ecgCanvas" width="1070" height="700" class="block"></canvas>
    <div id="offset-panel" class="bg-white shadow-sm rounded px-2 m-2 z-20 w-full hidden">
      <input
        class="w-full h-6 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-500 px-2"
        type="range"
        id="offsetSlider"
        min="0"
        max="0"
        step="500"
        value="0"
      />
    </div>
    <div id="overlay" class="absolute inset-0 bg-gray-800 bg-opacity-70 flex flex-col items-center justify-center z-10">
      <div class="w-24 h-24 border-4 border-gray-300 border-t-darkred-light rounded-full animate-spin"></div>
      <!-- Testo di caricamento -->
      <p id="loadingText" class="text-white text-lg font-sans mt-4"><%= t('ecg.loading.message') %></p>
    </div>
    <script>
      const canvas = document.getElementById("ecgCanvas");
      const offsetPanel = document.getElementById("offset-panel");
      const offsetSlider = document.getElementById("offsetSlider");
      const overlay = document.getElementById("overlay");

      const rect = canvas.getBoundingClientRect();
      overlay.style.position = "absolute";
      overlay.style.left = `${rect.left + window.scrollX}px`;
      overlay.style.top = `${rect.top + window.scrollY}px`;
      overlay.style.width = `${rect.width}px`;
      overlay.style.height = `${rect.height}px`;

      /*offsetPanel.style.position = "absolute";
      offsetPanel.style.left = `${(rect.width - 1000) / 2}px`;
      offsetPanel.style.width = `1000px`;
      offsetPanel.style.top = `${rect.height - 32}px`;*/
    </script>
    <script src="/js/lil-gui@0.20"></script>
    <script>
      var GUI = lil.GUI;
      const ecgDiv = document.getElementById("ecgDiv");
      const gui = new GUI({ container: ecgDiv, title: "Settings" });
      obj = {
        gridFineColor: "#111111",
        gridBoldColor: "#444444",
        gridStrokeStyle: "#000000",
        strokeStyle: "#00ff46",
      };
      gui.addColor(obj, "gridBoldColor").onChange((value) => {
        window.drawer.setBoldColor(value);
      });
      gui.addColor(obj, "gridFineColor").onChange((value) => {
        window.drawer.setFineColor(value);
      });
      gui.addColor(obj, "gridStrokeStyle").onChange((value) => {
        window.drawer.setGridStrokeStyle(value);
      });
      gui.addColor(obj, "strokeStyle").onChange((value) => {
        window.drawer.setStrokeStyle(value);
      });
      gui.open(false);
    </script>
    <script type="module">
      import { EcgData, EcgDrawer } from "/js/ecg-lib.js";

      const drawer = new EcgDrawer(canvas, {
        canvasWidth: 1070,
        canvasHeight: 700,
        leadWidthMm: 135,
        leadHeightMm: 20,
        gridFineColor: "#111111",
        gridBoldColor: "#444444",
        gridStrokeStyle: "#000000",
        strokeStyle: "#00ff46",
      });
      window.drawer = drawer;
      const ecgUrl = "<%=ecgUrl%>";

      window.onload = async () => {
        overlay.style.display = "flex";
        offsetPanel.style.display = "none";
        offsetSlider.value = 0;
        await new Promise((resolve) => requestAnimationFrame(resolve));
        console.log("ECG URL:", ecgUrl);
        let readData = await loadEcgFromUrl(ecgUrl);
        if (ecgUrl.endsWith(".ecg")) {
          drawer.setSamplesPerSecond(1000);
        }
        if (readData) {
          drawer.setEcgData(readData);
          offsetSlider.max = drawer.getMaxDataOffset();
        }
        overlay.style.display = "none";
        offsetPanel.style.display = "flex";
        //drawer.setScalingFactor(1.08);
        drawer.redraw();
      };
      //In caso di resize, ridisegno tutto.
      let resizeTimeout = null;
      window.addEventListener("resize", () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          drawer.redraw();
        }, 100); // 100ms dopo l'ultimo resize
      });

      offsetSlider.addEventListener("input", function (e) {
        drawer.setOffsetIndex(parseInt(this.value));
      });

      /** Carica da remoto i dati ecg e restituisce un oggetto EcgData.  */
      async function loadEcgFromUrl(url) {
        //await new Promise((resolve) => setTimeout(resolve, 500));
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
          const data = await response.text();
          const ecgData = new EcgData();
          if (url.endsWith(".csv")) {
            ecgData.fromCsvData(data);
          }
          if (url.endsWith(".ecg")) {
            ecgData.fromEcgData(data);
          }
          return ecgData;
        } catch (error) {
          console.error("Failed to load ECG data:", error);
          return null;
        }
      }
    </script>
  </body>
</html>
