{"name": "cruder", "version": "1.0.0", "description": "libreria di crud multi db", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON>", "license": "ISC", "private": true, "files": ["/db-adapter.js", "/db-oracle.js", "/db-maria.js", "/db-postgres.js", "/db-mocker.js", "/db-modules.js", "/abstract-crud.js", "/abstract-view.js", "/package.json", "README.md"], "exports": {"./dbOracle": "./db-oracle.js", "./dbOracle.js": "./db-oracle.js", "./dbMaria": "./db-maria.js", "./dbMaria.js": "./db-maria.js", "./dbPostgres": "./db-postgres.js", "./dbPostgres.js": "./db-postgres.js", "./dbMocker": "./db-mocker.js", "./dbMocker.js": "./db-mocker.js", "./abstractCrud": "./abstract-crud.js", "./abstractCrud.js": "./abstract-crud.js", "./abstractView": "./abstract-view.js", "./abstractView.js": "./abstract-view.js", "./package.json": "./package.json"}, "dependencies": {"dotenv": "^14.3.2", "path": "^0.12.7", "oracledb": "^5.4.0", "mariadb": "^3.4.2", "pg": "^8.16.2", "joi": "^17.13.3"}}