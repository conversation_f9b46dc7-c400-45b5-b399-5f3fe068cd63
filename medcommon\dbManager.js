"use strict";
const dbManager = require("./oracle");

/**
 * Questa classe dovrebbe essere una 'specie' di interfaccia indipendente dal motore di database usato
 */

const pool = createPool();

function createPool() {
  dbManager.createPool();
}

async function pingDB() {
  return dbManager.pingDB();
}

/**
 * Recupera una connessione Oracle dal pool di connessioni.
 * @returns Oggetto connessione.
 */
async function getDBConnection(userInfo = {}) {
  return dbManager.getDBConnection(userInfo);
}
/** Esegue la query su db fornita in statement
 * 
 * @param {string} La query o stored procedure da richiamare. 
 * @param {array} L'array di parametri da associare alla query 
 * @param {ExtraOptions} Opzioni aggiuntive da passare al gestore db. 
 * @returns 
 */
async function dbExecute(statement, bindParams, extraOptions) {
  return dbManager.dbExecute(statement, bindParams || [], extraOptions || {});
}
/** Esegue la query e ritorna la riga letta da db 
 * @param {string} La query o stored procedure da richiamare. 
 * @param {array} L'array di parametri da associare alla query 
 * @param {ExtraOptions} Opzioni aggiuntive da passare al gestore db. 
 * @returns undefined se la query non ritorna 1 riga, altrimenti la riga letta.
 */
async function dbSingleRow(statement, bindParams, extraOptions) {
  let toRet;
  let result = await dbExecute(statement, bindParams || [], extraOptions || {});
  if (result && result.rows && result.rows.length == 1) {
    toRet = result.rows[0];
  }
  return toRet;
}
/** Quando faccio update / delete il db ritorna rowsAffected.
 * 
 * @param {*} statement 
 * @param {*} bindParams 
 * @param {*} extraOptions 
 * @returns (rowsAffected == 1)
 */
async function dbRowsAffected(statement, bindParams, extraOptions) {
  let result = await dbExecute(statement, bindParams || [], extraOptions || {});
  return (result.rowsAffected == 1);
}

/**
 * Effettua una connessione/query di prova per verificare la connessione DB.
 * @param {*} req La richiesta
 * @param {*} res La risposta dal db (data&ora del server)
 * @param {*} next Callback in caso positivo.
 */
async function verifyDBConnection(req, res, next) {
  return dbManager.verifyDBConnection(req, res, next);
}

async function setPackagesOracle(conn, oraUser) {
  return dbManager.setPackagesOracle(conn, oraUser);
}

/** 
 * Ogni DB ha il suo modo (e libreria di creazione parametri) qui faccio l'astrazione 
 * Es. options = {
 *   dir: direzione (IN | OUT | INOUT)
 *   val: valore del parametro,
 *   type: tipo del parametro stringa minuscola (string | date | number | buffer | clob | blob | cursor | buffer).
 *   maxSize: dimensione del campo (! default == 200)
 * } 
*/
const createParameter = (options) => dbManager.createParameter(options);

/** Ogni DB ha il suo modo (e libreria di creazione parametri) qui faccio l'astrazione */
function createParameterIN(options) {
  //return dbManager.createParameterIN(options);
}
/** Ogni DB ha il suo modo (e libreria di creazione parametri) qui faccio l'astrazione */
function createParameterOUT(options) {
  //return dbManager.createParameterOUT(options);
}
/** Ogni DB ha il suo modo (e libreria di creazione parametri) qui faccio l'astrazione */
function createParameterINOUT(options) {
  //return dbManager.createParameterINOUT(options);
}
function formatReadData(data) {
  return data;
}
function formatNewData(data) {
  return data;
}
function formatUpdateData(data) {
  return data;
}
function formatDeleteData(data) {
  return data;
}

//TODO: Mettere gi� la logica di questi.
async function createTransaction() {}
async function commitTransation() {}
async function closeConnection() {}

module.exports = {
  getDBConnection,
  dbExecute,
  verifyDBConnection,
  //createParameterIN,
  //createParameterOUT,
  //createParameterINOUT,
  createParameter,
  setPackagesOracle,
  dbSingleRow,
  dbRowsAffected,
  pingDB
};
