"use strict";
const mariadb = require("mariadb");

const config = require("./config");
const logger = require("./logger");

const pool = createPool();

function createPool() {
  return mariadb.createPool(config.mariaConfig);
}

/**
 * Recupera una connessione Oracle dal pool di connessioni.
 * @returns Oggetto connessione.
 */
async function getDBConnection() {
  let conn = await pool.getConnection();
  logger.debug("mariadb.getDbConnection -> from connPool: %O", conn.info.threadId);
  return conn;
}

/**
 * Esegue la chiamata sul database e restituisce l'oggetto result della connessione,
 * altrimenti genera un errore che DEVE essere intercettato/gestito da fuori..
 * @param statement La query in formato stringa da eseguire.
 * @param binds Parametri da bindare alla query.
 * @param extraOptions  Struttura json con dati aggiuntivi da passare.
 * Es. extraOptions = {
 *   connection: LaConnessioneCheVoglio, <- Utile per le transazioni.
 *   userInfos: {  }, -> se non è vuoto allora imposta il package utente.
 *   doCommit: true | false -> se si verifica un errore fà il rollback altrimenti fà il commit.
 * }
 */
async function dbExecute(statement, bindParams, extraOptions) {
  let conn;
  let result = [];
  let opts = {};
  let closeConnection = true;
  //opts.outFormat = oracledb.OUT_FORMAT_OBJECT;
  try {
    if (extraOptions.connection) {
      conn = extraOptions.connection;
      closeConnection = false;
    } else {
      conn = await getDBConnection();
    }
    if (conn) {
      result = await conn.query(statement, bindParams);
      if (result && result.insertId === 0n) {
        result.insertId = "0";
      }
      //await conn.commit();
    } else {
      logger.debug("Connessione da Pool NON riuscita!");
      throw "Connessione mariadb non riuscita.";
    }
  } catch (err) {
    logger.error("Error on conn.execute. %s", err.message);
    throw err;
  } finally {
    if (conn && closeConnection) {
      // conn assignment worked, need to close
      try {
        await conn.end();
      } catch (err) {
        logger.error("Error on conn.close(). %s", err.message);
      }
    }
  }
  return result;
}
/**
 * Effettua una connessione/query di prova per verificare la connessione DB.
 * @param {*} req La richiesta
 * @param {*} res La risposta dal db (data&ora del server)
 */
async function verifyDBConnection(req, res) {
  // #swagger.tags = ['Oracle']
  // #swagger.summary = 'Verifica la connessione con il DB Oracle'
  // #swagger.description = 'Endpoint per verificare la connessione al db.'
  var respCode = 500;
  var respJson = {};
  try {
    let result = await dbExecute("SELECT NOW()", [], {});
    if (result) {
      respCode = 200;
      respJson = result;
    }
  } catch (error) {
    logger.error("Error on verifyDbConnection. %s", error.message);
    respJson = { message: error.message, error: error };
  } finally {
    res.status(respCode).send(respJson);
  }
}
module.exports = {
  createPool,
  getDBConnection,
  dbExecute,
  verifyDBConnection,
};
//Codice testato e funzionante.
async function testTransaction() {
  var connection = await getDBConnection();
  try {
    let query = "INSERT INTO utenti (username, userpassword) VALUES(?,?)";
    await connection.beginTransaction();
    let params = ["utente1", "password"];
    await dbExecute(query, params, { connection: connection });
    params[0] = "utente2";
    await dbExecute(query, params, { connection: connection });
    params[0] = "utente3";
    await dbExecute(query, params, { connection: connection });
    params[0] = "utente4";
    await dbExecute(query, params, { connection: connection });
    await connection.commit();
  } catch (error) {
    logger.error("Error:%O", error);
    await connection.rollback();
  }
}
