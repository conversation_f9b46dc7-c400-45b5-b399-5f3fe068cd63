<style>
::backdrop {
  background-color: rgba(0, 0, 0, 0.7); /* or use your bg-darkgray */
}
</style>

<dialog id="deleteModal" class="bg-[#2c2c2c] text-white rounded-lg shadow-lg fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px]">
  <div>
    <h2 class="text-xl font-semibold mb-4 text-center p-4"><%= t("patient.delete") %></h2>
    <p class="mb-4 text-center"><%= t("patient.deleteConfirmation") %></p>

    <!-- Action Buttons -->
    <div class="flex gap-6 p-4 text-center">
      <button type="button"
              onclick="deleteModal.close()"
              class="w-full px-4 py-2 bg-gray-600 rounded hover:bg-gray-700">
        <%= t('cancel') %>
      </button>

      <form method="POST" action="/patients/<%= patient.id %>/delete" class="w-full">
        <button type="submit"
                class="w-full px-4 py-2 rounded bg-red-700 hover:bg-red-600">
          <%= t('delete') %>
        </button>
      </form>
    </div>
  </div>
</dialog>
<script>
  const deleteModal = document.getElementById('deleteModal')
</script>
