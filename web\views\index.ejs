<!DOCTYPE html>
<html lang="<%= lang %>" dir="<%= lang === 'ar' ? 'rtl' : 'ltr' %>">
  <head>
    <%- include('components/head') %>
  </head>
  <body>
    <%- include('components/header') %>
    <main>
      <div class="p-4">
        <div class="flex items-center w-[180px] h-[180px] m-auto border border-yellow-500 rounded text-center">
          <div class="w-full"><a href="/patients/search">RICERCA PAZIENTI</a></div>
        </div>
      </div>
      <!--<div style="overflow-y:auto;height: 100%;" class="bg-darkgray">
      <table style="width: 75%; height:100%; margin: auto;">
        <tr style="text-align: center; vertical-align: middle;justify-content: center; align-items: center;">
          <td style="text-align: center; vertical-align: middle; justify-content: center; align-items: center;">
            <div style="height: 360px; width: 400px;margin:auto;" class="border border-yellow-500 rounded">
              <a href="/patients/search">RICERCA PAZIENTI</a>
            </div>
          </td>
          <td style="text-align: center; vertical-align: middle; justify-content: center; align-items: center;">
            <div style="height: 360px; width: 400px;margin:auto;" class="border border-yellow-500 rounded">
              contenuto 2
            </div>
          </td>
          <td style="text-align: center; vertical-align: middle; justify-content: center; align-items: center;">
            <div style="height: 360px; width: 400px;margin:auto;" class="border border-yellow-500 rounded">
              contenuto 3
            </div>
          </td>
        </tr>
        <tr style="text-align: center; vertical-align: middle;justify-content: center; align-items: center;">
          <td style="text-align: center; vertical-align: middle; justify-content: center; align-items: center;">
            <div class="h-[360px] w-[360px] m-auto border border-yellow-500 rounded">
              contenuto 4
            </div>
          </td><td style="text-align: center; vertical-align: middle; justify-content: center; align-items: center;">
            <div style="height: 360px; width: 400px;margin:auto;" class="border border-yellow-500 rounded">
              contenuto 5
            </div>
          </td>
          <td style="text-align: center; vertical-align: middle; justify-content: center; align-items: center;">
            <div style="height: 360px; width: 400px;margin:auto;" class="bg-red border border-yellow-500 rounded">
              contenuto 6
            </div>
          </td>
        </tr>
      </table>
      </div>-->
      <!--<div class="h-full flex items-center justify-center border border-yellow-500 p-8">
        <div class="grid grid-cols-2 gap-4 border border-yellow-500 p-4">
          <div class="w-64 h-64 bg-yellow-300 flex items-center justify-center border border-red-600">Box 1</div>
          <div class="w-64 h-64 bg-yellow-300 flex items-center justify-center">Box 2</div>
          <div class="max-w-sm rounded overflow-hidden shadow-lg bg-red border border-yellow-200">
            <img class="w-8 h-8 object-cover" src="/back.png" alt="Immagine della card">
            <div class="px-6 py-4">
              <h2 class="font-bold text-xl mb-2">Titolo della Card</h2>
              <p class="text-gray-700 text-base">
                Questo è il contenuto della card. Puoi aggiungere testo, pulsanti, immagini e altro ancora.
              </p>
            </div>
            <div class="px-6 pt-4 pb-2">
              <button class="bg-blue-500 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded">
                Azione
              </button>
            </div>
          </div>
          <div class="w-64 h-64 bg-yellow-300 flex items-center justify-center" style="background-color: blueviolet; border:solid 1px; border-color: aqua; width: 200px; height: 164px;">Box 4</div>
        </div>
      </div>-->
    </main>
  </body>
</html>
