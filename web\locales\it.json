{"dateFormat": "DD/MM/YYYY", "pickDateFormat": "d/m/Y", "dateTimeFormat": "DD/MM/YYYY HH:mm", "dateTimeSecondsFormat": "DD/MM/YYYY HH:mm:ss", "firstName": "Nome", "lastName": "Cognome", "birthDate": "Data di Nascita", "selectBirthDate": "Seleziona Data di Nascita", "age": "Età", "statusTag": "Stato", "dropDownSelect": "Seleziona...", "gender": "<PERSON><PERSON>", "male": "<PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "ef": "EF (%)", "efRangeHint": "EF deve essere compreso tra 10 e 80", "esv": "ESV (mL)", "esvRangeHint": "ESV deve essere compreso tra 10 e 1000", "ctScan": "TAC", "ecg": "ECG", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Elimina", "confirm": "Conferma", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saveChanges": "<PERSON><PERSON>", "please_wait": "Attendere prego...", "back": "Indietro", "about": "Informazioni", "logout_user": "Logout Utente", "lock_workstation": "Blocca Postazione", "shutdown": "Spegni Postazione", "noResultsFound": "<PERSON><PERSON><PERSON>", "postLoginWarning": {"heading": "N.I.C.E v%s", "message": "Questa versione di N.I.C.E. è destinata solo a scopi dimostrativi.\n\nN.I.C.E. è un Dispositivo Medico progettato per essere utilizzato da professionisti sanitari, in particolare cardiologi e cardiologhi elettrofisiologici.\n\nPer ulteriori informazioni, contattare: <EMAIL>\n\nSelezionando '%s' confermi di aver letto e compreso le informazioni fornite.", "confirm": "Ho letto e compreso le informazioni fornite"}, "breadcrumbs": {"patient": {"search": "Cerca Paziente", "selected": "Scheda Paziente", "ecg": "ECG", "ctscan": "TAC", "new": "Nuovo Paziente", "activationMap": "Mappa di attivazione"}}, "login": {"heading": "N.I.C.E.", "username_label": "Utente", "password_label": "Password", "button": "Accedi", "error": "Credenziali non valide. Riprova.", "empty_error": "Questo campo è obbligatorio."}, "home": {"heading": "Home", "patientList": "Lista Pazienti", "recentActivities": "Attività Recenti", "settings": "Impostazioni"}, "patientList": {"heading": "Lista Pazienti", "search": "Cerca Paziente", "patientName": "<PERSON><PERSON>", "patientCode": "<PERSON><PERSON>", "noResultsFound": "<PERSON><PERSON><PERSON>", "searchError": "Errore durante la ricerca", "searchInProgress": "Ricerca in corso"}, "patient": {"heading": "Scheda Paziente", "search": "Cerca...", "name": "<PERSON><PERSON>", "code": "<PERSON><PERSON>", "addNew": "Aggiungi Nuovo Paziente", "edit": "Modifica Paziente", "delete": "Elimina Record Paziente", "addConfirmation": "Sei sicuro di voler aggiungere un nuovo paziente?", "deleteConfirmation": "Sei sicuro di voler eliminare questo paziente?"}, "badge": {"filterStatus": "Filtra Stato", "all": "<PERSON><PERSON>", "before": "Pre-intervento", "after": "Post-intervento", "follow_up": "Follow up"}, "resources": {"heading": "Risorse", "addNew": "Acquisisci Nuova Risorsa", "resourceType": "Tipo di Risorsa", "resourceStatus": "Stato della Risorsa", "resourceText": "<PERSON><PERSON> Risorsa", "confirmAdd": "Conferma l'acquisizione della nuova risorsa"}, "ecgMaps": {"heading": "ECG Maps", "addNew": "Crea Nuova Mappa", "selectInitialData": "Seleziona ECG e TAC", "confirmAdd": "Crea Nuova Simulazione"}, "activationMaps": {"heading": "Mappe di Attivazione", "addNew": "Crea Nuova Mappa", "selectInitialData": "Seleziona ECG e TAC", "confirmAdd": "Crea Nuova Simulazione"}, "viewer": {"heading": "Mappa di Attivazione", "view_ap": "VA", "view_pa": "VP", "view_ll": "VL"}, "notFound": {"heading": "Errore", "message": "Questa funzionalità non è attualmente disponibile.", "noPermission": "L'accesso a questa funzionalità è stato negato."}, "ecgPage": {"heading": "ECG"}, "dicom": {"heading": "Dicom Viewer"}, "newEcg": {"acquiring_message": "Acquisizione ECG in corso...", "confirm_button_text": "Salva ECG", "discard_button_text": "Scarta ECG"}, "simulation": {"new": "Nuova Simulazione"}}